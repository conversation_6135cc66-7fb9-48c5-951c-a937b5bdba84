{"__meta": {"id": "X9ed2510f5c10318110cab1568f4a11f0", "datetime": "2025-06-07 22:17:44", "utime": **********.705068, "method": "GET", "uri": "/login", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749334663.121355, "end": **********.705107, "duration": 1.583751916885376, "duration_str": "1.58s", "measures": [{"label": "Booting", "start": 1749334663.121355, "relative_start": 0, "end": **********.431158, "relative_end": **********.431158, "duration": 1.3098030090332031, "duration_str": "1.31s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.431188, "relative_start": 1.30983304977417, "end": **********.705111, "relative_end": 4.0531158447265625e-06, "duration": 0.2739229202270508, "duration_str": "274ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45978184, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 4, "templates": [{"name": "1x auth.login", "param_count": null, "params": [], "start": **********.588023, "type": "blade", "hash": "bladeC:\\laragon\\www\\to\\newo\\resources\\views/auth/login.blade.phpauth.login", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fresources%2Fviews%2Fauth%2Flogin.blade.php&line=1", "ajax": false, "filename": "login.blade.php", "line": "?"}, "render_count": 1, "name_original": "auth.login"}, {"name": "1x layouts.auth", "param_count": null, "params": [], "start": **********.608767, "type": "blade", "hash": "bladeC:\\laragon\\www\\to\\newo\\resources\\views/layouts/auth.blade.phplayouts.auth", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fresources%2Fviews%2Flayouts%2Fauth.blade.php&line=1", "ajax": false, "filename": "auth.blade.php", "line": "?"}, "render_count": 1, "name_original": "layouts.auth"}, {"name": "1x landingpage::layouts.buttons", "param_count": null, "params": [], "start": **********.675454, "type": "blade", "hash": "bladeC:\\laragon\\www\\to\\newo\\Modules/LandingPage\\Resources/views/layouts/buttons.blade.phplandingpage::layouts.buttons", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2FModules%2FLandingPage%2FResources%2Fviews%2Flayouts%2Fbuttons.blade.php&line=1", "ajax": false, "filename": "buttons.blade.php", "line": "?"}, "render_count": 1, "name_original": "landingpage::layouts.buttons"}, {"name": "1x layouts.cookie_consent", "param_count": null, "params": [], "start": **********.685467, "type": "blade", "hash": "bladeC:\\laragon\\www\\to\\newo\\resources\\views/layouts/cookie_consent.blade.phplayouts.cookie_consent", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fresources%2Fviews%2Flayouts%2Fcookie_consent.blade.php&line=1", "ajax": false, "filename": "cookie_consent.blade.php", "line": "?"}, "render_count": 1, "name_original": "layouts.cookie_consent"}]}, "route": {"uri": "GET login/{lang?}", "middleware": "web, guest", "controller": "App\\Http\\Controllers\\Auth\\AuthenticatedSessionController@showLoginForm", "namespace": null, "prefix": "", "where": [], "as": "login", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FAuth%2FAuthenticatedSessionController.php&line=344\" onclick=\"\">app/Http/Controllers/Auth/AuthenticatedSessionController.php:344-359</a>"}, "queries": {"nb_statements": 9, "nb_failed_statements": 0, "accumulated_duration": 0.026000000000000002, "accumulated_duration_str": "26ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 46}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 78}, {"index": 15, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 555}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/Auth/AuthenticatedSessionController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\Auth\\AuthenticatedSessionController.php", "line": 348}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.5204098, "duration": 0.005690000000000001, "duration_str": "5.69ms", "memory": 0, "memory_str": null, "filename": "Utility.php:46", "source": "app/Models/Utility.php:46", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUtility.php&line=46", "ajax": false, "filename": "Utility.php", "line": "46"}, "connection": "ty", "start_percent": 0, "width_percent": 21.885}, {"sql": "select table_name as `name`, (data_length + index_length) as `size`, table_comment as `comment`, engine as `engine`, table_collation as `collation` from information_schema.tables where table_schema = 'ty' and table_type in ('BASE TABLE', 'SYSTEM VERSIONED') order by table_name", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 537}, {"index": 14, "namespace": null, "name": "app/Http/Controllers/Auth/AuthenticatedSessionController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\Auth\\AuthenticatedSessionController.php", "line": 351}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.532529, "duration": 0.01082, "duration_str": "10.82ms", "memory": 0, "memory_str": null, "filename": "Utility.php:537", "source": "app/Models/Utility.php:537", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUtility.php&line=537", "ajax": false, "filename": "Utility.php", "line": "537"}, "connection": "ty", "start_percent": 21.885, "width_percent": 41.615}, {"sql": "select `full_name`, `code` from `languages`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 17, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 543}, {"index": 18, "namespace": null, "name": "app/Http/Controllers/Auth/AuthenticatedSessionController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\Auth\\AuthenticatedSessionController.php", "line": 351}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.5530272, "duration": 0.0015400000000000001, "duration_str": "1.54ms", "memory": 0, "memory_str": null, "filename": "Utility.php:543", "source": "app/Models/Utility.php:543", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUtility.php&line=543", "ajax": false, "filename": "Utility.php", "line": "543"}, "connection": "ty", "start_percent": 63.5, "width_percent": 5.923}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 4716}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 4650}, {"index": 15, "namespace": "view", "name": "auth.login", "file": "C:\\laragon\\www\\to\\newo\\resources\\views/auth/login.blade.php", "line": 4}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.5900202, "duration": 0.00134, "duration_str": "1.34ms", "memory": 0, "memory_str": null, "filename": "Utility.php:4716", "source": "app/Models/Utility.php:4716", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUtility.php&line=4716", "ajax": false, "filename": "Utility.php", "line": "4716"}, "connection": "ty", "start_percent": 69.423, "width_percent": 5.154}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 4716}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 4650}, {"index": 15, "namespace": "view", "name": "layouts.auth", "file": "C:\\laragon\\www\\to\\newo\\resources\\views/layouts/auth.blade.php", "line": 10}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.610965, "duration": 0.0011200000000000001, "duration_str": "1.12ms", "memory": 0, "memory_str": null, "filename": "Utility.php:4716", "source": "app/Models/Utility.php:4716", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUtility.php&line=4716", "ajax": false, "filename": "Utility.php", "line": "4716"}, "connection": "ty", "start_percent": 74.577, "width_percent": 4.308}, {"sql": "select * from `users` where `type` = 'super admin' limit 1", "type": "query", "params": [], "bindings": ["super admin"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 4081}, {"index": 17, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 4123}, {"index": 18, "namespace": "view", "name": "layouts.auth", "file": "C:\\laragon\\www\\to\\newo\\resources\\views/layouts/auth.blade.php", "line": 22}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.6421332, "duration": 0.00202, "duration_str": "2.02ms", "memory": 0, "memory_str": null, "filename": "Utility.php:4081", "source": "app/Models/Utility.php:4081", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUtility.php&line=4081", "ajax": false, "filename": "Utility.php", "line": "4081"}, "connection": "ty", "start_percent": 78.885, "width_percent": 7.769}, {"sql": "select `value`, `name` from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 4082}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 4123}, {"index": 15, "namespace": "view", "name": "layouts.auth", "file": "C:\\laragon\\www\\to\\newo\\resources\\views/layouts/auth.blade.php", "line": 22}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.6534848, "duration": 0.00128, "duration_str": "1.28ms", "memory": 0, "memory_str": null, "filename": "Utility.php:4082", "source": "app/Models/Utility.php:4082", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUtility.php&line=4082", "ajax": false, "filename": "Utility.php", "line": "4082"}, "connection": "ty", "start_percent": 86.654, "width_percent": 4.923}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 4716}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 4650}, {"index": 15, "namespace": "view", "name": "layouts.auth", "file": "C:\\laragon\\www\\to\\newo\\resources\\views/layouts/auth.blade.php", "line": 39}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.661723, "duration": 0.0011, "duration_str": "1.1ms", "memory": 0, "memory_str": null, "filename": "Utility.php:4716", "source": "app/Models/Utility.php:4716", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUtility.php&line=4716", "ajax": false, "filename": "Utility.php", "line": "4716"}, "connection": "ty", "start_percent": 91.577, "width_percent": 4.231}, {"sql": "select * from `landing_page_settings`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 18, "namespace": null, "name": "Modules/LandingPage/Entities/LandingPageSetting.php", "file": "C:\\laragon\\www\\to\\newo\\Modules\\LandingPage\\Entities\\LandingPageSetting.php", "line": 27}, {"index": 19, "namespace": "view", "name": "landingpage::layouts.buttons", "file": "C:\\laragon\\www\\to\\newo\\Modules/LandingPage\\Resources/views/layouts/buttons.blade.php", "line": 2}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 73}], "start": **********.678572, "duration": 0.00109, "duration_str": "1.09ms", "memory": 0, "memory_str": null, "filename": "LandingPageSetting.php:27", "source": "Modules/LandingPage/Entities/LandingPageSetting.php:27", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2FModules%2FLandingPage%2FEntities%2FLandingPageSetting.php&line=27", "ajax": false, "filename": "LandingPageSetting.php", "line": "27"}, "connection": "ty", "start_percent": 95.808, "width_percent": 4.192}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "aeg571Q3ThqeNMfiIAc14ULZrTrG6DjnVuualNlJ", "_previous": "array:1 [\n  \"url\" => \"http://localhost/login\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/login", "status_code": "<pre class=sf-dump id=sf-dump-1173787051 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1173787051\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-575681054 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-575681054\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1479717269 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1479717269\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1283095793 data-indent-pad=\"  \"><span class=sf-dump-note>array:15</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">none</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1993 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=a091af98-270d-406e-ab7e-33a6e610d6e2414686; _clck=nups8l%7C2%7Cfwk%7C0%7C1960; _clsk=ij6lzq%7C1749334646194%7C1%7C1%7Ck.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IkxEdjFiZ1d6RVhDRy9aV09YbVFJT0E9PSIsInZhbHVlIjoic3FqaUorVzlvTG5FTWNBQXB6NkxJK3dWUzVTV1JSV3VTRjZYMms2REZoZUJTQUlZT284TXU0THpPTEtzRUcrTlA4Zm92WG9rUGxkcThTNEJEeHZTZExiZ2M2REFoM2NCTnN1QXQrZlZaVVlmK2laUkRnZzhpZG5aZnJRWTFkVWtHRTlTdEkxNDd2Z1BUcEp1K0VoRGpEWm4yV1Q0ZDZIaEJ5OG5IR1VKM21ZWElqUHNGUVRnSEdkM0MvUFBkaUsrMDNSMVgwMldXREdHYUhURHh2djZHcXdXNUxwUm56UEUzUjRkamZ3VDRKYnNrajZYRVR5c0VTSTl0NFd2UFdaelNIaWxoaU8xbWJpWUc5WVJSWFFDMUpIalBwL2lISW1rc3Mydk9sRFNvNVM0TEFJcDJmVXBYalJFdzFjdEs2YVduektMUFhXSWN5L084cC9FRnZEaEdUU0xJODFRR2YvMTlXOEQ2R1BtZkZONmw4M0xJdm04UkRYanVNY2xkTzJ4LzlYbzVHM0FiMDRDY21TRGZveG1xM2VFN2RPTEZGQ29yamsybmNjbi8zVkp1SGlaeTh1WTJyRlFEbUJYZU9XczdpZmlvQXNBMnVCbnllNXM1QnRSTUFDeFUwS0RMZ2xBaXVlTlhTeVZNeWxQUHVlT2tUWmhTN2sxZXRxc040ek4iLCJtYWMiOiIyN2NhMmE5ZTQ5NGJmNGMyODQ3ZDQyNjQ0MTMzNTVhMTkwMTc0NWEwYTk4M2E0MTYzNDExMjE3ZjlhYThlNmQwIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6ImVXeFB0TkFVUHlvN1A1Sk94Y2VsV3c9PSIsInZhbHVlIjoiVXdBaGEySlpya0xKRldHN2RicE01cC9KaE12aVRkNjhSemloL1d2a2J6dDhXVWlpWWhjSnA1RWxsbDhDZGNFN0hCdWY2SjhDZHZaWHBBT1ZnZDVrVUVjaU0xRGk3dWZaRVdvUEJBTy90U3graVA1TjJlUUxCekNiT3daaWtxWG5uaG1sMnB1c200aU1VTDRDNjk3ZlExd1JvZGlSMVluMy8rMnlNQm5nQ1V1NkZYLzM2aDY2cjJhWnBZMUlUK1JrcUluSW94WlV1ZzRzenNWODE4SXNYM2tjZWZ5MVQ0YzY5d3d0d0VDVTZQWFNEaFExdURGNERjWDdiUGtKWHNVTDBWY0t3N2FTV2czQjROWXMyZ2Y4OGNzUk5ERTRNcHBLdWtGb2k5MDYrUWZmZFAyVlA3SnVoT2lsWEF4azc4RWJQeC9Ca2JKSGc2RVhGOHI2am5wWHQ2R3NNbXJEOVI1MlpYYTM4TnowbTgveDE4eFNlZ25jSERteVZrWGNVT1dKRHF4SkRLd1Rxb2h4TDhDRTVtTzVHOFFsS0ZMQnNPdmthWks5VTRlSllvU3VPWDBkMHJEMW94cEZjQVVLTHpianBjdExndnZWSndkK0NQMjF5OEhObThDT3Ira0hES2tpZXB0Tm1nSkt2OFY5TWFYejRROVdNZ2FuQldiVGZQWDgiLCJtYWMiOiJkYTE2NDkzZDFjNDE5NzkzN2MxMWJlNDcxYzJjMGVkNTlhMjIwNTA1MDIxZTdmZmJiMTBiMjllNGIxYjRlOTlmIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1283095793\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-642521415 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">aeg571Q3ThqeNMfiIAc14ULZrTrG6DjnVuualNlJ</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">JGbRg3rX9CZC3OYjDIftJuBr6aHnx3C6fHbYY9Rw</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-642521415\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1554406178 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 07 Jun 2025 22:17:44 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6InZydGdVVnZ1WlNDQzQ3Rmo5S3Myc1E9PSIsInZhbHVlIjoiZGNQNEhLVlJYYWN1MHFCV2J5VVkvZDlSUGsrL3JuM2g3ZWtOWDlrbHcyQzJkOHBIT3MvMlR6cjgwRUM5ZllBY2JtOUtPQWxyVDZscWF4dCt1MXlCKzMrOXVtcVgvODlCbHE2Q0JoZGVoY1JPR1hWQ21BZ2syMU9EbmV4OWVhUVhvYXV6b0drUk01UTByV01tQWprWVZZNC96TEs2K2F0YVFaM0xZTXR2dWI5V0pkN0lzT2lPa2RRb1h5ck1KZWZmTWRWSjBJbXprakQwQ0I3azJsM2VRQk5jOTdZS2IwMVFTVUFtVHc4V2Y4Rjk4VHJTelR3TjNvZWIvVUVPT2s5Z0Vhd1Z5ZDBUM1c3Q1Y5NDBlZk9zWkNmM0pXNUZBTnFnQ0R5R3dheG04dXEzTUhyOUVsRndZcTN3ZDdZMlBMNkNWdnVYYmRZZWRkbldSM3VQK3U4S1BuYUhLNzdGVE4yYTZhYi9mUzh6cmF5dmtpZkNBd0NhNm51aWIxckF2cWU0bzJqQUJDTGNwc2E5WEhrNHZxck5XQlVNRmlrMm1VNjJ0UXlkUzNVcE1BMjE2em5DcmF1ak5uaWNZV2FWUzNZenJEaHhjSGJRWUtzRUlFVGlQUElKSVlQK3RCWEJoZm1HUlZZbTJPNkh5Z2E5NUVaaWhNdG9nZFBURkhxSU5TVjAiLCJtYWMiOiJiMjRhZDJhNGE5MmViYWNlZTJjMTgzMTgyMzFlNmRjNDNmYTEyOGY3MTYzNTRmMDdiZTA2NDc4OWM5Mzg3ZDJmIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 00:17:44 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6IjB6T1NuaWdnc1JBejl1SWZzQ0JrZmc9PSIsInZhbHVlIjoiZmpIV1hYMjc3ZjNKYVozd2srL1ljQ0ZjRVVwMHd3UEdTMnI3WVkwYm05N2FZcS9SbEROYnE2OGV1Rm1tTmZwSUZtaTlzaG1lMGRKcGduUjRYK3RQSng2ekpBN1I2RFBaZlI2emV4TzIwdVU0UElRdkNYQkkzeUdrOXZBRVE2dGd1SFoweWtVTWI5U1RXWllwNm0wVHdIWjVValhRRTJmYUl2L3E2dEJCNmZmdWcrYnpUM2FwSFRWR2RYWit1SFEyTnUwbHd5ejZkZDNaR3BRUHlHL1l0TnpMV1NGSjZzSzl0SnNUdkNtVXhEeFZ2WmgvTmpzMmlxTitLS01aeU42VVE0dHA5WFdjNDF4aXovNjVkVXpKc0ZwbFArb0lxTjMzeUhWS2FmRHRodkZRUUdac0txVEphQ0JBZ3cxR0h2TTZEVUlNbngvQ0hJOC9nMm0yaFdPMEhIUUU0Vi9zWDAyWmN0aFVVZG5QaHJKTFJSY0orbE82TGVMdmdhTjFDRUdhenFOK2xXT01RSWxHMTZWblZZSjRORGRidlV1eEk5YVd4SGJ4MTZUaG9BMEdGNEJibk81V3Zwek83b2tOL08wUkdJQ3p5Uk5nVFQ4TGZveFFGNjc2QzZ1clpUcFZQNzdQYzZqMEpWSzRIczErZy9pNTVSOEhIMGkyL1VHek8raVoiLCJtYWMiOiI4NzgxOGM4OTg5NzQ1YzMyOWI0NzZmZjg3NzVjYmQzNjVlZmQyMGM2MWUxM2NmY2ExMDU1Y2ZhYWM4NWM2NDQ1IiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 00:17:44 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6InZydGdVVnZ1WlNDQzQ3Rmo5S3Myc1E9PSIsInZhbHVlIjoiZGNQNEhLVlJYYWN1MHFCV2J5VVkvZDlSUGsrL3JuM2g3ZWtOWDlrbHcyQzJkOHBIT3MvMlR6cjgwRUM5ZllBY2JtOUtPQWxyVDZscWF4dCt1MXlCKzMrOXVtcVgvODlCbHE2Q0JoZGVoY1JPR1hWQ21BZ2syMU9EbmV4OWVhUVhvYXV6b0drUk01UTByV01tQWprWVZZNC96TEs2K2F0YVFaM0xZTXR2dWI5V0pkN0lzT2lPa2RRb1h5ck1KZWZmTWRWSjBJbXprakQwQ0I3azJsM2VRQk5jOTdZS2IwMVFTVUFtVHc4V2Y4Rjk4VHJTelR3TjNvZWIvVUVPT2s5Z0Vhd1Z5ZDBUM1c3Q1Y5NDBlZk9zWkNmM0pXNUZBTnFnQ0R5R3dheG04dXEzTUhyOUVsRndZcTN3ZDdZMlBMNkNWdnVYYmRZZWRkbldSM3VQK3U4S1BuYUhLNzdGVE4yYTZhYi9mUzh6cmF5dmtpZkNBd0NhNm51aWIxckF2cWU0bzJqQUJDTGNwc2E5WEhrNHZxck5XQlVNRmlrMm1VNjJ0UXlkUzNVcE1BMjE2em5DcmF1ak5uaWNZV2FWUzNZenJEaHhjSGJRWUtzRUlFVGlQUElKSVlQK3RCWEJoZm1HUlZZbTJPNkh5Z2E5NUVaaWhNdG9nZFBURkhxSU5TVjAiLCJtYWMiOiJiMjRhZDJhNGE5MmViYWNlZTJjMTgzMTgyMzFlNmRjNDNmYTEyOGY3MTYzNTRmMDdiZTA2NDc4OWM5Mzg3ZDJmIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 00:17:44 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6IjB6T1NuaWdnc1JBejl1SWZzQ0JrZmc9PSIsInZhbHVlIjoiZmpIV1hYMjc3ZjNKYVozd2srL1ljQ0ZjRVVwMHd3UEdTMnI3WVkwYm05N2FZcS9SbEROYnE2OGV1Rm1tTmZwSUZtaTlzaG1lMGRKcGduUjRYK3RQSng2ekpBN1I2RFBaZlI2emV4TzIwdVU0UElRdkNYQkkzeUdrOXZBRVE2dGd1SFoweWtVTWI5U1RXWllwNm0wVHdIWjVValhRRTJmYUl2L3E2dEJCNmZmdWcrYnpUM2FwSFRWR2RYWit1SFEyTnUwbHd5ejZkZDNaR3BRUHlHL1l0TnpMV1NGSjZzSzl0SnNUdkNtVXhEeFZ2WmgvTmpzMmlxTitLS01aeU42VVE0dHA5WFdjNDF4aXovNjVkVXpKc0ZwbFArb0lxTjMzeUhWS2FmRHRodkZRUUdac0txVEphQ0JBZ3cxR0h2TTZEVUlNbngvQ0hJOC9nMm0yaFdPMEhIUUU0Vi9zWDAyWmN0aFVVZG5QaHJKTFJSY0orbE82TGVMdmdhTjFDRUdhenFOK2xXT01RSWxHMTZWblZZSjRORGRidlV1eEk5YVd4SGJ4MTZUaG9BMEdGNEJibk81V3Zwek83b2tOL08wUkdJQ3p5Uk5nVFQ4TGZveFFGNjc2QzZ1clpUcFZQNzdQYzZqMEpWSzRIczErZy9pNTVSOEhIMGkyL1VHek8raVoiLCJtYWMiOiI4NzgxOGM4OTg5NzQ1YzMyOWI0NzZmZjg3NzVjYmQzNjVlZmQyMGM2MWUxM2NmY2ExMDU1Y2ZhYWM4NWM2NDQ1IiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 00:17:44 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1554406178\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-26777807 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">aeg571Q3ThqeNMfiIAc14ULZrTrG6DjnVuualNlJ</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/login</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-26777807\", {\"maxDepth\":0})</script>\n"}}