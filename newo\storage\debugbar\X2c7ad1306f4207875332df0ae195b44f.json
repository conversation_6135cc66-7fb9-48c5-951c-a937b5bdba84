{"__meta": {"id": "X2c7ad1306f4207875332df0ae195b44f", "datetime": "2025-06-07 22:38:03", "utime": **********.339516, "method": "POST", "uri": "/chats/getContacts", "ip": "127.0.0.1"}, "php": {"version": "8.3.16", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749335882.224397, "end": **********.339556, "duration": 1.115159034729004, "duration_str": "1.12s", "measures": [{"label": "Booting", "start": 1749335882.224397, "relative_start": 0, "end": **********.176871, "relative_end": **********.176871, "duration": 0.9524741172790527, "duration_str": "952ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.176901, "relative_start": 0.9525041580200195, "end": **********.339561, "relative_end": 5.0067901611328125e-06, "duration": 0.1626598834991455, "duration_str": "163ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45582968, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/getContacts", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getContacts", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "contacts.get", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=333\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:333-390</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.03093, "accumulated_duration_str": "30.93ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 16 limit 1", "type": "query", "params": [], "bindings": ["16"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.2524219, "duration": 0.0287, "duration_str": "28.7ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 92.79}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.301997, "duration": 0.00113, "duration_str": "1.13ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 92.79, "width_percent": 3.653}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (16) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.317715, "duration": 0.0011, "duration_str": "1.1ms", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 96.444, "width_percent": 3.556}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "iD3eOztlFQTk9Wu93brk2PH5kNLllwdWSLzbHLjf", "_previous": "array:1 [\n  \"url\" => \"http://localhost:8000/account-dashboard\"\n]", "_flash": "array:2 [\n  \"old\" => array:1 [\n    0 => \"error\"\n  ]\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "16", "error": "Access Denied. You do not have permission to access this feature.", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/getContacts", "status_code": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">iD3eOztlFQTk9Wu93brk2PH5kNLllwdWSLzbHLjf</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1596342365 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">http://localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"39 characters\">http://localhost:8000/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1938 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=17ceeen%7C2%7Cfwk%7C0%7C1960; XSRF-TOKEN=eyJpdiI6ImFRd09WL2pFYW92RDZaSGsxNWI4WlE9PSIsInZhbHVlIjoiTmZNSHRkR1BIbFVPMi96dGVubmo4ZzE4WVFMSkJwWjhBMEtBOFBHMVNVZlVBSG1MT2x4N1loRlYycE5SN0dFd3MvWFIyblFXOHB6cFhoelYxT0t6eVRKanpIdkFLYi9kNmRiZjdRNVZzd0I0cGx6RjBmKzhpMDZHREF1aGNBWWkzRFc4WUFocFF2cVlsb0VZbFFXZkpDT3Fhb3FNTE14dDBZZ3l3ZnlsRWI4REtSREg5YlZrTEl4bW01YnpyYVA0eWdWUmlpL29NNXp0Q2wwUDMyc3NmUHYyRDlrNWpnb29DM1VGeUZRRzdvUllOVVhza1lyQUpreGhTUm1Hb3NDYnA4d3p0cjhMc0tKTmE0bE0wOVkyd096MnJoMk4vUE0zRXZHWVhjYWhoamRwSTZHQUdMaS9ab0RPbW0yMzNPVGtYS3M0OVpFTFdwYVl6U2UvWDJsdDRHTE5DdCtIMGE4Qm1HUENVU1ZWbFpFYWM2SHcwY01jbE50NVAwdUFiU3hpcitmL0JYOVBEMkZkNWJzbmxOcVMyYzcrM0FzWlhlLzNuQ2g1OVJXOEVOYWVsZ0VtZUJHRFNXSDd2bnhWdDFkN041TThQaDM4U3VqU1R4eWJNay94WitSamphRjJsOVNjOG5ac0ttUEFhNkp2Z0lYNUIxQWtFNTgwbzh3WWdvRi8iLCJtYWMiOiJkNzI1ZWNmZTkzN2M1YWFhMWJiNDY4OGRkZGFhOWYwZWNiNmMwN2FhZTc5ZmMxYmEwMjgzNTFhZThkZGI4MzQ2IiwidGFnIjoiIn0%3D; test_session=eyJpdiI6ImdLc0RydXRmaVpreGF4WGhpYlpZU1E9PSIsInZhbHVlIjoiaGZOSTNpM2RhTGJGTTdvMWpld2ZiQzBBbVhYZ2RSSkNHK1duRDBObElzSFdXaWdyVHVKaEkvUXp4b0VjcDg2R09EMjJldTFmbGtFdWsxWHg4ZWMwUGg5dk5YNkdOb0lBMUt4ZFYrai9Xa1BTVHlWeVFmYW9VV2tTQWVncjNoZ2lvQ2JZcys1VW1QQVNrdUlCTUdXMVVOSFJVeFFOM2hDc09XcGdxWkJtWENVQy9LSG13Nkk1Z3FuT3NudExvb1dDRmppSWFYMjNPSU5XZ1M0S2tPOTdSeGpQYTJLM25aNnNncUpMRzRrK3pzUmFxb3F3eTRjbmtJWko0TWlPaldzZm5QVWlrek5ld2xpWmhvRENPS1NlUlJyUzJ5ODRMZUJrY3hSZ2NXU0ZnK0w0RExDQk5nQ1JQbi94bjVkU2N5b2U1VG1CUlM4RGwwQUlqc0VNVXdha0twakpKbGFTSDZJcjBmZlQ4S2ZsRTFjUjVjNUxpUWxlRTNiK3J0UHljV3BTY1dQZmowUEJuYTA2dEhONFUvalZlZVY2elNweDZScUN5TTZwYURtUjMxdTFtM2dwWE00dnVxaEUvR2tKZGplenBhU29MckNid0srSU9hVG1XbC9YRlA0Z0xpS0ozRFdzUjlGV25VRjZrYUtmTENXVFR5U2pEUXdjeGtNd0l6N28iLCJtYWMiOiJkZGU3MTU0NzI1NGM3NjY3NjBjN2NmOGRhYjY3M2U1MWI1MmI2OWNkYjRkN2VjNGZjZDk5NmUxMGZjMzg5ZWRjIiwidGFnIjoiIn0%3D; _clsk=1yftgw7%7C1749335881083%7C6%7C1%7Ck.clarity.ms%2Fcollect</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1596342365\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-223932035 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">iD3eOztlFQTk9Wu93brk2PH5kNLllwdWSLzbHLjf</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">atMfj5qpj5gKx4OQYPP5PbQzRbuF7iB8X4zv4aOY</span>\"\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-223932035\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-2053546179 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 07 Jun 2025 22:38:03 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"39 characters\">http://localhost:8000/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6ImordUVhMXZlRHgrb3BBOGJNVS9vMVE9PSIsInZhbHVlIjoiV0tqSmphYmdNaWdycGFPend4NXc5MnNoMEQ4cDhXUTNvRmN3cE5WampCVWZxL1Jmd0p1eVFhV2hVcHpJMVdsV0ttQk1rMHgrNm11OGJWM0loRW82QUgzRCtZTVJLTllFejNUQjhRMTY2dm1CQ1Y1WlRCUFg0eTByNlEwczBZT0lPdVZIUklvSkIxVjNmVW96WDRvQ21qRkNaODdXVEh2QW55MjdNVFNEcTlRNUZ5bVpOeGxqeU5pSVhzV2NJc0hta0JSdkROUGxOT3B4QStnTGZyL0ZmWDMzVUQ3NHZ1bGgrMTFpRWdUekt6UmUvS09qUjdvaDJxTlAzYk9MZFljbTg1LzM4L1NYNkxSNlNVVFMxVlF6UVMwOWJHNDdDYkRJSWZ3T2ZrVDlIdWRUdXlHM1VBMWVMd3Jyck8vZFR6dEc2eG9iQ01tTkdRT3VkTkxmVnVJejdNbmxqNmdKeStBTXIvWnVVSldobUlBeGVsbVFZb2c4TUVEUVdTYndDYWJ0NVhmRk8vdDZCa0xzUC9wdjkrNVlvUk9NM3cvMnlUdG5IRWdmMkwvZU1FOGkvSVJBMkMreExoTVNFTGtOMFFHUHRST0VJUWgwQ2ZjOEVOdFpuMXBjU3RjbzNBNUovSFFQYzF6bW1mWHBBdlE2cjlUYkM2TjVwVCtNZDdFQVdxL28iLCJtYWMiOiJhMGQ5Y2Q2Y2JlNDI4Y2Q3MDhiN2M1ZjU0NjAwYzljNjYyMDBkNzZkY2EwMmQ0MzcyOTFhZDcwODQ2NTgyN2IxIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 00:38:03 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6InlQb0xrek92RVoxdkdxejJZQjVybHc9PSIsInZhbHVlIjoiL1h5Y1k2R0hqMDVndmN5SFR0MTRjaGFmbzhEVGxpVWRmL0RlR2ExL0E1WjRiRmUzc2I3VnNtTm5xUTU4R2V3NXRBUDczTHVOSGtaeFJJU21FTzNXeS91Rk1XOVdNY2NmeHJWZkdPZnhXZnNRd1VaU2ErMWdNZmVFSnd5Vi85aEI5QzBiekdxanhpZENaOFR5RUFGRjJxTW5YQzVFTmovVk55WEx1ZUxEUGRRV2xoUlFreW5QL0ZkSHMzbXU3TDhhZVB6NjMzZDMxQmRxbmVIRE1TaU1GclphcmFjR0RWWjZQYnZHMHhiNXhZcU5mV2h0Nk9PNHNSd2cvV3FVY2VuUjFDcFArN3hxc1Vzd3lTT2d1TEpneE5WUHhTUXJWdUVab0RpNXdyb1JkR1BxdkhRL2dqTk96Q0YrQVVjU1JHL0lGY1R5M1B6RHcwSWlBZUIzcXIrVFNRSkhCNXdhS0hhZlc0cXlmb1V6R3VYa2lZR2w4QzY2Z3VQMVdIUk5XMHFTblhYcHVQWXdpKzRwaENSTlRFd1F5ZDJPQXRvM1V5RkxVc3VYY3JHV05XOTVPTFBsS0ZzcDFKV0Q1UC90T3hsWGM4aFBwM2tVU0h2OUNNOXhEMkJLcUlMaFpJZ2JXUU51N3Q0Q1VtTmxkTGJhQVlWdkpPNHVzQVIvMzVtOEo2NlQiLCJtYWMiOiJkYmFhNjFmOGM2MzFiNTQxZWYyNTdmZDA2MjRjOTE5ZDc4MjA1MTc2NGE2ZGJmMjQzN2E2NjNhZTY4MDcyNzExIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 00:38:03 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6ImordUVhMXZlRHgrb3BBOGJNVS9vMVE9PSIsInZhbHVlIjoiV0tqSmphYmdNaWdycGFPend4NXc5MnNoMEQ4cDhXUTNvRmN3cE5WampCVWZxL1Jmd0p1eVFhV2hVcHpJMVdsV0ttQk1rMHgrNm11OGJWM0loRW82QUgzRCtZTVJLTllFejNUQjhRMTY2dm1CQ1Y1WlRCUFg0eTByNlEwczBZT0lPdVZIUklvSkIxVjNmVW96WDRvQ21qRkNaODdXVEh2QW55MjdNVFNEcTlRNUZ5bVpOeGxqeU5pSVhzV2NJc0hta0JSdkROUGxOT3B4QStnTGZyL0ZmWDMzVUQ3NHZ1bGgrMTFpRWdUekt6UmUvS09qUjdvaDJxTlAzYk9MZFljbTg1LzM4L1NYNkxSNlNVVFMxVlF6UVMwOWJHNDdDYkRJSWZ3T2ZrVDlIdWRUdXlHM1VBMWVMd3Jyck8vZFR6dEc2eG9iQ01tTkdRT3VkTkxmVnVJejdNbmxqNmdKeStBTXIvWnVVSldobUlBeGVsbVFZb2c4TUVEUVdTYndDYWJ0NVhmRk8vdDZCa0xzUC9wdjkrNVlvUk9NM3cvMnlUdG5IRWdmMkwvZU1FOGkvSVJBMkMreExoTVNFTGtOMFFHUHRST0VJUWgwQ2ZjOEVOdFpuMXBjU3RjbzNBNUovSFFQYzF6bW1mWHBBdlE2cjlUYkM2TjVwVCtNZDdFQVdxL28iLCJtYWMiOiJhMGQ5Y2Q2Y2JlNDI4Y2Q3MDhiN2M1ZjU0NjAwYzljNjYyMDBkNzZkY2EwMmQ0MzcyOTFhZDcwODQ2NTgyN2IxIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 00:38:03 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6InlQb0xrek92RVoxdkdxejJZQjVybHc9PSIsInZhbHVlIjoiL1h5Y1k2R0hqMDVndmN5SFR0MTRjaGFmbzhEVGxpVWRmL0RlR2ExL0E1WjRiRmUzc2I3VnNtTm5xUTU4R2V3NXRBUDczTHVOSGtaeFJJU21FTzNXeS91Rk1XOVdNY2NmeHJWZkdPZnhXZnNRd1VaU2ErMWdNZmVFSnd5Vi85aEI5QzBiekdxanhpZENaOFR5RUFGRjJxTW5YQzVFTmovVk55WEx1ZUxEUGRRV2xoUlFreW5QL0ZkSHMzbXU3TDhhZVB6NjMzZDMxQmRxbmVIRE1TaU1GclphcmFjR0RWWjZQYnZHMHhiNXhZcU5mV2h0Nk9PNHNSd2cvV3FVY2VuUjFDcFArN3hxc1Vzd3lTT2d1TEpneE5WUHhTUXJWdUVab0RpNXdyb1JkR1BxdkhRL2dqTk96Q0YrQVVjU1JHL0lGY1R5M1B6RHcwSWlBZUIzcXIrVFNRSkhCNXdhS0hhZlc0cXlmb1V6R3VYa2lZR2w4QzY2Z3VQMVdIUk5XMHFTblhYcHVQWXdpKzRwaENSTlRFd1F5ZDJPQXRvM1V5RkxVc3VYY3JHV05XOTVPTFBsS0ZzcDFKV0Q1UC90T3hsWGM4aFBwM2tVU0h2OUNNOXhEMkJLcUlMaFpJZ2JXUU51N3Q0Q1VtTmxkTGJhQVlWdkpPNHVzQVIvMzVtOEo2NlQiLCJtYWMiOiJkYmFhNjFmOGM2MzFiNTQxZWYyNTdmZDA2MjRjOTE5ZDc4MjA1MTc2NGE2ZGJmMjQzN2E2NjNhZTY4MDcyNzExIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 00:38:03 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2053546179\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">iD3eOztlFQTk9Wu93brk2PH5kNLllwdWSLzbHLjf</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"39 characters\">http://localhost:8000/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">error</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>16</span>\n  \"<span class=sf-dump-key>error</span>\" => \"<span class=sf-dump-str title=\"65 characters\">Access Denied. You do not have permission to access this feature.</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n"}}