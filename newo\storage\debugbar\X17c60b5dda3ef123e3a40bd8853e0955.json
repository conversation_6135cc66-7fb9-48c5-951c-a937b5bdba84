{"__meta": {"id": "X17c60b5dda3ef123e3a40bd8853e0955", "datetime": "2025-06-07 23:59:03", "utime": **********.603228, "method": "POST", "uri": "/chats/favorites", "ip": "127.0.0.1"}, "php": {"version": "8.3.16", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749340742.534154, "end": **********.603263, "duration": 1.0691089630126953, "duration_str": "1.07s", "measures": [{"label": "Booting", "start": 1749340742.534154, "relative_start": 0, "end": **********.470446, "relative_end": **********.470446, "duration": 0.9362921714782715, "duration_str": "936ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.470468, "relative_start": 0.9363141059875488, "end": **********.603267, "relative_end": 4.0531158447265625e-06, "duration": 0.1327989101409912, "duration_str": "133ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45046544, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/favorites", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getFavorites", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "favorites", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=454\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:454-477</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.00665, "accumulated_duration_str": "6.65ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.5405722, "duration": 0.00466, "duration_str": "4.66ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 70.075}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.568677, "duration": 0.00107, "duration_str": "1.07ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 70.075, "width_percent": 16.09}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (15) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.583679, "duration": 0.00092, "duration_str": "920μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 86.165, "width_percent": 13.835}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "cN6pwMHluqMWM9hLaRtOtRasnQF8pfN3ghxjsOfa", "_previous": "array:1 [\n  \"url\" => \"http://localhost:8000/productservice\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/favorites", "status_code": "<pre class=sf-dump id=sf-dump-1963236383 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1963236383\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1661013422 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1661013422\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1643986136 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">cN6pwMHluqMWM9hLaRtOtRasnQF8pfN3ghxjsOfa</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1643986136\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1267779806 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">http://localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"36 characters\">http://localhost:8000/productservice</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1995 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=a091af98-270d-406e-ab7e-33a6e610d6e2414686; _clck=nups8l%7C2%7Cfwk%7C0%7C1960; XSRF-TOKEN=eyJpdiI6IkgvYzJMSGwxNHk1bjMxNHpTTEkyRVE9PSIsInZhbHVlIjoiSmExU2xJanZoT3R4ZkFQeUplOFRzaUdFQ3A0Q1VlQzBoSkhPSERSSEJwNzlGV1hXVlV4NVlhMGZaRkRBYUQ0aEVnejd4MGZ6WmF6Z3JjN3JMZVMxZy9FQURBSE56bFo5YlVWNEk5cEV1azdsaS9QMWs4Rks4T21pdi9FeTVHZHpxbFgrWkxHdXBUKzhIOHQ0MVlEMjI5K096VXNTaVpMM09KL3VpRnVOc0FKUGF3TlRIRi8yNUNVUTNZbFc0cHRjMmZ3TnkveDV6TUpYck03NVBDWVFPamFCZEg4WmpFdUpXRDlEanN5UmdLbVJ4aXgyd0gxSlRUa3VhNnB3aldIZ3Vhdk9ROHhuQW4zVlpKMHdyY0lrMVpibmFoUEZJUDZQRU15dGRPb3Jaa2FzcjB1djVHVXJsSExKWVBUN242WXZ1bkJIbkpJamp1V2krMzRCOXFQeXMxVXNnNCthekNNa0tDd1h0Q0dsRnlMbVU0MDhBN3FCMm5KZ0RKclBBOXdueXZ6dDBTdTZ3SkV6bEZCMGowMHd4bUhjSjVFVTN4bVcyTVF3NU1NSEJsYVgxQzhhYmZ5ZDdiQ2hHR1p0YkZRTGVPUk1KK2E1bnRpYjk3MEQ1Y05GSWtUUUFIeENXU0x5enZxTjZDK3J3WDYrNFdKR0NINFZXTlRhTXVXY255NWQiLCJtYWMiOiJiZjYyNDgxNDVmM2UwNjkwZDcxM2VmNTk2ZjIzZjU3NjJlOWIxY2MwOTc1MTdiNGU0MmM5MDkwNGRkNjFiYzQwIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6InZBZUpNdTlCUi9kWGV3ZGdBZCsxYkE9PSIsInZhbHVlIjoiaVRHaUk2TU5jbjlFc3Zjd1JPMmRyY0VkekszeGh4U1JXeHFwaFNEakU2dk1ydzgwelVBd1NuUUJQbDk1aWpxVmtSb1J1THVZaWI2Vm50UjR2VVo3MHNzWXY3eG8vYk1pdEg1WnNOMjloUVlpL1NuaEFBTmMwdDJWcXFUNTBzOVM5dGFBMTAzTXBGSVBGQnh5RmtBYXRQLy9kbkQvakdNNkFZekdmcWZkT2NyQzd0R2F1REMxNi9qcDN4L2ttWUNPVUtxb3Bva2JxUmhDdVh5RFRpSWh1WnZHSzgzRzluTXVocHMwUWlwYTZjMWJHVGk5RTZWcnVUanhpQU01VmhPUlpNWVZCMFhHMlpQNFZueHFxZUhRajY2R0owNi8wSFV3eWNueTI1bk51UFFvN3NRblhuS1Rhd3FLWDBIZmdBYWhkQTM4TldWZnUrVWpEQVJyZmNaRWthcU80R29vK0NrQm9Ja25KZDFOQVAyVkxHbXlkcHpzVVNSc3JlMHNJdXNLaStRejBlSVdXY043VHJNVmx1SldVbVpFZXM3bWNmbThCMVhDTUc4OTlOeWlnWk5EYWRPMDBKcGptY09sbjJDQWNHVC9MelVUazVVbDJkQ3VEdUpVWDBpNGJRSDl5bHUvOHRkQzJkRWVHWlNXMXp0ZFRHK0lmOGNjVjRLSFZERlciLCJtYWMiOiJiNmUwY2YzZjgwMmMyMDE1M2I5YjU2MjRlMzA5MzY5OTY2ZDYzNTg4ZmE3ZDA0NTcwZGRmMTYzNmZmOTQ1MmQyIiwidGFnIjoiIn0%3D; _clsk=1dgl8io%7C1749340740136%7C23%7C1%7Ck.clarity.ms%2Fcollect</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1267779806\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1070067633 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">cN6pwMHluqMWM9hLaRtOtRasnQF8pfN3ghxjsOfa</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Eo35AMmp3Bkf2zsyHLroae0N6MdUih7xJ0ojLwSF</span>\"\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1070067633\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-797248138 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 07 Jun 2025 23:59:03 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"39 characters\">http://localhost:8000/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IlpoMFY4dkVFTUhkcG80aDlvOUtQa3c9PSIsInZhbHVlIjoiRFByVlhrekJhQ0FseHc5NDFuWmR3UmZ0R0pHQ1RGZGJsT0FnQnFXdFdnNjJJUTFWNFAwUjJ5MXRNVUNnUy9LS25MNmVDV00xNGpxT0xnNHV2bnFZSVZhRkZ4RFBRMk1ScmlGb1NSRm4vNmxnRWxSYU1uNWpUK1UvV09Oa0w1bjY0bVZTaGhNRXlqTlBpVzJrNEgwM25IZlNsbDBiNHQ5cW5ybjE5QmNXV2hBMVhIalNwb05OVEJvTjNGMDRHVWVLZHR6Y0E5MlB3K3h1aHlVM3l1bDlVSS8xMk5xa0ZSVGdwTGtyM2NyNVQ4eUNHR3lpNHFQbGxvQ3htaGVoZGN6aExyby9EQ3FLRi9rN05CbWxtYWNLV3RwclVSWjZZRkVLTVRsS2N4NkQ5RTZsS1RDMUw5MTl5ZXUvTEl5RTl4Tkg0RzZzNUtxZmc4dW1GNXB6eHFTYlNiNDNWbzVRT1JTbUk5cFZGTHNkRGJpQ1ZJMXVkTjdpb1MxMU9JN0FTMTNYRkxqLzhYQ1JKeFJLTzZnVlpYRE9GK1dyWDJrVlRtbTR4Vk5UcXZCYXFlWHp2anArTkh3WEVVL21VeVNNUEh1RThJY0hUMmoxWU43Z3dLZDdrUmJTYVd4OENISXhZdHgzcnhBWEdZUmo3cmdUNmYwRFloZHBySDQrU0t0TElxbC8iLCJtYWMiOiIyMjA0YmE4MDI3NjYxYmU1OTM3MjhjYTViZjdlZDY4NjAzZTZjMjRlZTljMmNmZjYwYTcyMjZkYzQ3ZjNkNjA1IiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 01:59:03 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6InA2N21YbjJ4TEUrZTBOM3FOS2RTYUE9PSIsInZhbHVlIjoic3liaHN5d2JyNm5QVnoyVGNld0tXSjZCZC9Nc0twOTZhd3cvaG5QN0I0M2dDaHlITE5ZMjVmaDhINnlBVkJ4cXNuaWl1aFNwOWZuK0JseXdnWWpkUHFTRkZnY2RtUmJCNXFwQmJsakUvQ3hZelNna2hZOFNHSURTam1wSXU0WFlKUnJiUDNjZ2ROQXVMU3VPM1pRZGlhVDRhbnVRWU4zNU01d2lJUWQybGxQaUpsSEJsSTFXOXdOT3NYVE5QMkw4SWVXRVQ5WlNTTlFqTzRYVHV5UUE1SlhSQ251L3FzbGlFM3lNemdTN3hpRVAzektvS0JZUG9OeVJkbUlSRmQzdDF1YzhRWlhsTFViY2xqdk11KzMwWXhLUmUvem15ZWVDS2c2ZHVoTEc0Qlp4N3Y5ZHgvMGwrN08wVnFSZGF0Mi9CS0xXbUtKME90UU9vN1Y1U25NZ3JaR254amlKSVFnMy80K1NiMitIZkF3T1BvMmxSeW9DWURKK2FJaWRwQjEvWlVRcUhxUno3SEV0VHhab3JXZHVrRmZhSE9Ib2VLVmdPSXRVY0dPNDFmVkFCbTlqMTNEOHZObGJsZzBibDcxNmg3OWZETVZOYXprVTd2bGJaeHlZN2hhbGdNKzdoL2plbDhkV05uc0xUbXRuazZaa2hZRjIrbHRXcm9YT2ExdVMiLCJtYWMiOiJhZTc3MWFlNTNkY2FkZjVjZDY4YzY0YzE4ZTY4ZTRkNTk4MDgzNGY2OTkzNTZkMzkwYzQ2ZTdhODI3NWRhYTc2IiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 01:59:03 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IlpoMFY4dkVFTUhkcG80aDlvOUtQa3c9PSIsInZhbHVlIjoiRFByVlhrekJhQ0FseHc5NDFuWmR3UmZ0R0pHQ1RGZGJsT0FnQnFXdFdnNjJJUTFWNFAwUjJ5MXRNVUNnUy9LS25MNmVDV00xNGpxT0xnNHV2bnFZSVZhRkZ4RFBRMk1ScmlGb1NSRm4vNmxnRWxSYU1uNWpUK1UvV09Oa0w1bjY0bVZTaGhNRXlqTlBpVzJrNEgwM25IZlNsbDBiNHQ5cW5ybjE5QmNXV2hBMVhIalNwb05OVEJvTjNGMDRHVWVLZHR6Y0E5MlB3K3h1aHlVM3l1bDlVSS8xMk5xa0ZSVGdwTGtyM2NyNVQ4eUNHR3lpNHFQbGxvQ3htaGVoZGN6aExyby9EQ3FLRi9rN05CbWxtYWNLV3RwclVSWjZZRkVLTVRsS2N4NkQ5RTZsS1RDMUw5MTl5ZXUvTEl5RTl4Tkg0RzZzNUtxZmc4dW1GNXB6eHFTYlNiNDNWbzVRT1JTbUk5cFZGTHNkRGJpQ1ZJMXVkTjdpb1MxMU9JN0FTMTNYRkxqLzhYQ1JKeFJLTzZnVlpYRE9GK1dyWDJrVlRtbTR4Vk5UcXZCYXFlWHp2anArTkh3WEVVL21VeVNNUEh1RThJY0hUMmoxWU43Z3dLZDdrUmJTYVd4OENISXhZdHgzcnhBWEdZUmo3cmdUNmYwRFloZHBySDQrU0t0TElxbC8iLCJtYWMiOiIyMjA0YmE4MDI3NjYxYmU1OTM3MjhjYTViZjdlZDY4NjAzZTZjMjRlZTljMmNmZjYwYTcyMjZkYzQ3ZjNkNjA1IiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 01:59:03 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6InA2N21YbjJ4TEUrZTBOM3FOS2RTYUE9PSIsInZhbHVlIjoic3liaHN5d2JyNm5QVnoyVGNld0tXSjZCZC9Nc0twOTZhd3cvaG5QN0I0M2dDaHlITE5ZMjVmaDhINnlBVkJ4cXNuaWl1aFNwOWZuK0JseXdnWWpkUHFTRkZnY2RtUmJCNXFwQmJsakUvQ3hZelNna2hZOFNHSURTam1wSXU0WFlKUnJiUDNjZ2ROQXVMU3VPM1pRZGlhVDRhbnVRWU4zNU01d2lJUWQybGxQaUpsSEJsSTFXOXdOT3NYVE5QMkw4SWVXRVQ5WlNTTlFqTzRYVHV5UUE1SlhSQ251L3FzbGlFM3lNemdTN3hpRVAzektvS0JZUG9OeVJkbUlSRmQzdDF1YzhRWlhsTFViY2xqdk11KzMwWXhLUmUvem15ZWVDS2c2ZHVoTEc0Qlp4N3Y5ZHgvMGwrN08wVnFSZGF0Mi9CS0xXbUtKME90UU9vN1Y1U25NZ3JaR254amlKSVFnMy80K1NiMitIZkF3T1BvMmxSeW9DWURKK2FJaWRwQjEvWlVRcUhxUno3SEV0VHhab3JXZHVrRmZhSE9Ib2VLVmdPSXRVY0dPNDFmVkFCbTlqMTNEOHZObGJsZzBibDcxNmg3OWZETVZOYXprVTd2bGJaeHlZN2hhbGdNKzdoL2plbDhkV05uc0xUbXRuazZaa2hZRjIrbHRXcm9YT2ExdVMiLCJtYWMiOiJhZTc3MWFlNTNkY2FkZjVjZDY4YzY0YzE4ZTY4ZTRkNTk4MDgzNGY2OTkzNTZkMzkwYzQ2ZTdhODI3NWRhYTc2IiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 01:59:03 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-797248138\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">cN6pwMHluqMWM9hLaRtOtRasnQF8pfN3ghxjsOfa</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"36 characters\">http://localhost:8000/productservice</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n"}}