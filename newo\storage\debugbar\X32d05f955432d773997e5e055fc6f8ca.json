{"__meta": {"id": "X32d05f955432d773997e5e055fc6f8ca", "datetime": "2025-06-07 22:21:14", "utime": **********.247811, "method": "POST", "uri": "/chats/getContacts", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749334872.636928, "end": **********.247844, "duration": 1.6109158992767334, "duration_str": "1.61s", "measures": [{"label": "Booting", "start": 1749334872.636928, "relative_start": 0, "end": **********.033277, "relative_end": **********.033277, "duration": 1.3963489532470703, "duration_str": "1.4s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.033305, "relative_start": 1.3963768482208252, "end": **********.247848, "relative_end": 4.0531158447265625e-06, "duration": 0.21454310417175293, "duration_str": "215ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45576128, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/getContacts", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getContacts", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "contacts.get", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=333\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:333-390</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.00819, "accumulated_duration_str": "8.19ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 16 limit 1", "type": "query", "params": [], "bindings": ["16"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.151057, "duration": 0.00584, "duration_str": "5.84ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 71.306}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.194329, "duration": 0.0012, "duration_str": "1.2ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 71.306, "width_percent": 14.652}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (16) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.215711, "duration": 0.00115, "duration_str": "1.15ms", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 85.958, "width_percent": 14.042}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "iD3eOztlFQTk9Wu93brk2PH5kNLllwdWSLzbHLjf", "_previous": "array:1 [\n  \"url\" => \"http://localhost/hrm-dashboard\"\n]", "_flash": "array:2 [\n  \"old\" => array:1 [\n    0 => \"error\"\n  ]\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "16", "error": "Access Denied. You do not have permission to access this feature.", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/getContacts", "status_code": "<pre class=sf-dump id=sf-dump-1607401399 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1607401399\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1850317691 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1850317691\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-227136075 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">iD3eOztlFQTk9Wu93brk2PH5kNLllwdWSLzbHLjf</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-227136075\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1459401475 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"30 characters\">http://localhost/hrm-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1938 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=17ceeen%7C2%7Cfwk%7C0%7C1960; _clsk=1yftgw7%7C1749334860015%7C4%7C1%7Ck.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IjRnbUdnaG1vQlU1SXBtVWJPQ3dJRnc9PSIsInZhbHVlIjoiQUgzYkZLNFdCbjF4Q2RpK095bXk3L2ZqMnNzOE9YMFlSNWF3TWpBWW1TcnNhQkM5SWJhcGdSTEtsTjl1L2J6b2NCUFJnWDgwRFkrVkFpM2V1WGY5WUkxYXg2ZjJMKzVlTm1ZM3E1NUpBMllQNEYvd3RUanBUdlljTWZURk5ld1R2RU5rWDcrclBidCsxdjgzTVFYU2ZQdFVEeFUwYUdYYnNldDAxdFhWU3RtVUNNU25jUExlalR1eWZoRFd6eHpGQUhDSFBUWjE3TXQ4V01kK0xZQVdSclZIcHFyVFZUamlqcTUrOCtidU5ObXZxN3Jrby81TExBeHpYZkc0QVJiM3k5aXJnSkhWSVNxQitqNmVtUnZzQ24rbW9kbmx0bEdaa0Jhd0tkdTJUVlBEbjN3NzQ1RmpkZ0taVmpFVDJ4dnRzQkJTeDVCN0RuaW5zbDErMlV2elhDVU9DS3ZSV3NObWczTWRwVUxoOHQ2N3RkZiszbDQ1anRBTTRITHdSTlFYVWx2WG4wSTIzZzF5c29ZQXlBL1VIZ25xQUxiSW1RRnhIQWU3c3FyZkJoMjFGRGEvYlVPaFVydFA3cjFKVzhwNDFROWtrSURST2ptRXU3cmZZeSsyWk45UlhYYTEySXZ1Z0Rudk52S1RRY3RjZklvMXR4dXAyeSthMmJRSkdOZkUiLCJtYWMiOiI2YjhlNDQwMjUxYWYxNmQ1ZDlhNDUwYTdjYzFmMDliZjJiODcyNWE4ZWJmZmUxNTc1NmQxNGI3N2RjYzU3Y2ZiIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6IjEyKytiWUJqdmIvQjZwcEhiRDlvR0E9PSIsInZhbHVlIjoiVEc0K09GT2puUm5aalI5Zy9KRWFJYjcrS0oxNzNZUkRKcnpBc1ZHTmNGVW9jM1UwbXJ1NG5wZXZBeGQ5QmF5WmhWMWFmMCtYYjh1NVdvdUViTkczcUVhMG94bnZKY0wyRkdjWllPVzdkc3hnaFRQL3Z5R0s5SU5yL3NIaGtvUFZxc0hlVjBkelpnZFBmK0FDQzc1dVFLQUxNblpGYU9JU2RITjYyeUpPaTBrSU5mQ3hDeFZTN0dxcjRJZ1Z6M1RoSnY4TFpqeHR4cGQwa3UxQUdTWDJNdXF4NDlDR25LTXhEUW9MRGlMZks3eHhyTlJkNTB3UUlhTHFSWXQrdGJmVUxISFE4NVd6Q2lNaXByNGU1U2psVXlvZEpRR2JCcjgvRFRtNWdMVU1uWERtSzE1NjBZL2hYL3NycmxPelpLS20zb1dSVDFxVk9MZ2pqQ2NjRFZXdDczdGJHN2dqS011emhwUDNpTlZyRHIzcTlpWTNCWGZScVFjVDY2b2dkeGRKT2Z0YXpGeWYzTk4rTUhFdHNNM1JvMWo4bWpsSzlQNDQ5cG1xa0FjcHRNTTRGdWUzd0ZYNjFRUU52OXRPRUwrS05PbCsxMTEzZ1ZoVFRrVndyajI0RVhYeVVNSm9ISGlsYUtOaEFZcEt5SjljbVpNdW80Vjd0QTRDQjV1UWY4NGUiLCJtYWMiOiJhMjZjOGFjNmY4OWFhMTczMmNhYTY1MzIwNzg5YWM4ZTkzMjY3MmQ0ZWI2OTQ0ZjRkOWE0ZDc4NTFhODgzZWQ5IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1459401475\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-620174707 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">iD3eOztlFQTk9Wu93brk2PH5kNLllwdWSLzbHLjf</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">atMfj5qpj5gKx4OQYPP5PbQzRbuF7iB8X4zv4aOY</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-620174707\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1072050438 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 07 Jun 2025 22:21:14 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6ImcxeEFuOGVXWkV2SVBWRTg4TWtGaUE9PSIsInZhbHVlIjoiYm8reW5tV3YxRHZxZ2lNdlZQT3JEMHRmeXovTUVzWXVGMDV3TDF0Y29jemlxMThyZnlhMi9kNFFQVkRwMVRBakFZSE04SkQ3a3U1MTcxb2F2MEwxZXVwTnp3VlBIWmRmZkEwZTk4Rmh3bUVpNlpFeHppdEMvYzczcWxXNDVrRWVGVnhXNUNZeEw0Qzg0VzJjU0FISDg3MUZSOUROZlBYNFltVTNUek9GbEFGRzdrUi9JOGdCa0RmM25lTlJMQmdyejF0UGUxb3Q1WGt6eW5SdFNqaGRQK2V3dHNXOXpyamxCSFY5WTJCakVVdFFEYk5OTFdNTzFGTE5IYU9sY2dwMW9xZlY1VSszOTl6QjIxN1ZhRTdhazE0SlQ2UnBXL1NRdUR3K3Avc0F4d1lJODNXRmJMYldqRVZPK2hJeEQzWHVCbHBpRkpVN09HMDlMQmw1U29mQUZkV2NrMEVBdmZaMjRDZXI5bG9NV0JZUVI3YU55dXUzckYvT2t0bi9DMmpOSEpkb0JqbXdxSHBjekZXOHo3dVNWekY4WnBzV3JxeHR2cFZBQlpxZGg2WWxxZ0FxOGZTTjd3Y2ljTG1uT3N0K1Jla3lNL1orWGdDa08wQnpodDNRMGE4bExnNjY5RWM1TzBrN2FSUENhN1lmS3c0czVsUGZNNWtURXduVi8ydjIiLCJtYWMiOiIwYjk2NTgzMzg5NTY0ZDFkZDkzZDRjZWM2YzhjZDdlYTU3MzRmMDkwNzg1YjVhZDQ1NTI0MWYxMjZlNjYxNTMwIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 00:21:14 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6Im12RlgxVTNVNDZaUjNJRktSV0tFbEE9PSIsInZhbHVlIjoiU1B0ajVHUWZ0S1AvVVNYNWhPY09NVFBidGtQUFd5Q1Q0NE1QWnNGcWJ2b3ZwSUJic1MybjBVeDFUS1k3anVXRjVYaGpJSU9nb1pQdjYyOXNZMkhKSGQxODJRQVQrcEhGSnZHdVVWbVBnb292U0t6eVVOSGF0VEp4cnVJNUxRc1AvcVBBMUJiOW1DZlJMc252M3pOeXVyUlRHYk14ZWp1NjdQYmpaVzM0SlIrTmVSRzVhVVhITHpqTHozZUFMMDhvVkQ5dmRkZ0RrWUJOSUFDaXRGN1d6S0s3eUVJQUsyQlR6VnVxci9rckZ4d3pVOTlBanNMd1N4ZzRlMXd6eGdqdXVwMmt0WlRVblFmbnBENkZnNG9MR3cxNHJoS1RRTGJYQSsySFg0THlCNzY1K0ZlT0pTRFlNd1dVVVJFTjJwNjd5NE1Wc2RYTk5xM0tiRlg3VHBhMEJ4cWtTZE9uWmhISkNENCttbnF4WDhRTG1uZFJMQ2xqYm9mMGVic2dRM0pReFVlRU9ieVJHNndIUjJKd2NqMUlaQktEVDJkdmZCZ2ZISkltWDZwSlRRSVB6azBUVnFRbSt1b21vbWlDTnNpT3dpUDZ1Z3JhZ056Ulk5TUVLaVA4L3Q5bkpuelN5RysyaVlEWHBMcDY4dnc1c0N6alNoOVBZVmJoNEhFenAxRlYiLCJtYWMiOiJhY2RjNWYyOTAxZTBlZjNjNThmZGIyNjMwYTkzN2M4ZThmNzNjNTQ2NjAxZjFiOGE2NDhiZWE1Y2FhYjU3ZTZhIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 00:21:14 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6ImcxeEFuOGVXWkV2SVBWRTg4TWtGaUE9PSIsInZhbHVlIjoiYm8reW5tV3YxRHZxZ2lNdlZQT3JEMHRmeXovTUVzWXVGMDV3TDF0Y29jemlxMThyZnlhMi9kNFFQVkRwMVRBakFZSE04SkQ3a3U1MTcxb2F2MEwxZXVwTnp3VlBIWmRmZkEwZTk4Rmh3bUVpNlpFeHppdEMvYzczcWxXNDVrRWVGVnhXNUNZeEw0Qzg0VzJjU0FISDg3MUZSOUROZlBYNFltVTNUek9GbEFGRzdrUi9JOGdCa0RmM25lTlJMQmdyejF0UGUxb3Q1WGt6eW5SdFNqaGRQK2V3dHNXOXpyamxCSFY5WTJCakVVdFFEYk5OTFdNTzFGTE5IYU9sY2dwMW9xZlY1VSszOTl6QjIxN1ZhRTdhazE0SlQ2UnBXL1NRdUR3K3Avc0F4d1lJODNXRmJMYldqRVZPK2hJeEQzWHVCbHBpRkpVN09HMDlMQmw1U29mQUZkV2NrMEVBdmZaMjRDZXI5bG9NV0JZUVI3YU55dXUzckYvT2t0bi9DMmpOSEpkb0JqbXdxSHBjekZXOHo3dVNWekY4WnBzV3JxeHR2cFZBQlpxZGg2WWxxZ0FxOGZTTjd3Y2ljTG1uT3N0K1Jla3lNL1orWGdDa08wQnpodDNRMGE4bExnNjY5RWM1TzBrN2FSUENhN1lmS3c0czVsUGZNNWtURXduVi8ydjIiLCJtYWMiOiIwYjk2NTgzMzg5NTY0ZDFkZDkzZDRjZWM2YzhjZDdlYTU3MzRmMDkwNzg1YjVhZDQ1NTI0MWYxMjZlNjYxNTMwIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 00:21:14 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6Im12RlgxVTNVNDZaUjNJRktSV0tFbEE9PSIsInZhbHVlIjoiU1B0ajVHUWZ0S1AvVVNYNWhPY09NVFBidGtQUFd5Q1Q0NE1QWnNGcWJ2b3ZwSUJic1MybjBVeDFUS1k3anVXRjVYaGpJSU9nb1pQdjYyOXNZMkhKSGQxODJRQVQrcEhGSnZHdVVWbVBnb292U0t6eVVOSGF0VEp4cnVJNUxRc1AvcVBBMUJiOW1DZlJMc252M3pOeXVyUlRHYk14ZWp1NjdQYmpaVzM0SlIrTmVSRzVhVVhITHpqTHozZUFMMDhvVkQ5dmRkZ0RrWUJOSUFDaXRGN1d6S0s3eUVJQUsyQlR6VnVxci9rckZ4d3pVOTlBanNMd1N4ZzRlMXd6eGdqdXVwMmt0WlRVblFmbnBENkZnNG9MR3cxNHJoS1RRTGJYQSsySFg0THlCNzY1K0ZlT0pTRFlNd1dVVVJFTjJwNjd5NE1Wc2RYTk5xM0tiRlg3VHBhMEJ4cWtTZE9uWmhISkNENCttbnF4WDhRTG1uZFJMQ2xqYm9mMGVic2dRM0pReFVlRU9ieVJHNndIUjJKd2NqMUlaQktEVDJkdmZCZ2ZISkltWDZwSlRRSVB6azBUVnFRbSt1b21vbWlDTnNpT3dpUDZ1Z3JhZ056Ulk5TUVLaVA4L3Q5bkpuelN5RysyaVlEWHBMcDY4dnc1c0N6alNoOVBZVmJoNEhFenAxRlYiLCJtYWMiOiJhY2RjNWYyOTAxZTBlZjNjNThmZGIyNjMwYTkzN2M4ZThmNzNjNTQ2NjAxZjFiOGE2NDhiZWE1Y2FhYjU3ZTZhIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 00:21:14 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1072050438\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">iD3eOztlFQTk9Wu93brk2PH5kNLllwdWSLzbHLjf</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"30 characters\">http://localhost/hrm-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">error</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>16</span>\n  \"<span class=sf-dump-key>error</span>\" => \"<span class=sf-dump-str title=\"65 characters\">Access Denied. You do not have permission to access this feature.</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n"}}