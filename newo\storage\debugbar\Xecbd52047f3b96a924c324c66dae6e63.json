{"__meta": {"id": "Xecbd52047f3b96a924c324c66dae6e63", "datetime": "2025-06-07 22:39:09", "utime": **********.358679, "method": "POST", "uri": "/chats/getContacts", "ip": "127.0.0.1"}, "php": {"version": "8.3.16", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749335948.613082, "end": **********.358705, "duration": 0.7456231117248535, "duration_str": "746ms", "measures": [{"label": "Booting", "start": 1749335948.613082, "relative_start": 0, "end": **********.257143, "relative_end": **********.257143, "duration": 0.6440610885620117, "duration_str": "644ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.257158, "relative_start": 0.6440761089324951, "end": **********.358708, "relative_end": 2.86102294921875e-06, "duration": 0.10154986381530762, "duration_str": "102ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45583272, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/getContacts", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getContacts", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "contacts.get", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=333\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:333-390</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.00653, "accumulated_duration_str": "6.53ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 16 limit 1", "type": "query", "params": [], "bindings": ["16"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.310024, "duration": 0.00521, "duration_str": "5.21ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 79.786}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.330631, "duration": 0.00069, "duration_str": "690μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 79.786, "width_percent": 10.567}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (16) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.3415751, "duration": 0.00063, "duration_str": "630μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 90.352, "width_percent": 9.648}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "iD3eOztlFQTk9Wu93brk2PH5kNLllwdWSLzbHLjf", "_previous": "array:1 [\n  \"url\" => \"http://localhost:8000/account-dashboard\"\n]", "_flash": "array:2 [\n  \"old\" => array:1 [\n    0 => \"error\"\n  ]\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "16", "error": "Access Denied. You do not have permission to access this feature.", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/getContacts", "status_code": "<pre class=sf-dump id=sf-dump-******** data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-********\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">iD3eOztlFQTk9Wu93brk2PH5kNLllwdWSLzbHLjf</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-593180027 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">http://localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"39 characters\">http://localhost:8000/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1938 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=17ceeen%7C2%7Cfwk%7C0%7C1960; XSRF-TOKEN=eyJpdiI6Ikg5ZCtmNW1uZHFMS1VtU2VEWkRLM2c9PSIsInZhbHVlIjoiQlhTVkFPT29Ncm5McnI3UVkwUDNUNWJZaVBrd3RDS0tObkJucGNoT0twdU9vRnpGNUVZK042WS9tWHkwczVnWWwrNitLZ2VnTVU4bXhtZm5Oc1dvYnIrd2dEOVB0aVRlejkvM1daMmxzQWprbTUrdHVYZjgyNW02dzZEVG5Cd2k2a2NpYkRjbnJrQ3o1cHViV0Rhd2J0eDAvSlJ2UklBRXdOUy9CUGxNa2tMRk9Yc0RIZGU1MDZmYWpkOE1yL3JmUFNlSGZldWl6SUJNZlUrZG9rK0dqb3VyU3ZEWVk4N2gzdFcrM1lsM21nTGJNcHF4T2g0T1JybTN1R0JNQURCbDdYR3c3bUNQVVpoRnhpZ3JYYjBwd2haWU9YMm5ickFGTFpWMStHZGt3T1NLbE5WSHlObzB3RFVhSTl6bGF6S2NTU1JFUENwTkVUUml3aUhUTUFYWmRKTkViMEd5S2F4RjJIRXpjc1hqaWFtZVJDcTJ2aC9rNFg1VTVRazZKaDJ1N1ZmZzNpSjhhMmlTc09hUjdqcm15NmkvNWcvQVdQdllZaGhsUTNZTXM5blR0Z0tzYTJCQzRxSXljTy9WZnpNc3lkbWdITWZtWkZvNmNJM1g4Y08wVU9ocXc0TzduQWg5MWF2ME5KRGNaSzVWaTRlR1RIK2RKMk00YWVaWDk3NnUiLCJtYWMiOiIxODBkYjI4NzFmYTYxNTlkMTE3NDFmODMyMDY0NGE1MTU2MTlhOTQzNzE1MmNiZjllOWRhMGQ2MWM0NmExYjdkIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6IjlheHlLNm5BNFU1RTJxd0JYWmJhemc9PSIsInZhbHVlIjoiQ2l2VWlQeFFGYUc2ZElOcUhrQ3g4NU9HbDBKSndZTzRsTWNvM3hEbTR6SmZtTmlTWm9mYTJ0SXVjejlZS01FNTltd25VMG1ESXRaTWx5QkN5N1BOajhneVYwY2M2eFl5MUM4OVBzTm5vai9KaU1vUDRnVVVxY2hJMGFURlRPN0F4RDQ1bjI2RnUyR1k0dFdEV0JaNzEwR1N5SzZ3UThTUmNHR1A5YmxEaDB3NW9idkRnbm92T0o4UTNCV05lNlhPSzgwVlk0YnlkcDRSa1B5OVdJTlVyVXhxaVp1SERiWitJYnJ3SnBTSWFKWTdPcC9UbFFvVnZGb1hvblNvMkFrZkQ4REFXc0dsbXJvaHYyNmRmWktZd1NPL2w3MUhScXo5ZFpuMkxyQWVnYzJZOWlIS2xQTFRPR2RpRTBTRkFqSk51TDdWRnpKdnhHUGpTUE1RNjJFM3RYcFVrMWFvTkFmMVduYkd4YURadGxnbk5OdkNreTFvMmxmOHlXRWVDMmVsSjltNXUrZ1JsMktLQkgzT2dmZmlVbGk5dnRkRXEzL0VzbW4rL3YzTWNHOGZFVVhreXNjQ1RtMXphVks3NUZXY1k1bzNsandpVkw0cVRpbHVjOHN2WTVpZnpiVXdDZzY3NktmWUtiMDFrMEE5ZzhESFpIdVhGYXpVNzlET0krZnIiLCJtYWMiOiI2MDZiOWJiYzczZjljN2UwNDcwZDg0OTA0NmQyOGJjYzRhZjk0MTRmYjU5ZjExYWQ4YzQ5NzZkOGNkYTVhYjMxIiwidGFnIjoiIn0%3D; _clsk=1yftgw7%7C1749335947329%7C7%7C1%7Ck.clarity.ms%2Fcollect</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-593180027\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">iD3eOztlFQTk9Wu93brk2PH5kNLllwdWSLzbHLjf</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">atMfj5qpj5gKx4OQYPP5PbQzRbuF7iB8X4zv4aOY</span>\"\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1462749649 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 07 Jun 2025 22:39:09 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"39 characters\">http://localhost:8000/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IkFKcDBKeGRVaStMYUMxK1pSS3kyS1E9PSIsInZhbHVlIjoibmpqS1NobzZPQ0EvL2NESlBNN01RVndSSkFDOFppUjVoQytENGxHOWFST1YzS1ZxL0hCVWZuZGZPWVlTK21iSXhDV09EaEpMWUZCMlFKNjNpM1ZEdGo1WW9nM3FvTGQ1YkRnRWw2dmorWjhQb1lRQTNkY3owWVFtUzkwTEpET2tKLzNvNWJidnJGTmRyOUN1Sk9uNmp1RHdxYWVMaGJONGhTMDkxU3FpTTNVM2hvY0o3cmxUejBLZXA3MHVSZHlSdUhidXViNGNjVXZmQ3BGUzhUR3FqNkwzOHJBbTMvZDJiOGxxcXVCcFc4ZnNvVnNtaUoyUi9XdTFzVGdyZkhHYXJzYVlBeHQxZ1lmZ3ZhYU1odnVOWVJWMEl5dkV0L1F1SmZrV1dxK1djSE95SFNadGk4MTRCbXJNMTQ3ZHM0V2VBL0xlRFBUT2djcWxMV2E0cFFYMUtjSVVjdnRRZmlwS0FRT2JqeUhJMVBDc2FQNVhtMG9ybGszcHAvMjJ6bVJpTU5LNXZHNXhiaVhFT0pLS1EzZ0Q3SjZWVGVZb2gwK2VMVytpa1RXdjlXOFJhZWhsOFkvSUw2SUJnbi95bk5DYXlkcXZtQUJBNHBjRUFUVENkWERER3JsQmFsVG92NXJZdG5rSEtDL3UxUGh6NjBJdlF0OUVCTGp2enN0YWNiUVIiLCJtYWMiOiJmZTc1Nzk5MmRjNGJiNDU5NDg0MjEwNDFmMThlYTljZWUxZjRlYzY5ZDMxNzNkMTc5NzhkZTU5OTE1MTNmMTZhIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 00:39:09 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6ImJaelRUNjcyTmZYcy9GWUZacmxDNUE9PSIsInZhbHVlIjoiSWVHYmhmVnZSekVBdno1aXJiSUpVdjlxeG9MYXNUM0lNbVlla3BuT096cVB5QnVEeGVqaWJLNUgzRnhMMEFOVGxRTHFUTEViU1YrS29za0RPZ0dONDVDUnhyNUFiZkh1NlkveGRwa2s3aFlLMjZTUUQ0MTh2elk2TGxORDN1VmhOZGttY3J1TElyZWVOdFJldmtPRkNOL2RUa0NwL0F5S2ZCQ0ZVTEFTTzN4RnhmUmVFWTRXM3M4ZGIzbDdidU1WL2UzMkY5R1F0em5BNlUxTFN2OWJvSm5CNWFlOG5IZmhadUNGMDBUT3dxeCtBdndCbUl6NTdXcTh6SHRpd0RSUWgwOHdGS1FZUEhjYWM0SlpiL045UnpsZUd6WjNWa3NQK2VqR2EraE1wNUt1ZndlZ2luU1RFSG1GSjFpSGJwSy9tQ1JRSVpvNnFtcWQ5SkcxYjdXZ0FWK0EyUDQ5ODBjUDVOZzZWbjV2VEZQU0lWWUZ4RmpDTHRDZzkwSmNXcWJZR2RFSkpOdnJZTWl5bHh4QVRaek1BZEdmQUlJbG1oZ2haeGpyVkszb3dJbHA0bWUzYmdGWVFHdmdDU0RzeXoyK2I2NkQzK2tnL3FBVCsxQVVtbDJ2Nk8vOHluQjdmall6MTVrSkZVbWFVKzBicG03NDNCSDdhb1lYYlluYVZyTXoiLCJtYWMiOiI5ZjgxZDE1ZTkwNzQwNDg3N2FlOWE5OTE5MTMzMWVmODE2NTZkMjNhN2QyMjgyMTU5YWQ1Nzg2OGMyNDU0YWZiIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 00:39:09 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IkFKcDBKeGRVaStMYUMxK1pSS3kyS1E9PSIsInZhbHVlIjoibmpqS1NobzZPQ0EvL2NESlBNN01RVndSSkFDOFppUjVoQytENGxHOWFST1YzS1ZxL0hCVWZuZGZPWVlTK21iSXhDV09EaEpMWUZCMlFKNjNpM1ZEdGo1WW9nM3FvTGQ1YkRnRWw2dmorWjhQb1lRQTNkY3owWVFtUzkwTEpET2tKLzNvNWJidnJGTmRyOUN1Sk9uNmp1RHdxYWVMaGJONGhTMDkxU3FpTTNVM2hvY0o3cmxUejBLZXA3MHVSZHlSdUhidXViNGNjVXZmQ3BGUzhUR3FqNkwzOHJBbTMvZDJiOGxxcXVCcFc4ZnNvVnNtaUoyUi9XdTFzVGdyZkhHYXJzYVlBeHQxZ1lmZ3ZhYU1odnVOWVJWMEl5dkV0L1F1SmZrV1dxK1djSE95SFNadGk4MTRCbXJNMTQ3ZHM0V2VBL0xlRFBUT2djcWxMV2E0cFFYMUtjSVVjdnRRZmlwS0FRT2JqeUhJMVBDc2FQNVhtMG9ybGszcHAvMjJ6bVJpTU5LNXZHNXhiaVhFT0pLS1EzZ0Q3SjZWVGVZb2gwK2VMVytpa1RXdjlXOFJhZWhsOFkvSUw2SUJnbi95bk5DYXlkcXZtQUJBNHBjRUFUVENkWERER3JsQmFsVG92NXJZdG5rSEtDL3UxUGh6NjBJdlF0OUVCTGp2enN0YWNiUVIiLCJtYWMiOiJmZTc1Nzk5MmRjNGJiNDU5NDg0MjEwNDFmMThlYTljZWUxZjRlYzY5ZDMxNzNkMTc5NzhkZTU5OTE1MTNmMTZhIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 00:39:09 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6ImJaelRUNjcyTmZYcy9GWUZacmxDNUE9PSIsInZhbHVlIjoiSWVHYmhmVnZSekVBdno1aXJiSUpVdjlxeG9MYXNUM0lNbVlla3BuT096cVB5QnVEeGVqaWJLNUgzRnhMMEFOVGxRTHFUTEViU1YrS29za0RPZ0dONDVDUnhyNUFiZkh1NlkveGRwa2s3aFlLMjZTUUQ0MTh2elk2TGxORDN1VmhOZGttY3J1TElyZWVOdFJldmtPRkNOL2RUa0NwL0F5S2ZCQ0ZVTEFTTzN4RnhmUmVFWTRXM3M4ZGIzbDdidU1WL2UzMkY5R1F0em5BNlUxTFN2OWJvSm5CNWFlOG5IZmhadUNGMDBUT3dxeCtBdndCbUl6NTdXcTh6SHRpd0RSUWgwOHdGS1FZUEhjYWM0SlpiL045UnpsZUd6WjNWa3NQK2VqR2EraE1wNUt1ZndlZ2luU1RFSG1GSjFpSGJwSy9tQ1JRSVpvNnFtcWQ5SkcxYjdXZ0FWK0EyUDQ5ODBjUDVOZzZWbjV2VEZQU0lWWUZ4RmpDTHRDZzkwSmNXcWJZR2RFSkpOdnJZTWl5bHh4QVRaek1BZEdmQUlJbG1oZ2haeGpyVkszb3dJbHA0bWUzYmdGWVFHdmdDU0RzeXoyK2I2NkQzK2tnL3FBVCsxQVVtbDJ2Nk8vOHluQjdmall6MTVrSkZVbWFVKzBicG03NDNCSDdhb1lYYlluYVZyTXoiLCJtYWMiOiI5ZjgxZDE1ZTkwNzQwNDg3N2FlOWE5OTE5MTMzMWVmODE2NTZkMjNhN2QyMjgyMTU5YWQ1Nzg2OGMyNDU0YWZiIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 00:39:09 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1462749649\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">iD3eOztlFQTk9Wu93brk2PH5kNLllwdWSLzbHLjf</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"39 characters\">http://localhost:8000/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">error</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>16</span>\n  \"<span class=sf-dump-key>error</span>\" => \"<span class=sf-dump-str title=\"65 characters\">Access Denied. You do not have permission to access this feature.</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n"}}