{"__meta": {"id": "X02ebba2ad3d08d94cfa546899af7c76d", "datetime": "2025-06-07 22:21:14", "utime": **********.278232, "method": "POST", "uri": "/event/get_event_data", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749334872.636928, "end": **********.278263, "duration": 1.6413350105285645, "duration_str": "1.64s", "measures": [{"label": "Booting", "start": 1749334872.636928, "relative_start": 0, "end": **********.036693, "relative_end": **********.036693, "duration": 1.3997650146484375, "duration_str": "1.4s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.036725, "relative_start": 1.399796962738037, "end": **********.278266, "relative_end": 2.86102294921875e-06, "duration": 0.24154090881347656, "duration_str": "242ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45384168, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET event/get_event_data", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\EventController@get_event_data", "namespace": null, "prefix": "", "where": [], "as": "event.get_event_data", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FEventController.php&line=317\" onclick=\"\">app/Http/Controllers/EventController.php:317-345</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.01997, "accumulated_duration_str": "19.97ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 16 limit 1", "type": "query", "params": [], "bindings": ["16"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.1873732, "duration": 0.01678, "duration_str": "16.78ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 84.026}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.2408512, "duration": 0.00136, "duration_str": "1.36ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 84.026, "width_percent": 6.81}, {"sql": "select * from `events` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/EventController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\EventController.php", "line": 327}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.2517529, "duration": 0.00183, "duration_str": "1.83ms", "memory": 0, "memory_str": null, "filename": "EventController.php:327", "source": "app/Http/Controllers/EventController.php:327", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FEventController.php&line=327", "ajax": false, "filename": "EventController.php", "line": "327"}, "connection": "ty", "start_percent": 90.836, "width_percent": 9.164}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "iD3eOztlFQTk9Wu93brk2PH5kNLllwdWSLzbHLjf", "_previous": "array:1 [\n  \"url\" => \"http://localhost/hrm-dashboard\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "16"}, "request": {"path_info": "/event/get_event_data", "status_code": "<pre class=sf-dump id=sf-dump-909429562 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-909429562\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-405061460 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-405061460\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-518841519 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">iD3eOztlFQTk9Wu93brk2PH5kNLllwdWSLzbHLjf</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-518841519\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1036475517 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"30 characters\">http://localhost/hrm-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1938 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=17ceeen%7C2%7Cfwk%7C0%7C1960; _clsk=1yftgw7%7C1749334860015%7C4%7C1%7Ck.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IjRnbUdnaG1vQlU1SXBtVWJPQ3dJRnc9PSIsInZhbHVlIjoiQUgzYkZLNFdCbjF4Q2RpK095bXk3L2ZqMnNzOE9YMFlSNWF3TWpBWW1TcnNhQkM5SWJhcGdSTEtsTjl1L2J6b2NCUFJnWDgwRFkrVkFpM2V1WGY5WUkxYXg2ZjJMKzVlTm1ZM3E1NUpBMllQNEYvd3RUanBUdlljTWZURk5ld1R2RU5rWDcrclBidCsxdjgzTVFYU2ZQdFVEeFUwYUdYYnNldDAxdFhWU3RtVUNNU25jUExlalR1eWZoRFd6eHpGQUhDSFBUWjE3TXQ4V01kK0xZQVdSclZIcHFyVFZUamlqcTUrOCtidU5ObXZxN3Jrby81TExBeHpYZkc0QVJiM3k5aXJnSkhWSVNxQitqNmVtUnZzQ24rbW9kbmx0bEdaa0Jhd0tkdTJUVlBEbjN3NzQ1RmpkZ0taVmpFVDJ4dnRzQkJTeDVCN0RuaW5zbDErMlV2elhDVU9DS3ZSV3NObWczTWRwVUxoOHQ2N3RkZiszbDQ1anRBTTRITHdSTlFYVWx2WG4wSTIzZzF5c29ZQXlBL1VIZ25xQUxiSW1RRnhIQWU3c3FyZkJoMjFGRGEvYlVPaFVydFA3cjFKVzhwNDFROWtrSURST2ptRXU3cmZZeSsyWk45UlhYYTEySXZ1Z0Rudk52S1RRY3RjZklvMXR4dXAyeSthMmJRSkdOZkUiLCJtYWMiOiI2YjhlNDQwMjUxYWYxNmQ1ZDlhNDUwYTdjYzFmMDliZjJiODcyNWE4ZWJmZmUxNTc1NmQxNGI3N2RjYzU3Y2ZiIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6IjEyKytiWUJqdmIvQjZwcEhiRDlvR0E9PSIsInZhbHVlIjoiVEc0K09GT2puUm5aalI5Zy9KRWFJYjcrS0oxNzNZUkRKcnpBc1ZHTmNGVW9jM1UwbXJ1NG5wZXZBeGQ5QmF5WmhWMWFmMCtYYjh1NVdvdUViTkczcUVhMG94bnZKY0wyRkdjWllPVzdkc3hnaFRQL3Z5R0s5SU5yL3NIaGtvUFZxc0hlVjBkelpnZFBmK0FDQzc1dVFLQUxNblpGYU9JU2RITjYyeUpPaTBrSU5mQ3hDeFZTN0dxcjRJZ1Z6M1RoSnY4TFpqeHR4cGQwa3UxQUdTWDJNdXF4NDlDR25LTXhEUW9MRGlMZks3eHhyTlJkNTB3UUlhTHFSWXQrdGJmVUxISFE4NVd6Q2lNaXByNGU1U2psVXlvZEpRR2JCcjgvRFRtNWdMVU1uWERtSzE1NjBZL2hYL3NycmxPelpLS20zb1dSVDFxVk9MZ2pqQ2NjRFZXdDczdGJHN2dqS011emhwUDNpTlZyRHIzcTlpWTNCWGZScVFjVDY2b2dkeGRKT2Z0YXpGeWYzTk4rTUhFdHNNM1JvMWo4bWpsSzlQNDQ5cG1xa0FjcHRNTTRGdWUzd0ZYNjFRUU52OXRPRUwrS05PbCsxMTEzZ1ZoVFRrVndyajI0RVhYeVVNSm9ISGlsYUtOaEFZcEt5SjljbVpNdW80Vjd0QTRDQjV1UWY4NGUiLCJtYWMiOiJhMjZjOGFjNmY4OWFhMTczMmNhYTY1MzIwNzg5YWM4ZTkzMjY3MmQ0ZWI2OTQ0ZjRkOWE0ZDc4NTFhODgzZWQ5IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1036475517\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1250654390 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">iD3eOztlFQTk9Wu93brk2PH5kNLllwdWSLzbHLjf</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">atMfj5qpj5gKx4OQYPP5PbQzRbuF7iB8X4zv4aOY</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1250654390\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 07 Jun 2025 22:21:14 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6ImxGdldDdGdnRmtZajJYcFlMYnlXT0E9PSIsInZhbHVlIjoiOTJ1YlJ5NWRCaGJHSnZUVFpXL21tOFk0TXNmMGdVM0ZoQ1dHbUxoTVhBRDZaMlJKbzFsNHZTZGpwVU80TDNDOU1Pc2dsVHRjelozb2ptQlNPS21pMTAxaFBmUWcrcVUzWlZRQVVEYytiOEdnT2NiSldMWHh4NjJuWWtyaWtGMTVWbldCN01DRFdJVHJmendJcDBIbnhxZmFjakVhNHdhdEkrVTMwWTRiRGlCaFdHYzZmSTB1MVAvNlZpcDliMWtydVBFVXhGM0tWLzlHMUFDVlRkZlAwMjNlVzNiMVdCeDNCTTMwL292SXFHL3NqU3hTZDQwcFp2Nkw4bi9yME5qaHlFQ3dSbFFFZVR5N3B6TDQ0MGdWSkJpWE1xZkMyMUk2L1lQNkVadmVkdXl0cXQxTTh6TjErdWhaZmJVbW9mNHNIQ2lUWjVaMHJFSWR5dkVzQlNDWjlkeDNYN1Z2NTlObWRVMDNDUE5iUDNkdGFPUU8wdjdsNHVjZmZlclV0VTFIVEM5K3JMNTM1Z3Qvb2hlSG5uSE8zWEthb3l2aHFmeW1RaVVsbDBYd3VwZ3Irc1U3QlMzRmtFT2g0V0czTW1zMTl4dlFQdGFMamcxSU9kK2FIRTZtYzMwWFBRQzdwVXlHTDNHR3BnNHhlbWxEbHBzZGFzNCsyUUg2Y2M5UVpXQ08iLCJtYWMiOiJhYmY3ZDc0ODY0MDU5YTIxODExMDg0MmQ1NDBhNjIwODgzNzM1OTgyZmE2ZTg1MmUzYmM5ZDAxY2I0ZjIzODU4IiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 00:21:14 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6IlNXS0JEVzJZRUdZRDRvSXRvYW5qZmc9PSIsInZhbHVlIjoieVBpc1NPaEJTUnlDZkpnbE1kRnpsZkllRVJsK2NTVDlFQjJMTE0zLzN3Y2VDd3ZGdUFTdW9SK3ViTzU5UjFhT3YrNkNxL3Y1eE9UK1lKanQ5dkVEZ2twM1JCU1B6MVk0ZEVqdWc4aW9Iai91OUIxaE5FWjJXSW4yVS9JVjRvZVVka1lhR2x5QmZXRno5bnM2a2tyT3Q4QlBSUzhuR1pXNjU4aVJlT0k5TFowTmVsUVA5SFFSMkcrOFdESEVyeCszcFR0bHdHZ0tqMU9qNTVKNDZlQlpOZnBHR2lUSnRib0VCVmNlTDNkbmFWMC9tWi9RL3gxWWt4bUVFVk9jUWZ6Vkc3d201WXc4dXZiaXQ3bDVCZDgzSmV2V2xIb3VLbVBNNEU4Mzd2Sm9aaVpVd1NjQ0pnWmhmWmtFK2x3cFZaWGNKUDVIMWFES1RKWUFPSkJCMDdVWEQxV2lublpMeXFNRk9rcUVPQ2hGRlhOOG5Pd2JhTnFTd2tVcU05VXFTUTdsTmlPalFpQk5OS0pDOVZUSXZ5dVYxSVZJVHZrdkE2dzl4b1ZGanRDNUw3NjF4QmRNbjlkMHJwZGVnb3p4ZkxDeG10V0lZUFpaSHM4bTAxZDZMbEZrTnpIeWJVTHFEMU44OGJVQk9zY0lVSEhhUHc2aEo2dzhaR1dqZVVUODE5c1ciLCJtYWMiOiIzNjg0YWI4NzM1MzMyYjRjM2YxZTM3Y2I0ZDAzMmE2ODRjZDJjZTFhZjNhMDMzNTMzODBiZjRjZjMwOWM3ZjhlIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 00:21:14 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6ImxGdldDdGdnRmtZajJYcFlMYnlXT0E9PSIsInZhbHVlIjoiOTJ1YlJ5NWRCaGJHSnZUVFpXL21tOFk0TXNmMGdVM0ZoQ1dHbUxoTVhBRDZaMlJKbzFsNHZTZGpwVU80TDNDOU1Pc2dsVHRjelozb2ptQlNPS21pMTAxaFBmUWcrcVUzWlZRQVVEYytiOEdnT2NiSldMWHh4NjJuWWtyaWtGMTVWbldCN01DRFdJVHJmendJcDBIbnhxZmFjakVhNHdhdEkrVTMwWTRiRGlCaFdHYzZmSTB1MVAvNlZpcDliMWtydVBFVXhGM0tWLzlHMUFDVlRkZlAwMjNlVzNiMVdCeDNCTTMwL292SXFHL3NqU3hTZDQwcFp2Nkw4bi9yME5qaHlFQ3dSbFFFZVR5N3B6TDQ0MGdWSkJpWE1xZkMyMUk2L1lQNkVadmVkdXl0cXQxTTh6TjErdWhaZmJVbW9mNHNIQ2lUWjVaMHJFSWR5dkVzQlNDWjlkeDNYN1Z2NTlObWRVMDNDUE5iUDNkdGFPUU8wdjdsNHVjZmZlclV0VTFIVEM5K3JMNTM1Z3Qvb2hlSG5uSE8zWEthb3l2aHFmeW1RaVVsbDBYd3VwZ3Irc1U3QlMzRmtFT2g0V0czTW1zMTl4dlFQdGFMamcxSU9kK2FIRTZtYzMwWFBRQzdwVXlHTDNHR3BnNHhlbWxEbHBzZGFzNCsyUUg2Y2M5UVpXQ08iLCJtYWMiOiJhYmY3ZDc0ODY0MDU5YTIxODExMDg0MmQ1NDBhNjIwODgzNzM1OTgyZmE2ZTg1MmUzYmM5ZDAxY2I0ZjIzODU4IiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 00:21:14 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6IlNXS0JEVzJZRUdZRDRvSXRvYW5qZmc9PSIsInZhbHVlIjoieVBpc1NPaEJTUnlDZkpnbE1kRnpsZkllRVJsK2NTVDlFQjJMTE0zLzN3Y2VDd3ZGdUFTdW9SK3ViTzU5UjFhT3YrNkNxL3Y1eE9UK1lKanQ5dkVEZ2twM1JCU1B6MVk0ZEVqdWc4aW9Iai91OUIxaE5FWjJXSW4yVS9JVjRvZVVka1lhR2x5QmZXRno5bnM2a2tyT3Q4QlBSUzhuR1pXNjU4aVJlT0k5TFowTmVsUVA5SFFSMkcrOFdESEVyeCszcFR0bHdHZ0tqMU9qNTVKNDZlQlpOZnBHR2lUSnRib0VCVmNlTDNkbmFWMC9tWi9RL3gxWWt4bUVFVk9jUWZ6Vkc3d201WXc4dXZiaXQ3bDVCZDgzSmV2V2xIb3VLbVBNNEU4Mzd2Sm9aaVpVd1NjQ0pnWmhmWmtFK2x3cFZaWGNKUDVIMWFES1RKWUFPSkJCMDdVWEQxV2lublpMeXFNRk9rcUVPQ2hGRlhOOG5Pd2JhTnFTd2tVcU05VXFTUTdsTmlPalFpQk5OS0pDOVZUSXZ5dVYxSVZJVHZrdkE2dzl4b1ZGanRDNUw3NjF4QmRNbjlkMHJwZGVnb3p4ZkxDeG10V0lZUFpaSHM4bTAxZDZMbEZrTnpIeWJVTHFEMU44OGJVQk9zY0lVSEhhUHc2aEo2dzhaR1dqZVVUODE5c1ciLCJtYWMiOiIzNjg0YWI4NzM1MzMyYjRjM2YxZTM3Y2I0ZDAzMmE2ODRjZDJjZTFhZjNhMDMzNTMzODBiZjRjZjMwOWM3ZjhlIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 00:21:14 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-96752458 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">iD3eOztlFQTk9Wu93brk2PH5kNLllwdWSLzbHLjf</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"30 characters\">http://localhost/hrm-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>16</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-96752458\", {\"maxDepth\":0})</script>\n"}}