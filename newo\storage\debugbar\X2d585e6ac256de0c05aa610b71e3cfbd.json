{"__meta": {"id": "X2d585e6ac256de0c05aa610b71e3cfbd", "datetime": "2025-06-07 22:19:55", "utime": **********.146477, "method": "POST", "uri": "/chats/getContacts", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749334793.772346, "end": **********.14652, "duration": 1.374173879623413, "duration_str": "1.37s", "measures": [{"label": "Booting", "start": 1749334793.772346, "relative_start": 0, "end": 1749334794.967148, "relative_end": 1749334794.967148, "duration": 1.1948020458221436, "duration_str": "1.19s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": 1749334794.967176, "relative_start": 1.1948299407958984, "end": **********.146525, "relative_end": 5.0067901611328125e-06, "duration": 0.17934894561767578, "duration_str": "179ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45052640, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/getContacts", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getContacts", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "contacts.get", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=333\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:333-390</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.00931, "accumulated_duration_str": "9.31ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.066462, "duration": 0.00613, "duration_str": "6.13ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 65.843}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.1002998, "duration": 0.0011, "duration_str": "1.1ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 65.843, "width_percent": 11.815}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (15) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.119779, "duration": 0.0020800000000000003, "duration_str": "2.08ms", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 77.658, "width_percent": 22.342}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "cN6pwMHluqMWM9hLaRtOtRasnQF8pfN3ghxjsOfa", "_previous": "array:1 [\n  \"url\" => \"http://localhost/financial/productservice\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/getContacts", "status_code": "<pre class=sf-dump id=sf-dump-1572952845 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1572952845\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-24816297 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-24816297\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-43699298 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">cN6pwMHluqMWM9hLaRtOtRasnQF8pfN3ghxjsOfa</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-43699298\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1719053948 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">http://localhost/financial/productservice</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1993 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=a091af98-270d-406e-ab7e-33a6e610d6e2414686; _clck=nups8l%7C2%7Cfwk%7C0%7C1960; _clsk=ij6lzq%7C1749334756217%7C8%7C1%7Ck.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IkJPRExGM3ptNmNuNXpJTElrZ1VMZVE9PSIsInZhbHVlIjoieVcyU3pwSE8vMS9sNnRVZkRsaXQ4OEMyMlcxWCtQc3ZzdzMvdW0rank1U05rQUdXLzdqRHppQlA1WDI4UTFJamtwcUZGUm5IdHNlYVNBWldrQlJadUliRXdXN2daV1doeFM0MFZ6aDdaejlZZGF3ajhpSjhjRDhkem1qMGdlMjBIWWVZMEdLUjJZTDVHMHdsSmdMM1B5aS9Kcy90MnBwYktsYVc2cGVQNHIzMDh2ODB2dVh0SFphcDJ2NGk4K2ZGOFlkWjh4eWEvcnNNcExCZHhTcTZCcndNdHhqdHhDeFh3MkRrZlNYcVY5d2hrZGZLdlRlRklJMjIvU3F6NTljQlBMcEVpZ3I4bUlwOXlRWmxqSXZlUFVGYVZCdHNCQm9YZjlnZFRGOVllYmJNM3c3MWdTbTg4aXZObk40OWZWdTJvWGNFNDJ4TnNETk1tU1BMSDFBK0ZOVzFMWFg2KytrU0dMOWJ6RHV0SktiUTBYbkFLTzVqQ1RsSzIxREpscmJnMllhYjBGekZBanhRd25Wd0t2aUFiQ1dHVDgrbWN0d29RbjA4bDFlL0dKZ1NTeW51ZGJkcFVTSnNTQzdKbnNwY1JQVjhYNkhXTkhwQmpETTNrZW52N0NHdkEwOTkwWTdTSkxkNUxYTmlRQm5lYnNiRUdTWjZSOWNWbk1PdlJYQ2kiLCJtYWMiOiI4MzY4ZmIwMjJkYTc4YTQ4OTExZmFkN2U4MmRmNjUzNmZhNTFlNzE4NjhhMWFmMzc2YjcwNzc5MzBlNTA5OGVkIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6IkY2L0VYSlA2NUxCcW9MM3RHelNzaEE9PSIsInZhbHVlIjoiVHJUZGZtSVNmNFlvNG5zUDdqZGk3aVJPNjFlbzBPa0w0dS9CRVp1cnNpU3VDTy9lSCtPSEkwbGRqL1pacTlhMU0wVzdQNGtrR1VLSGRYSVdHaW9TYVRRYjlYZkovZGRvQVQ4dm9URFhRVE1RZFBMRXhyYkwvVmVHSWlrbVNmdENMLzJjU0gxUWgwWGNlQlVlTUUyQXVLWnQ3ZGtHMjFHZGFJS014cGdPdlBOcFJUVXVkWXBBWERKZFpDZ0Y2RkhoektuUWhBM0p6T1ZySmxGQWV5YzYrTGt1ZE41UG9XZXhOTHUxR3ZkemVyam9saXpwRld0QXpMNFB2VE9lWE5VenFPbWFtVVBwVEcyZUpxZ2ZsbjZ5dXhieVY4bkliTzAvM3hOcmp6b29GWmxYNWVnWkxmRCtkVU9EcDU2Z2lPa3ltN0hMK3VSUVU0N3B4bVh1Ui9sQ0Yva3dCbjVGK1A0RC9EaC95SVp2K25oRk5ET0lrYWRXMlZ0RGRMMmdqS2wwZGYwM3ZMNFhBTjg2bVcxRC9Sa1FVVzh6bk02d2ErYkU3SnlqM3BOcjByQWFyY2ZrRFA2S2RleGY1ajN0T2RsWWZGY0I1M2lmaXFuM3F2eG1zaHlyMHVIR0NsYjFleE14SlpGR3NDVFczSFJqaWlraTFWZkF4T0ViMS9SQTdwbEUiLCJtYWMiOiJkODQwN2E5OTRhZWJjMzQyOGU5ZWFhMzlkMWRiOTZiZjc3NWQ0MGNiN2IzZmFmYzFjMGFiNDczMTEzOTU1MDc1IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1719053948\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1632309027 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">cN6pwMHluqMWM9hLaRtOtRasnQF8pfN3ghxjsOfa</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Eo35AMmp3Bkf2zsyHLroae0N6MdUih7xJ0ojLwSF</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1632309027\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1620484839 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 07 Jun 2025 22:19:55 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IkN4TTNRVWd0cUIxaVFOaDgwWDFtT0E9PSIsInZhbHVlIjoiNmxrQU1rS0FKRHFkanNJek1DNktIVVFRV0VLVmM5bFhqQUtLdEdzUDljclRTdVIzZkpNa3Uvamd0TS9rRng4NHJVRkZUeERGQzB6OWlnQTRDR3FBZlFycit5OFF5eVpsV2VzbkQvUUVxMUxGVFBKWUdTMEhDRU1sNWZaSk51NE8xN1FCbU1Rai8veHJ4eFFaSTV3QjNXVERzL2IwUXZNbWt4TkdRbmVVOU1tVlBJVmtZY0RKRWxNM29tQnovb1cxWXFGVHhaL2gvWFQzQm5tT21BQlJVMmh1Si9ZNG5qa1QwOVZlSTdaeXFqbDJSaC96U08xTUc1elNEcFgzZDVacmpEWWNCK3hvTGYwZzA0V2xMN2Z5WDRMRmdFSVZuYkVSZUhEZGVqTTEvVHdwYnBrNE9yWXpYZlBYbm9ib3pxQTd2ckNTck9TUjZQYVloYTRRTnFLVTNNeFMyT2VaenRtKzRzdXJ4N1JtWFJQWitPTWNGYWFQY041cHFxdVhMWmgwcmtTQlFOeDljV1NHdzBwSmk0ODF4NFAvakpscW9XLzBhUjM2MHdOYnpPL0d1N290VlJJQS9iZkhESkVrNkR4a2JqN1FoMnNlOVFlZStDK2VkY2NqUkkvNWhXSWNoS0RlalcvcW44L3NIQnRZUEtlV3I0MTYra0txQjNLNjhCdm4iLCJtYWMiOiJkMzJhYzA2YWM5YjI3MDUwOTFhZDk1MjFjZDgyY2FjMWZhMjcxZTQyNTE0MGMzZjE4YWQyNDE2ZWY1ODE3YzdjIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 00:19:55 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6Im1kREZZYWNmVExHNzh2bmtXWGRZUnc9PSIsInZhbHVlIjoiM1Q2M0JSb0kvOStpMHhRdm5adjYzR3huczYxM1RlZkdVaGpPYjhTRG1iTUI2aFUwVVM1MG5GWlg0dEo2Qm9jd29mWjIzQXA1K0JEUWxNaVhrOUZVUU5hdEw5UUE0MU1jYmY3TlZzME1aSWVMc2lXa1pMS0JlQmlWbkovVnpKOHBPczN5Rm1zZGUrZzA0U0p3SnJ4cHdXZFVtVVpMM2xndkwwazFxUWtDRk5pUTFSTm9WZm9HMlkrb0d3UnB4N3JkK1VWajhtcEtnZ3B3RktvRElPQ3V0OVJ5V3R3cU9CYkpJSFQxNDlkejNySjBVTGVKbUxzejF1QXBUTE1jZWhHWTlseUp0SzAxT2pMRDByWjdDekFCMSthZFV4NlZGUzM0TldLNnhKRlhKdFhyU2c5dmtLcFlDSWYvYlJQNGRHZlg4a0JoazVvbUl5R1Nxbmo0NVd3bkpyZ1cwQ2czc0c1aVViSXZUMW9tMGQxLysvNzAvd2QwMGh4ZnpQTjNOVzdYd1RKdmlPTDE2akYyVmRWN2NVdFhsMC9rSm5naW1GeUM2QkJiZXNvYnRpdWNaQzFpMm5EV2h6VGVQQ2t1enZJSlQ2dzVtdzZDdDVQbGJldTRRV1YwOGIvbkowY21QUG00MlRXcjdVaE80R0hQN0lxRjhtV3NTWldycUhYeTFpcmMiLCJtYWMiOiIxN2MyNGVmMzgyMGU5YzZjOWJkNDE2Y2YxNWMxOGUzNTUzZTY1OWI1ODM4NWE4ZTI4NDcwMmIwMjJhNDkzNGRiIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 00:19:55 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IkN4TTNRVWd0cUIxaVFOaDgwWDFtT0E9PSIsInZhbHVlIjoiNmxrQU1rS0FKRHFkanNJek1DNktIVVFRV0VLVmM5bFhqQUtLdEdzUDljclRTdVIzZkpNa3Uvamd0TS9rRng4NHJVRkZUeERGQzB6OWlnQTRDR3FBZlFycit5OFF5eVpsV2VzbkQvUUVxMUxGVFBKWUdTMEhDRU1sNWZaSk51NE8xN1FCbU1Rai8veHJ4eFFaSTV3QjNXVERzL2IwUXZNbWt4TkdRbmVVOU1tVlBJVmtZY0RKRWxNM29tQnovb1cxWXFGVHhaL2gvWFQzQm5tT21BQlJVMmh1Si9ZNG5qa1QwOVZlSTdaeXFqbDJSaC96U08xTUc1elNEcFgzZDVacmpEWWNCK3hvTGYwZzA0V2xMN2Z5WDRMRmdFSVZuYkVSZUhEZGVqTTEvVHdwYnBrNE9yWXpYZlBYbm9ib3pxQTd2ckNTck9TUjZQYVloYTRRTnFLVTNNeFMyT2VaenRtKzRzdXJ4N1JtWFJQWitPTWNGYWFQY041cHFxdVhMWmgwcmtTQlFOeDljV1NHdzBwSmk0ODF4NFAvakpscW9XLzBhUjM2MHdOYnpPL0d1N290VlJJQS9iZkhESkVrNkR4a2JqN1FoMnNlOVFlZStDK2VkY2NqUkkvNWhXSWNoS0RlalcvcW44L3NIQnRZUEtlV3I0MTYra0txQjNLNjhCdm4iLCJtYWMiOiJkMzJhYzA2YWM5YjI3MDUwOTFhZDk1MjFjZDgyY2FjMWZhMjcxZTQyNTE0MGMzZjE4YWQyNDE2ZWY1ODE3YzdjIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 00:19:55 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6Im1kREZZYWNmVExHNzh2bmtXWGRZUnc9PSIsInZhbHVlIjoiM1Q2M0JSb0kvOStpMHhRdm5adjYzR3huczYxM1RlZkdVaGpPYjhTRG1iTUI2aFUwVVM1MG5GWlg0dEo2Qm9jd29mWjIzQXA1K0JEUWxNaVhrOUZVUU5hdEw5UUE0MU1jYmY3TlZzME1aSWVMc2lXa1pMS0JlQmlWbkovVnpKOHBPczN5Rm1zZGUrZzA0U0p3SnJ4cHdXZFVtVVpMM2xndkwwazFxUWtDRk5pUTFSTm9WZm9HMlkrb0d3UnB4N3JkK1VWajhtcEtnZ3B3RktvRElPQ3V0OVJ5V3R3cU9CYkpJSFQxNDlkejNySjBVTGVKbUxzejF1QXBUTE1jZWhHWTlseUp0SzAxT2pMRDByWjdDekFCMSthZFV4NlZGUzM0TldLNnhKRlhKdFhyU2c5dmtLcFlDSWYvYlJQNGRHZlg4a0JoazVvbUl5R1Nxbmo0NVd3bkpyZ1cwQ2czc0c1aVViSXZUMW9tMGQxLysvNzAvd2QwMGh4ZnpQTjNOVzdYd1RKdmlPTDE2akYyVmRWN2NVdFhsMC9rSm5naW1GeUM2QkJiZXNvYnRpdWNaQzFpMm5EV2h6VGVQQ2t1enZJSlQ2dzVtdzZDdDVQbGJldTRRV1YwOGIvbkowY21QUG00MlRXcjdVaE80R0hQN0lxRjhtV3NTWldycUhYeTFpcmMiLCJtYWMiOiIxN2MyNGVmMzgyMGU5YzZjOWJkNDE2Y2YxNWMxOGUzNTUzZTY1OWI1ODM4NWE4ZTI4NDcwMmIwMjJhNDkzNGRiIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 00:19:55 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1620484839\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1413016499 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">cN6pwMHluqMWM9hLaRtOtRasnQF8pfN3ghxjsOfa</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"41 characters\">http://localhost/financial/productservice</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1413016499\", {\"maxDepth\":0})</script>\n"}}