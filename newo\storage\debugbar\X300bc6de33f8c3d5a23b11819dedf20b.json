{"__meta": {"id": "X300bc6de33f8c3d5a23b11819dedf20b", "datetime": "2025-06-07 22:20:07", "utime": 1749334807.067634, "method": "POST", "uri": "/chats/getContacts", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749334805.722568, "end": 1749334807.067671, "duration": 1.3451030254364014, "duration_str": "1.35s", "measures": [{"label": "Booting", "start": 1749334805.722568, "relative_start": 0, "end": **********.876939, "relative_end": **********.876939, "duration": 1.1543710231781006, "duration_str": "1.15s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.876967, "relative_start": 1.1543989181518555, "end": 1749334807.067675, "relative_end": 4.0531158447265625e-06, "duration": 0.19070816040039062, "duration_str": "191ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45052240, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/getContacts", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getContacts", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "contacts.get", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=333\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:333-390</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.02472, "accumulated_duration_str": "24.72ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.971, "duration": 0.0225, "duration_str": "22.5ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 91.019}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": 1749334807.023757, "duration": 0.00109, "duration_str": "1.09ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 91.019, "width_percent": 4.409}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (15) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": 1749334807.04279, "duration": 0.00113, "duration_str": "1.13ms", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 95.429, "width_percent": 4.571}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "cN6pwMHluqMWM9hLaRtOtRasnQF8pfN3ghxjsOfa", "_previous": "array:1 [\n  \"url\" => \"http://localhost/users\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/getContacts", "status_code": "<pre class=sf-dump id=sf-dump-788701572 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-788701572\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-802918803 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-802918803\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-17694945 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">cN6pwMHluqMWM9hLaRtOtRasnQF8pfN3ghxjsOfa</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-17694945\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1976802497 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/users</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1993 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=a091af98-270d-406e-ab7e-33a6e610d6e2414686; _clck=nups8l%7C2%7Cfwk%7C0%7C1960; _clsk=ij6lzq%7C1749334794107%7C9%7C1%7Ck.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IkpWa0NZNjJXN2REcHE2bGI3aEVCTEE9PSIsInZhbHVlIjoidXdQTk9acGNKNW8xVWVWQWFsTm9qbEV2R25nK2h6Si96S3AwWGVtTFpvRHdpdml1RG54V1VUTEk0RmNKYzlsa052YTdmY1VyZURmQ3VLaWNCUjE5dGF6ZWl5Z1pVVEpEWVc3RHlQZzhBOE4zNDY3NDR4Zll5VnlSL2JwNWVmVUJKaTU0N0NLenVzUHJ3MWo1cmE4Z2ttdkFzTkI1WWNBbStXbHpGbzkyTDI0NWhYVERvSVRmQlc2RWRzbUpUTEZmbCtIaTd3N1Q0ay95UCsvVk9OS3hjbEpiQVkwUlJwK21BcWdUYlpXU080cVNiRFZCd0ROam8yRnFHbXdHZHlVVUpIb01ML040SDQzdnJYbGxxb2d5ellvZFQzc3hwY2R4Tk9HM25HY2NVaWJ2WFBpcjdVMkIvV3lQUnhncVdkSnlkYURBSHdSbXJXTlRIS2tBVVFUS3VUdHY1NEs1TU1VMUdYLzBzNHBSLy9jTGpWVWkvM0lUclJ6eFRQY3AzbDRuL2RJak5ZTnVjWi9nQ3JDWTZ5dVBnK1ZOeTBKbVZJZEVCcVZmSysvSzdTcmx3QzNoNExSL3JZNHVZL2dTMkswYTZ3R1lEa0lMd01oR3dHS243eUxINnUrRndwd2txb0JkTUhmZjQ4OXhkSHpHMUk2b0NVM1RNZERub3ptUlpxZEIiLCJtYWMiOiI5YTIwMTA2ODQyZTA1YjViOTExMzgwZjc4M2RjYzE2ZTQyYzQxZGNjOGEyZDJlZjA0NjI4NGQxNWM0ZjEzZDc3IiwidGFnIjoiIn0%3D; test_session=eyJpdiI6InFSVnFiS2JzZ2lkendVRmtQSkJFZHc9PSIsInZhbHVlIjoiWis5blFoU3NlQndXNXgxWlpDWHF5R1FaWWhyRm52UEZwVmxZMlh5MkY1OWw2Nk50czVVQXdzWnNoVllVYTcvblFMQjNXbEhSNlRCTVZYVWk2d3ovRnEyY3JsVVhLak5LaWxBWm5pa2UvYUZvWmxmNkdqSjFidzhuVHFuR1RGK09ob3J3L3pYS3YrVnpHYTdncjd2WTVMQlFQYW1TR1pFWUErYU45TDkxU0JubmJFV0daOTZBQVdjS1VGOWRLaGgvZFVCWU1IMEsvcEl5OXJxSFpOMENOUThtbFZVZlZDMzlzUHdseFNqRi9HQ0pXYXQ3NVY1ZjdpK0VmSkwwWTdYaytQR0dQOVR2RldiWnZNaG5SbVVnWGJudUNjKzRWN1lhNHVQWlJCd0E5d2oxd1JuVUdnT2VFVW04MEdHZHNvRmpMSlNNOGtXRnZmU1JUL2VSUUNBR2xzaDlxZmJ3Y1ZrdE5maWNRMHA1ekU5UXhwUGVVNXluTytGQmtLOWZNTHVwc2Y3dTBSaUg4bzhWeGg4R0hIakYwamhFbE9WcVM4aVFiWVVoMi90SitXY0dWSHQ2ZGIrYkpxNzVFY2pyd1A4eGk4L3p6K0NNd1QvTzZFOTJHdG42dGo0cmxVTFJ2L210QVd6clhZd1k2bG1BRjVRYnIycjJnWG1Bd0hIMWE4TVAiLCJtYWMiOiI0MmVmMzc4YWM5M2M2NzE1NDYxYTg2ZWIxYmQ3ZjhmNGZjNTY1OWQxNWMwNGMwNzhhZmRjYmJkN2ZiM2Y3YzRmIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1976802497\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1091639660 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">cN6pwMHluqMWM9hLaRtOtRasnQF8pfN3ghxjsOfa</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Eo35AMmp3Bkf2zsyHLroae0N6MdUih7xJ0ojLwSF</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1091639660\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1413767195 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 07 Jun 2025 22:20:07 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6InRLclBnS215YzNSYnJXam55NEpuZmc9PSIsInZhbHVlIjoiQ2VNRXRZZi9yL2hwOW10b1ZoNXAwdWQwWjZmRzNxRjZGRTYrNGh4cVZWWjB5MTRIYnZHazltUUk0a1NkM1NYZjc4dFEzanpoeWdRbHk4R3R0cmpHb25JcmlYVytjSjJSbFl1MWdaS2k2OEdzdzdDMUI0Y0J1WUw1REg0cVRLczRDYkxSN0JXMTUxdG5kdjE5Q28wSFRteEVDZ0JCZDI4VExmRU1HSUNTMi9sNkFTbjlHVk8yWURuMnRRamFBRUtNUlhMR2pYaFB4UTlkNmFqdGU2TXBsQmFCbFF0RDArRHlLbGhFSVU5eEFuQ2FRTkZjTnZpMVRKcnZRSTZoNkZaMXRsS0NiTk1obktIbGpCSk1uWU1CK1p2NjdIYlVoS1Z1eldiZmx0YTZzT0QwbXA4cG4vaGJiaDhtWkhSOFdORzA2cHJ1MHpWU3A5eE50Nm5NMVJCRDNTbVBacjV2cmRBYkN1dXlZa0ZuWmhYU0swZG5LT25VWnRlRFJLZ2ZVVDNnNnJ2SFpzOTVXTm9sdkZ6Um1Qc3JrOXRzTDZvQ3pkblBDVFNRU0VOblV2cFVxNWlSSjlDL2UydUVMOGNnaWlUR01iK3lvWkllcHJiYks5TUdHbXRQZzlzblpKUjR1ZWQ1OWQyOFZVSXBETS93ZVRIcUU5SEk2c1FoekJBYXp0TnkiLCJtYWMiOiJkN2QzOGMzY2IwYjllOWRiNDBhZGQ3YjQ5OGMxZWM3YWNkZjVjMDU2MjU2NTRhNjEyNmRmNjk4OWI0MWY5ZDAyIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 00:20:07 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6IkpsOTFhakFuWHZod05sdnFkemZTOEE9PSIsInZhbHVlIjoiYXE2TUpRM1pRL0xkcE84U1E0NlRNa3NwYURjbTFqSVZHdjJoeTJUSXRMam5MbkRVNTdZam95aWRsUE4xVGxpWDFrZ056L2tZUW1tSEQyYnk1RjVJQXRoUVc1UEdRMFRsWE10ZmliN2pSTjhwUnlXNmE0ZXhraHlDbUtRTXh1Q29qWEpxbFl5N3V0TjFHQ1BsN250MmhIdXFDSW1tWUYyRzVmYkVvaSttdEtTVmNMREI5VHFJNHd2dnhNWjdORndYd0lIdWlyN2VMZEhBaTNFVlZOVTZlVUFFY0pmNE45alJtOWtmemdpeW9XdWNtc01qUFIvUk5EZEdJQ3E2bUlMYWhnV25qRERpa0M5dC9Sd3ZLNGhPTng2SFhrZE1kcHBFSnB3d2hZZnFMaUtKb0ZXWDVpd3FHNXhMcEI4endJRFJ0SHpPdVBxVFg3Z2EzWXRxMFBsNGs3NGk5Rjlvelhra2tpTDFUcnkzRWJIaUhrb2I3dnJlNmNXSzJHMlUyR3NmRldiK2YxcHErS0V6RmN5RndTTTVRL0krcjlWemM2b3hGZFN5T1NiVG5yNnhaUXV5aGRoMVVRYVNpVUJXcnZxM0dDd1ZyUURkSUh1QU90TmF1RjZ5L1NEQ3d2VXVjbURibUd2SzMrWjk1VlErS2JSUE1HOUQyYkxFZWN5bTF4ME0iLCJtYWMiOiIzNTRiOWQ3ZDA0YzhmNjFkNzUzZjhjNDRlM2U0YjE0NzliYWVjMDE2MGI1ZTA4YmQwNTg2YzlhYmUyNDAzNGEzIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 00:20:07 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6InRLclBnS215YzNSYnJXam55NEpuZmc9PSIsInZhbHVlIjoiQ2VNRXRZZi9yL2hwOW10b1ZoNXAwdWQwWjZmRzNxRjZGRTYrNGh4cVZWWjB5MTRIYnZHazltUUk0a1NkM1NYZjc4dFEzanpoeWdRbHk4R3R0cmpHb25JcmlYVytjSjJSbFl1MWdaS2k2OEdzdzdDMUI0Y0J1WUw1REg0cVRLczRDYkxSN0JXMTUxdG5kdjE5Q28wSFRteEVDZ0JCZDI4VExmRU1HSUNTMi9sNkFTbjlHVk8yWURuMnRRamFBRUtNUlhMR2pYaFB4UTlkNmFqdGU2TXBsQmFCbFF0RDArRHlLbGhFSVU5eEFuQ2FRTkZjTnZpMVRKcnZRSTZoNkZaMXRsS0NiTk1obktIbGpCSk1uWU1CK1p2NjdIYlVoS1Z1eldiZmx0YTZzT0QwbXA4cG4vaGJiaDhtWkhSOFdORzA2cHJ1MHpWU3A5eE50Nm5NMVJCRDNTbVBacjV2cmRBYkN1dXlZa0ZuWmhYU0swZG5LT25VWnRlRFJLZ2ZVVDNnNnJ2SFpzOTVXTm9sdkZ6Um1Qc3JrOXRzTDZvQ3pkblBDVFNRU0VOblV2cFVxNWlSSjlDL2UydUVMOGNnaWlUR01iK3lvWkllcHJiYks5TUdHbXRQZzlzblpKUjR1ZWQ1OWQyOFZVSXBETS93ZVRIcUU5SEk2c1FoekJBYXp0TnkiLCJtYWMiOiJkN2QzOGMzY2IwYjllOWRiNDBhZGQ3YjQ5OGMxZWM3YWNkZjVjMDU2MjU2NTRhNjEyNmRmNjk4OWI0MWY5ZDAyIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 00:20:07 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6IkpsOTFhakFuWHZod05sdnFkemZTOEE9PSIsInZhbHVlIjoiYXE2TUpRM1pRL0xkcE84U1E0NlRNa3NwYURjbTFqSVZHdjJoeTJUSXRMam5MbkRVNTdZam95aWRsUE4xVGxpWDFrZ056L2tZUW1tSEQyYnk1RjVJQXRoUVc1UEdRMFRsWE10ZmliN2pSTjhwUnlXNmE0ZXhraHlDbUtRTXh1Q29qWEpxbFl5N3V0TjFHQ1BsN250MmhIdXFDSW1tWUYyRzVmYkVvaSttdEtTVmNMREI5VHFJNHd2dnhNWjdORndYd0lIdWlyN2VMZEhBaTNFVlZOVTZlVUFFY0pmNE45alJtOWtmemdpeW9XdWNtc01qUFIvUk5EZEdJQ3E2bUlMYWhnV25qRERpa0M5dC9Sd3ZLNGhPTng2SFhrZE1kcHBFSnB3d2hZZnFMaUtKb0ZXWDVpd3FHNXhMcEI4endJRFJ0SHpPdVBxVFg3Z2EzWXRxMFBsNGs3NGk5Rjlvelhra2tpTDFUcnkzRWJIaUhrb2I3dnJlNmNXSzJHMlUyR3NmRldiK2YxcHErS0V6RmN5RndTTTVRL0krcjlWemM2b3hGZFN5T1NiVG5yNnhaUXV5aGRoMVVRYVNpVUJXcnZxM0dDd1ZyUURkSUh1QU90TmF1RjZ5L1NEQ3d2VXVjbURibUd2SzMrWjk1VlErS2JSUE1HOUQyYkxFZWN5bTF4ME0iLCJtYWMiOiIzNTRiOWQ3ZDA0YzhmNjFkNzUzZjhjNDRlM2U0YjE0NzliYWVjMDE2MGI1ZTA4YmQwNTg2YzlhYmUyNDAzNGEzIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 00:20:07 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1413767195\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1710639452 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">cN6pwMHluqMWM9hLaRtOtRasnQF8pfN3ghxjsOfa</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/users</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1710639452\", {\"maxDepth\":0})</script>\n"}}