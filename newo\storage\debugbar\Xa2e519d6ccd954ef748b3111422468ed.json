{"__meta": {"id": "Xa2e519d6ccd954ef748b3111422468ed", "datetime": "2025-06-07 23:58:29", "utime": **********.452954, "method": "POST", "uri": "/chats/favorites", "ip": "127.0.0.1"}, "php": {"version": "8.3.16", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749340708.514818, "end": **********.452999, "duration": 0.9381811618804932, "duration_str": "938ms", "measures": [{"label": "Booting", "start": 1749340708.514818, "relative_start": 0, "end": **********.286278, "relative_end": **********.286278, "duration": 0.7714600563049316, "duration_str": "771ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.286306, "relative_start": 0.7714879512786865, "end": **********.453005, "relative_end": 5.9604644775390625e-06, "duration": 0.16669917106628418, "duration_str": "167ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45046544, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/favorites", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getFavorites", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "favorites", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=454\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:454-477</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.022359999999999998, "accumulated_duration_str": "22.36ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.373501, "duration": 0.019989999999999997, "duration_str": "19.99ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 89.401}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.4173892, "duration": 0.00083, "duration_str": "830μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 89.401, "width_percent": 3.712}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (15) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.434142, "duration": 0.0015400000000000001, "duration_str": "1.54ms", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 93.113, "width_percent": 6.887}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "cN6pwMHluqMWM9hLaRtOtRasnQF8pfN3ghxjsOfa", "_previous": "array:1 [\n  \"url\" => \"http://localhost:8000/productservice\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/favorites", "status_code": "<pre class=sf-dump id=sf-dump-826847230 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-826847230\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1818584575 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1818584575\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-741545424 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">cN6pwMHluqMWM9hLaRtOtRasnQF8pfN3ghxjsOfa</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-741545424\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-102884890 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">http://localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"36 characters\">http://localhost:8000/productservice</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1995 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=a091af98-270d-406e-ab7e-33a6e610d6e2414686; _clck=nups8l%7C2%7Cfwk%7C0%7C1960; XSRF-TOKEN=eyJpdiI6Ii9IYkdPOTNRZlNtbEJkS3BpZ0JFaHc9PSIsInZhbHVlIjoib1hETUZDVTRDN2dhWjlDYzlBbWJGVStsVk9peFV4dDNXZU9OMnpwUXhTSXN1ZHpVZ216bmt5Ni9BVDFYR0svUEJpODFndzFwcmhUelNheVVINlF6dkNUaWlWWkpLN1VKd2F0S3J3dVhweUVRT3IwQmI4YUoyeUVVM1ZBVTdQSmNnY0RPRElYMVRQbzhJUXhtTXFwT05sT05RSDFZVjRaQ3NtVHhPYWxQL3RpYkFGTXJqcVNyaFFMckFqcWZyYUx0YmlqMmFFRmc3Nng5d2sxOTdEK3BuUmhwVWd5dGpiV3g3NFlsNlZJeXcyOXFhSVFyZ2RwRW0reDVDbGpveVFCYVlWZkxJMmdWUUVDZTJwdDJVSWJObTBNZURORFA5SEVQR2t3bGxuSkRFVU9rcW1LVjE1WTAyMFJGc0RFK2cxSDNJSlhYM1ltTDNTbWlFV2dCM2Y5d3REOUt3eHAyQkJSMVRSaGtUMWtoMWYvb0dMdzBnNHlYL3RsYmkybDNjTjdzZlJ5NWdHYXU4NVBmaDdjNm5Mcm9jMFZ6TFROTFE1ZU9iQVFoM0RnQitFK01DZExVSTNaY3BGMHJHM1IreDdHUGJUVWpacTcrcWpkUHhWNm9Ua1FXWkJEcGlCajBUbllieWw3MzYrbW10QzJlTFpzbmlmeVNvbVdKelFMT3NpYkQiLCJtYWMiOiI5YTJjNmMwZjUzZjRkM2Q1Y2E4NzcyOGI0ZTQ3MTFkNzQ4NzI2MzRlNTEyYTgzZjcxMmU4ODllNTg3NmJmNTY2IiwidGFnIjoiIn0%3D; test_session=eyJpdiI6IlRnRTlGTWxmL2NDelZIVyszTmltL2c9PSIsInZhbHVlIjoieFFPa2IxbkYwbEZYOWlEc1Axa0ZNcytzTkxvOG9VUWI3bW5hTXhFQmtETkRFZkZPSVZjVldTWTh4QVkzYUNwdGV4MjIrRG5ORSt6d2lFRThQTDJtdWpreFFBT0EvMHlDTEFBcjNqQlRxc0FZRE9ENG1rOGNjVWZUbmNMdGkrS1p3Y1M4UXR0SWxTemVTR0N1WDdzMjJTTDFva2UyZTRXc29YN1BEeG5jUVRBQnNrMzY2cWY2OE5vdXFNbytmNzM3TDVHNDF0NFRIU01FRFdjOFJlbjNMYUt3bWtzU2c3ankwMSt5a0l4ZzhRSE5jS3l5cTROOHMxazRlMEVKSS8yQnp3VWkyU2tkTmY3ZUVLT3hDc2todFVOUEdNN1NNSUlyZ0tJZm1QSllCaDN1NmpKOXhOWktkYUljUFRTaUU4VTYrSFV2NjFhMHRlRFNzZXJWMDRBTjJLNmlpaXBtTWhTaVdxU2Y0cE9qMWswaHBycnJSdTR4N3FmdnNENElUZGQreFNoeDI5NkVyRWRmajdyM0praFhVbGV6NTJDcUxYaFFhTlpCY2hkY2piWUJBTlBGU203STNWRmhjMTM3RytnZjBvL0t4N01sYXZOUDJYTGc1NUlXcnZCQXlKS0JleXM3Q29VbnBvMmMrcWsrZmNmdmJDQ1BOYXh1MlF2bTMxT2YiLCJtYWMiOiIyNzQ4ZDliNjdlMjVlYmZkYTNjM2IyZDgyNWM5YmUyZWY2ZDcyNGRjYmZiZGE3NGVmNWU3OTkyMmMxZjY4MjdjIiwidGFnIjoiIn0%3D; _clsk=1dgl8io%7C1749340706378%7C22%7C1%7Ck.clarity.ms%2Fcollect</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-102884890\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-676503280 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">cN6pwMHluqMWM9hLaRtOtRasnQF8pfN3ghxjsOfa</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Eo35AMmp3Bkf2zsyHLroae0N6MdUih7xJ0ojLwSF</span>\"\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-676503280\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-52457964 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 07 Jun 2025 23:58:29 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"39 characters\">http://localhost:8000/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IndLU3poN0l4YllmVUlLZ0Ivak9jclE9PSIsInZhbHVlIjoibFBGTWI3dUZWenE4UGhicDRGNUxEajByTUFLV2dkdStMQ2JuRnNKaExPanZaRGg5TmU4aTM4dFVRZ0ZmeVg2dlphRmcvMDNhdGRVYXFIMlpiN2xmOU9qUmg1T1VOWkxtOGs1dWNNRi9sTy9uS0cwM01kQTlGNzI3TDBGZWNrYWUzOXNITk5jZjVjblgxQTl1NTJDZ1JsNkRpRExlYUNieGR5SEw1a3pteEhHdDlnVjNyMVJNSmgydnFrNjdmWlZybnRWZFpmMmdCd0VsTzRVOVNTbnQvakgyeEltN2FoU1JPUWJEaVFEa0tIaHo0TU9mWHZPLythMmRWSUtsSGR0MWJqYnM0Uk1UQ2NScGt0RjBLWi9XRWxOK1ZzY0ZEMERkSzYxUU03eENaakF3emt3STFlMmRIYVRpMnY5UlhLNTVxQ1V1MWx2UU5QVllDWFBOTkR2ZUFSbHFjSzR3Vk5wUk01cCtOM2tYTzZBSTVvRVZyMmoxa09EUjg3NDZmV1NBa1Nsc2VKYkxLcGt1LzdiZ2czVXBFeWQvWkZzZVlkTjZYYm5WRnhjUlVlYmNVRFFQMFNKMUJWcUs2WHNFRkFIazBBSVo2R2ZhMWRFS0laaEhZbzlMYWlaWFVNL083UlFBcTVITGNDZmJKY1JGYThtWW9Zc0hlVk1uNVdwNkU0cHYiLCJtYWMiOiJmOWZhZjIyNjNlOTQ5NTcxZmNlMjczM2YzNzIzNTA4ZGNhODg2ODFmN2I1MzA5MWE3ZTlmNmM2YzhlMWY2ODg2IiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 01:58:29 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6InFLU01hb0FKWUhKNm9FOC9JQ2YrZnc9PSIsInZhbHVlIjoibDUreWpsVzJDQzA2bmpXa1JHWE83M1JYaDRDTkhLWFQ5N0ZEZjhhdUVmMllXOS9lU3VtYmtibzFPMExHdk5nME4waFZJTnBMaklWYXZ6bTdNWWdJaVpmWUF3dVhqVWxYVEZOZEdlbHVseXJOVGJJMW1GT00vT2V6dGVMV01mV2RrNURIYkFiMnRWaGhFNEEzeURBNDFiQkxxd3U4UTB5bVdKaFRCaHVQMDV3aC81b1BhcFhBczhpSW5CSHJXdWZFQ0RJS2U2dXVXbUZFTWlJTFdNSkJ4YitTSlJLSzRabjVYNm5pNnozVFZrMUZIclAzaWcxa1gzOEhWbDdGTlh3RGZmb3c4Nmo1a2liSkpQUEdVMExVYzVLOFZHYlJrQjFsR2dIQkRtdkxubEUzZWoyMk5aeGhkZEUwdDluQ1hLeEhkdEdqSnFVdUxzOXZCUHJKK2VsSFN5Yjhhb0IySHduN0tMbURsd2p2V2hJalVuS1RvWFg3ODFRNXRaenZIQnUyOUJIbldOcmplb1BmSk10Sk5jcGNSaHRuUy9PZU1mR0QvWlcwL2UzRC9aekVBMkM0dHZKL3FhQ0NBZGhPemdjNnU5Q3JMVHpOdTZQS1JudGh1c3NVSHkxSldneFJMYWVhL2twMkhqK0FMQlhmUzgwWWZwL1lLYjVkbXo5RVNtc1oiLCJtYWMiOiJlYzQzZGE2ZmY1ODBiZGI1ZjdlOGUwOTE1Y2Q0OGIzNjFiNGVhNWY3ZDRlZWI3MTZkNjZkYTNhZjBkNzJiYjA3IiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 01:58:29 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IndLU3poN0l4YllmVUlLZ0Ivak9jclE9PSIsInZhbHVlIjoibFBGTWI3dUZWenE4UGhicDRGNUxEajByTUFLV2dkdStMQ2JuRnNKaExPanZaRGg5TmU4aTM4dFVRZ0ZmeVg2dlphRmcvMDNhdGRVYXFIMlpiN2xmOU9qUmg1T1VOWkxtOGs1dWNNRi9sTy9uS0cwM01kQTlGNzI3TDBGZWNrYWUzOXNITk5jZjVjblgxQTl1NTJDZ1JsNkRpRExlYUNieGR5SEw1a3pteEhHdDlnVjNyMVJNSmgydnFrNjdmWlZybnRWZFpmMmdCd0VsTzRVOVNTbnQvakgyeEltN2FoU1JPUWJEaVFEa0tIaHo0TU9mWHZPLythMmRWSUtsSGR0MWJqYnM0Uk1UQ2NScGt0RjBLWi9XRWxOK1ZzY0ZEMERkSzYxUU03eENaakF3emt3STFlMmRIYVRpMnY5UlhLNTVxQ1V1MWx2UU5QVllDWFBOTkR2ZUFSbHFjSzR3Vk5wUk01cCtOM2tYTzZBSTVvRVZyMmoxa09EUjg3NDZmV1NBa1Nsc2VKYkxLcGt1LzdiZ2czVXBFeWQvWkZzZVlkTjZYYm5WRnhjUlVlYmNVRFFQMFNKMUJWcUs2WHNFRkFIazBBSVo2R2ZhMWRFS0laaEhZbzlMYWlaWFVNL083UlFBcTVITGNDZmJKY1JGYThtWW9Zc0hlVk1uNVdwNkU0cHYiLCJtYWMiOiJmOWZhZjIyNjNlOTQ5NTcxZmNlMjczM2YzNzIzNTA4ZGNhODg2ODFmN2I1MzA5MWE3ZTlmNmM2YzhlMWY2ODg2IiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 01:58:29 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6InFLU01hb0FKWUhKNm9FOC9JQ2YrZnc9PSIsInZhbHVlIjoibDUreWpsVzJDQzA2bmpXa1JHWE83M1JYaDRDTkhLWFQ5N0ZEZjhhdUVmMllXOS9lU3VtYmtibzFPMExHdk5nME4waFZJTnBMaklWYXZ6bTdNWWdJaVpmWUF3dVhqVWxYVEZOZEdlbHVseXJOVGJJMW1GT00vT2V6dGVMV01mV2RrNURIYkFiMnRWaGhFNEEzeURBNDFiQkxxd3U4UTB5bVdKaFRCaHVQMDV3aC81b1BhcFhBczhpSW5CSHJXdWZFQ0RJS2U2dXVXbUZFTWlJTFdNSkJ4YitTSlJLSzRabjVYNm5pNnozVFZrMUZIclAzaWcxa1gzOEhWbDdGTlh3RGZmb3c4Nmo1a2liSkpQUEdVMExVYzVLOFZHYlJrQjFsR2dIQkRtdkxubEUzZWoyMk5aeGhkZEUwdDluQ1hLeEhkdEdqSnFVdUxzOXZCUHJKK2VsSFN5Yjhhb0IySHduN0tMbURsd2p2V2hJalVuS1RvWFg3ODFRNXRaenZIQnUyOUJIbldOcmplb1BmSk10Sk5jcGNSaHRuUy9PZU1mR0QvWlcwL2UzRC9aekVBMkM0dHZKL3FhQ0NBZGhPemdjNnU5Q3JMVHpOdTZQS1JudGh1c3NVSHkxSldneFJMYWVhL2twMkhqK0FMQlhmUzgwWWZwL1lLYjVkbXo5RVNtc1oiLCJtYWMiOiJlYzQzZGE2ZmY1ODBiZGI1ZjdlOGUwOTE1Y2Q0OGIzNjFiNGVhNWY3ZDRlZWI3MTZkNjZkYTNhZjBkNzJiYjA3IiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 01:58:29 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-52457964\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">cN6pwMHluqMWM9hLaRtOtRasnQF8pfN3ghxjsOfa</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"36 characters\">http://localhost:8000/productservice</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n"}}