{"__meta": {"id": "X4109dc0c7048d71a3c112150171e5263", "datetime": "2025-06-07 22:38:16", "utime": **********.298715, "method": "POST", "uri": "/warehouse-empty-cart", "ip": "127.0.0.1"}, "php": {"version": "8.3.16", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749335895.294575, "end": **********.298744, "duration": 1.00416898727417, "duration_str": "1s", "measures": [{"label": "Booting", "start": 1749335895.294575, "relative_start": 0, "end": **********.165399, "relative_end": **********.165399, "duration": 0.8708240985870361, "duration_str": "871ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.165417, "relative_start": 0.8708419799804688, "end": **********.298747, "relative_end": 3.0994415283203125e-06, "duration": 0.1333301067352295, "duration_str": "133ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45270608, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST warehouse-empty-cart", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\ProductServiceController@warehouseemptyCart", "namespace": null, "prefix": "", "where": [], "as": "warehouse-empty-cart", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FProductServiceController.php&line=1585\" onclick=\"\">app/Http/Controllers/ProductServiceController.php:1585-1595</a>"}, "queries": {"nb_statements": 2, "nb_failed_statements": 0, "accumulated_duration": 0.005059999999999999, "accumulated_duration_str": "5.06ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 16 limit 1", "type": "query", "params": [], "bindings": ["16"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.251905, "duration": 0.0042699999999999995, "duration_str": "4.27ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 84.387}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.2806318, "duration": 0.00079, "duration_str": "790μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 84.387, "width_percent": 15.613}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "iD3eOztlFQTk9Wu93brk2PH5kNLllwdWSLzbHLjf", "_previous": "array:1 [\n  \"url\" => \"http://localhost:8000/pos\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "16"}, "request": {"path_info": "/warehouse-empty-cart", "status_code": "<pre class=sf-dump id=sf-dump-540189684 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-540189684\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-1343375640 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1343375640\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1352920940 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>session_key</span>\" => \"<span class=sf-dump-str title=\"3 characters\">pos</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1352920940\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-105327523 data-indent-pad=\"  \"><span class=sf-dump-note>array:19</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">15</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">iD3eOztlFQTk9Wu93brk2PH5kNLllwdWSLzbHLjf</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">http://localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"25 characters\">http://localhost:8000/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1938 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=17ceeen%7C2%7Cfwk%7C0%7C1960; _clsk=1yftgw7%7C1749335881083%7C6%7C1%7Ck.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IjlaZEpxNnNXdUxlYnRaTFNRaE5KWkE9PSIsInZhbHVlIjoiRk1xeW50dUJnUGxTclJWOTNSajVzV1duaUgvZndraE9mNXVuZnJxY1NjM2pJR2VCWEZDa2pRbzBGbVd1NmlGOXdPbEVDQm04WTloN2JVcUZ0SWYrNXBYQ05tWHVBcGMzaFNwYUdValpmbkJyZ09xYUZaaDBPenE5T2dLL3U1ZXZUOEM1M3QxR0h3M2VWL2VNc015bTFhV2cwTUM0SjJJTFVwTXp1djFJeEJhRXRlSlBQeitlQzY5ZFkvYmltMHBjZVZnQnI0UGwyM2kyMlNXczRTTUVlaW1Ea21wcjJnR3duV055ZHpnSmdndjBNOUR3TExmc1hjOFFGUzBNaWR5bjM3TmJhUDdMYU5uUUJWZVRIeTZkKytXWjk1WmI5cXFyZS81a0swVmlGdnJrZjZRaXhjNHBEdzRIb1ZFazFDSmlCc01xSGYrMlQ3TkltUHZIa09qZkgvRjA0MnFuYjZFYWxqVE41cDJtMFptUCtTeHFCdnB4Wm00UzNqbG1pMzN0ZnAyYkJKWDZiQ0c2WFdoRGpGcVNsMEFOZk5mS1JiQmZueGFFOHNtdEh1dmVaRW03NFhpOUoxZUFubE92d3o2SnFLcFJMNCt1Wi9McHFCTDhqMkFmdlVhZGdpd091UmNBbTFjMjVaNHY2bkFKTmUyeHJTT053YXl4VUFweFhMWjEiLCJtYWMiOiIwYzUxYjIzNDYzYzYxMDBhNDZmZTRjMzdjMjFlZjIzYjM5MzU1YjhiNTkzNzk3NjA1MTk4MzQ4NmY3MjExNzFhIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6IjRLUnE0Vm9CSDJEZHl6b0lDMEVjQnc9PSIsInZhbHVlIjoiMWtMdFk3azVYOWxmb1djamFnbDRvQ1FxK1hJc3Foa2dxaWNuR1ExRjFHNFlYbmNxL3R4UGZmeVBQd05wNlNzY3E1MG1VV2NpdEIwcEwyWWlQMk95VHE5VzBrbUVSeXZxcEJFM0JYc29wdTBuK3Z3S09vRVBnVXNnNFRER3ZnRTBFcHZqRkdudkMwYWU4bmE4YlNYT0VJREExYkQzODNKOGswRFhKdmtybVZ0Y2RELzZGa0taRks3MjAwUjY1VmFOK1JDeGs0QXlBbXdrLzdHRjM1V2lxTkZKUlJ2S1owV1JSRGdBcHBsd1kwbjdhbjRVbjlTdGtkRjJ3Wms0Y3dyTU9acWJnL3lMci9vZHpjeld5ZFhBMnBNaHhpa1MxYzlwWkNTVmJVM0IxQmxmcDJ2dEN4bUlmR25HbVg2K25yUVBNQWZFc3NzWmtUbkFlOHdCRyszeW5MUHFxR0lJYWcxNWswQ01SeDFwNjMvN2xOMjQzdUI0bHlvQTNFQnM2Z1plb1hpQm8wcmloRFVwbWNGVk4xN1liR3NRRGZxakJOeGxjN2dHbWFjZjJRVVduUUNmL0ZLMkdtV0xIVjJrVW5qTjc2b0FxbWZvcHJ0T3p4NVJ5TjBUcGoyMmlmdXJGMld5OVpzajdsVWIrZiszU3d3TUVWeklZdDRFbFB2MjBiME4iLCJtYWMiOiI4M2QzNmMyMWRkNzJmZDlkMTM4N2NkZTJjYmY2YzUyM2IxZjFjZjEyYTcxZWE0MjIzN2I1NDA1NzdlODBlZDAxIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-105327523\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1036714974 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">iD3eOztlFQTk9Wu93brk2PH5kNLllwdWSLzbHLjf</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">atMfj5qpj5gKx4OQYPP5PbQzRbuF7iB8X4zv4aOY</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1036714974\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-2058508446 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 07 Jun 2025 22:38:16 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6ImZRVDFBbjA1TDMwTEZNbGMrK2dXTEE9PSIsInZhbHVlIjoicGQ3ZUxOaEVSNDRCZ3JPVTQybXNiQk96RzFKekNhcXluci9oU2hLb2xBT3RIclM2azBQc3YydzFKWmNrUm9WOWtJS202aFNmeVR4SDNWWHVqR0dYNzlVVHZpdjdHNkh2czNZOHVnQ1pqdEZBY1BjWHJlb0F4YklyckRYUzlkcE1GbzZBa3FiZ1l1azZySzMxa1JxZmVaS0lzalBWSkxrUEcxRmhnUHpmS3pGall4NVA0MWhZSk52OXlsQ3RaclV5bUNTWW5ZejFGVGN6MFFhWUE3Zms2RjF5L0J5ODZuM2cya0t5QXlKRW9KRXczUFN0aTI3QnZxOFhSR016eGpjUS9ybVJxcjNpL3FuamQrK1FkdmRzZVpzZGVBQStQMDA1aDRBSXBzWDFvOExmN2c3elB1eFhiZTJyOGhFMi8zME5WRVZsOEhCT3V0cTJ5VXhVNFNtZG14aVZ4Ly9ObTNGSVMzZkZFenpidGNoU2JwaVk2dDBMeHhsRkFJK1VCQWFGRU5GeVFGMkxuZSttR3FiS0pIckxaSnREWG1aRlMwV3J2cGNOamxPOEhyWGNYMEpyK1NMRVFZRFc2SzJJSCtwTG9rcEtIckJkZWFPR2owMFJQeVE3d1Fwd25Cb3dmNkQrdS8vMHBKMUpWUTBtWGJoUDNSVVA2YW51MGxuOE1JQSsiLCJtYWMiOiI4ZjY5NmM1NzZhNGZkNTk2M2NhODBmYTUxZWM1YmFmNzQ2NDMzOTc4NTBlMTQzNzAzNTc5YzdmM2MxYjdlOWI2IiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 00:38:16 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6IllySEUyaVJQVWwyWDlKUnc5dVBLdmc9PSIsInZhbHVlIjoidFJUM0JabzgxanpEbVZQNldLcFBnNXZyRGlFWUozUE9WOUJIeVppNWpxUVF6V1RrMkMxSmRaaFdnWU1SWHg0NlBidmFlYWdIcnhJeU94SklNNjR5ZGpWT0dQL0xuQzNObjUydTVob0g5QWZqKzFPM2hKNzI3aWwxNkVrSHN5djdxdldqYVdPUWNoSXlmODdXNjVjaEd1aVI4K1JCdkNIcnUxSFNrQTZ6c3F4dTRqMW85cWR6WEdJWFRrWDF6dXV0VHIwV0k1RE1NQ2RudmpUSkVmdmRlZkJ0Rnp2OFpoSWN3UmZvem9NV0o2bFhCUmNvRUt4THg3YnRQczhrWnRDYVpjamJGVWZuM015VFU0Q0ovNkd3TnM2dnBFS01LZDBPZlVvaGVoOWJYNzhRemc5Tm5QQkU5bFo5c210aDUyZUxwNStxS00vZHVabjE3RUJhQ0VkeFRQbkFYMCtsS0JlaDNKSDdqSWR4aElHUEpnUUFuOUFEQzZ4SXJKQWlkQWNRWTlUd0p1UFloR0UzM3RyZkE1YjYxY3ZGekUyK2JVa2hER0pKRXBDUzh1TGNRWW0zbkpyN1MyN1hvR2x5REt1T2JlSUpqeG0yZDNIZ3hFZUNuTlJldG0wV09MS1doMjdHVmFoVU5nQU9SZ1FjZzdsOWFaaHM0NEJFS2MzNGFqRnoiLCJtYWMiOiI2ODZjNmI3ZDI3NWVjYTYxMzk1N2RlOWM3NTMyMzY1MjAwYTA3NWE5ZjY3ODI4OTgxNTg0YTY2NDlmY2VmYTc1IiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 00:38:16 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6ImZRVDFBbjA1TDMwTEZNbGMrK2dXTEE9PSIsInZhbHVlIjoicGQ3ZUxOaEVSNDRCZ3JPVTQybXNiQk96RzFKekNhcXluci9oU2hLb2xBT3RIclM2azBQc3YydzFKWmNrUm9WOWtJS202aFNmeVR4SDNWWHVqR0dYNzlVVHZpdjdHNkh2czNZOHVnQ1pqdEZBY1BjWHJlb0F4YklyckRYUzlkcE1GbzZBa3FiZ1l1azZySzMxa1JxZmVaS0lzalBWSkxrUEcxRmhnUHpmS3pGall4NVA0MWhZSk52OXlsQ3RaclV5bUNTWW5ZejFGVGN6MFFhWUE3Zms2RjF5L0J5ODZuM2cya0t5QXlKRW9KRXczUFN0aTI3QnZxOFhSR016eGpjUS9ybVJxcjNpL3FuamQrK1FkdmRzZVpzZGVBQStQMDA1aDRBSXBzWDFvOExmN2c3elB1eFhiZTJyOGhFMi8zME5WRVZsOEhCT3V0cTJ5VXhVNFNtZG14aVZ4Ly9ObTNGSVMzZkZFenpidGNoU2JwaVk2dDBMeHhsRkFJK1VCQWFGRU5GeVFGMkxuZSttR3FiS0pIckxaSnREWG1aRlMwV3J2cGNOamxPOEhyWGNYMEpyK1NMRVFZRFc2SzJJSCtwTG9rcEtIckJkZWFPR2owMFJQeVE3d1Fwd25Cb3dmNkQrdS8vMHBKMUpWUTBtWGJoUDNSVVA2YW51MGxuOE1JQSsiLCJtYWMiOiI4ZjY5NmM1NzZhNGZkNTk2M2NhODBmYTUxZWM1YmFmNzQ2NDMzOTc4NTBlMTQzNzAzNTc5YzdmM2MxYjdlOWI2IiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 00:38:16 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6IllySEUyaVJQVWwyWDlKUnc5dVBLdmc9PSIsInZhbHVlIjoidFJUM0JabzgxanpEbVZQNldLcFBnNXZyRGlFWUozUE9WOUJIeVppNWpxUVF6V1RrMkMxSmRaaFdnWU1SWHg0NlBidmFlYWdIcnhJeU94SklNNjR5ZGpWT0dQL0xuQzNObjUydTVob0g5QWZqKzFPM2hKNzI3aWwxNkVrSHN5djdxdldqYVdPUWNoSXlmODdXNjVjaEd1aVI4K1JCdkNIcnUxSFNrQTZ6c3F4dTRqMW85cWR6WEdJWFRrWDF6dXV0VHIwV0k1RE1NQ2RudmpUSkVmdmRlZkJ0Rnp2OFpoSWN3UmZvem9NV0o2bFhCUmNvRUt4THg3YnRQczhrWnRDYVpjamJGVWZuM015VFU0Q0ovNkd3TnM2dnBFS01LZDBPZlVvaGVoOWJYNzhRemc5Tm5QQkU5bFo5c210aDUyZUxwNStxS00vZHVabjE3RUJhQ0VkeFRQbkFYMCtsS0JlaDNKSDdqSWR4aElHUEpnUUFuOUFEQzZ4SXJKQWlkQWNRWTlUd0p1UFloR0UzM3RyZkE1YjYxY3ZGekUyK2JVa2hER0pKRXBDUzh1TGNRWW0zbkpyN1MyN1hvR2x5REt1T2JlSUpqeG0yZDNIZ3hFZUNuTlJldG0wV09MS1doMjdHVmFoVU5nQU9SZ1FjZzdsOWFaaHM0NEJFS2MzNGFqRnoiLCJtYWMiOiI2ODZjNmI3ZDI3NWVjYTYxMzk1N2RlOWM3NTMyMzY1MjAwYTA3NWE5ZjY3ODI4OTgxNTg0YTY2NDlmY2VmYTc1IiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 00:38:16 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2058508446\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-673512464 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">iD3eOztlFQTk9Wu93brk2PH5kNLllwdWSLzbHLjf</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"25 characters\">http://localhost:8000/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>16</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-673512464\", {\"maxDepth\":0})</script>\n"}}