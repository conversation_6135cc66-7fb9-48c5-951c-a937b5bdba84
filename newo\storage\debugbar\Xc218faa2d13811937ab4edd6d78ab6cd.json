{"__meta": {"id": "Xc218faa2d13811937ab4edd6d78ab6cd", "datetime": "2025-06-07 22:36:40", "utime": **********.636538, "method": "GET", "uri": "/pos-payment-type?vc_name=6&user_id=&warehouse_name=8&quotation_id=0", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749335799.677663, "end": **********.636566, "duration": 0.9589028358459473, "duration_str": "959ms", "measures": [{"label": "Booting", "start": 1749335799.677663, "relative_start": 0, "end": **********.428706, "relative_end": **********.428706, "duration": 0.7510428428649902, "duration_str": "751ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.428725, "relative_start": 0.7510619163513184, "end": **********.636569, "relative_end": 3.0994415283203125e-06, "duration": 0.20784401893615723, "duration_str": "208ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 53361376, "peak_usage_str": "51MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 1, "templates": [{"name": "1x pos.bill_type", "param_count": null, "params": [], "start": **********.623207, "type": "blade", "hash": "bladeC:\\laragon\\www\\to\\newo\\resources\\views/pos/bill_type.blade.phppos.bill_type", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fresources%2Fviews%2Fpos%2Fbill_type.blade.php&line=1", "ajax": false, "filename": "bill_type.blade.php", "line": "?"}, "render_count": 1, "name_original": "pos.bill_type"}]}, "route": {"uri": "GET pos-payment-type", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\PosController@posBillType", "namespace": null, "prefix": "", "where": [], "as": "pos.billtype", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FPosController.php&line=1410\" onclick=\"\">app/Http/Controllers/PosController.php:1410-1518</a>"}, "queries": {"nb_statements": 8, "nb_failed_statements": 0, "accumulated_duration": 0.011059999999999999, "accumulated_duration_str": "11.06ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 16 limit 1", "type": "query", "params": [], "bindings": ["16"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.4961169, "duration": 0.0035499999999999998, "duration_str": "3.55ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 32.098}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.5164812, "duration": 0.0008399999999999999, "duration_str": "840μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 32.098, "width_percent": 7.595}, {"sql": "select `permissions`.*, `model_has_permissions`.`model_id` as `pivot_model_id`, `model_has_permissions`.`permission_id` as `pivot_permission_id`, `model_has_permissions`.`model_type` as `pivot_model_type` from `permissions` inner join `model_has_permissions` on `permissions`.`id` = `model_has_permissions`.`permission_id` where `model_has_permissions`.`model_id` = 16 and `model_has_permissions`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["16", "App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 326}, {"index": 21, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 566}], "start": **********.547444, "duration": 0.00135, "duration_str": "1.35ms", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:326", "source": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php:326", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=326", "ajax": false, "filename": "HasPermissions.php", "line": "326"}, "connection": "ty", "start_percent": 39.693, "width_percent": 12.206}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (16) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 312}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 26, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}], "start": **********.5523908, "duration": 0.0012900000000000001, "duration_str": "1.29ms", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 51.899, "width_percent": 11.664}, {"sql": "select * from `customers` where `name` = '6' and `created_by` = 15 limit 1", "type": "query", "params": [], "bindings": ["6", "15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/PosController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\PosController.php", "line": 1421}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.564241, "duration": 0.00113, "duration_str": "1.13ms", "memory": 0, "memory_str": null, "filename": "PosController.php:1421", "source": "app/Http/Controllers/PosController.php:1421", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FPosController.php&line=1421", "ajax": false, "filename": "PosController.php", "line": "1421"}, "connection": "ty", "start_percent": 63.562, "width_percent": 10.217}, {"sql": "select * from `warehouses` where `id` = '8' and `created_by` = 15 limit 1", "type": "query", "params": [], "bindings": ["8", "15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/PosController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\PosController.php", "line": 1422}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.569823, "duration": 0.0010400000000000001, "duration_str": "1.04ms", "memory": 0, "memory_str": null, "filename": "PosController.php:1422", "source": "app/Http/Controllers/PosController.php:1422", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FPosController.php&line=1422", "ajax": false, "filename": "PosController.php", "line": "1422"}, "connection": "ty", "start_percent": 73.779, "width_percent": 9.403}, {"sql": "select * from `pos` where `created_by` = 15 order by `created_at` desc limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/PosController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\PosController.php", "line": 517}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/PosController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\PosController.php", "line": 1426}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.5779932, "duration": 0.00099, "duration_str": "990μs", "memory": 0, "memory_str": null, "filename": "PosController.php:517", "source": "app/Http/Controllers/PosController.php:517", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FPosController.php&line=517", "ajax": false, "filename": "PosController.php", "line": "517"}, "connection": "ty", "start_percent": 83.183, "width_percent": 8.951}, {"sql": "select * from `pos` where `created_by` = 15 order by `id` desc limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/PosController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\PosController.php", "line": 1505}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.604453, "duration": 0.00087, "duration_str": "870μs", "memory": 0, "memory_str": null, "filename": "PosController.php:1505", "source": "app/Http/Controllers/PosController.php:1505", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FPosController.php&line=1505", "ajax": false, "filename": "PosController.php", "line": "1505"}, "connection": "ty", "start_percent": 92.134, "width_percent": 7.866}]}, "models": {"data": {"App\\Models\\Pos": {"value": 2, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FPos.php&line=1", "ajax": false, "filename": "Pos.php", "line": "?"}}, "App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}, "App\\Models\\warehouse": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2Fwarehouse.php&line=1", "ajax": false, "filename": "warehouse.php", "line": "?"}}}, "count": 5, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 2, "messages": [{"message": "[ability => manage pos, result => true, user => 16, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-698975438 data-indent-pad=\"  \"><span class=sf-dump-note>manage pos</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"10 characters\">manage pos</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>16</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-698975438\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.561908, "xdebug_link": null}, {"message": "[ability => manage pos, result => true, user => 16, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-306320685 data-indent-pad=\"  \"><span class=sf-dump-note>manage pos</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"10 characters\">manage pos</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>16</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-306320685\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.576582, "xdebug_link": null}]}, "session": {"_token": "iD3eOztlFQTk9Wu93brk2PH5kNLllwdWSLzbHLjf", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "16", "pos": "array:1 [\n  3 => array:9 [\n    \"name\" => \"حمد\"\n    \"quantity\" => 1\n    \"price\" => \"10.00\"\n    \"id\" => \"3\"\n    \"tax\" => 0\n    \"subtotal\" => 10.0\n    \"originalquantity\" => 19\n    \"product_tax\" => \"-\"\n    \"product_tax_id\" => 0\n  ]\n]"}, "request": {"path_info": "/pos-payment-type", "status_code": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-1215546853 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>vc_name</span>\" => \"<span class=sf-dump-str>6</span>\"\n  \"<span class=sf-dump-key>user_id</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>warehouse_name</span>\" => \"<span class=sf-dump-str>8</span>\"\n  \"<span class=sf-dump-key>quotation_id</span>\" => \"<span class=sf-dump-str>0</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1215546853\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-2128381668 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-2128381668\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-2076416331 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">iD3eOztlFQTk9Wu93brk2PH5kNLllwdWSLzbHLjf</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1938 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=17ceeen%7C2%7Cfwk%7C0%7C1960; _clsk=1yftgw7%7C1749334872942%7C5%7C1%7Ck.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6InhONDlLbHYxYk5LZXNJUWxaRWxLYkE9PSIsInZhbHVlIjoidHNEVitwdktjQVBDSjc2eCszem5rWXc5a0lSS0R5THdFWlh2UE1CUXFCY2lMVUlVd2QwV2MvbXZrcjJ1Nk5FcHZncDRFamRKYnlKcm9oMVlOeFYrS0dER0dtb2VuVDc2Mnd1RXhHY25GYTVTYm1sb2FFSDNDRk5Dd0pnNVI2a1gvK1RYb3NiZ2tqSXRlcDAzS1RyOFpKMlZqQXdvUm1PcW91cFM2VUNpRW05M3hpbk5QQlNCS21NVXZDZUdnSFlLaW51eXFTejRDcnQxNGJRb1ppaVNNdU83Qk9hb25zbTdObWZWbWd3eVUrK2RwRnQ4YUhXSTVuaSs1TlBKdk5HdDdNcVhTTzZTYkxia0lVVmVPSVhscWNLWUp3WEMxVG5MSDVPTmVVelFqZmVFb0Uwbi8zYm9yWFZVdjl0OTV6TUx4UlgyZnJOdnR2TDJ0RUtvOEpLZGlJd3JPelROTzk3WEJVZWQzTHJiVHJPMUExVEhJeHJYc3ZaUWNsWVlvdTU4dFhYQnVIeWhtb2NYbVAyYTlHNlBYRHA3L2VyTE9iMjdrQzErWmZkMThTeGtENDdaRHJCYkdNeERTbXV0NjliaDFhYkZTdXNsRHQwbVNuS1kzZ2UrcE1HTDVTSVNnQXFndjFyc0hzMnJEN0d6UEx5K1JtVkNld01zLy83ekVkdy8iLCJtYWMiOiI0YTQ5NmIyZWNiNzJkMDk1MTRjYTE1NzAyZTI5YmY5MGNjOTdkMGVlODgzYWExMjJkYWM4MWFkZGM1MjBmMDEzIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6ImxxMkZQVkl3dzdKY2VSRFJsalB6bGc9PSIsInZhbHVlIjoiUVBnR0dMUVFFK3NVekp5U2c4TW1lNU03Q2t2YkQyQU9FeXR0NU9rRzROaHFqL2pqcHlUMDRXZ1JJbE5GWE9kL3N5NTBMVlN1cGpHaWVXUlpIaXN3ZURsN2pSdXl0Vk51c3NVMjBCaTBMUWovazY2N0NKbCtNTzFjYldMUzhSMHhCWEJ2VHFsVjlTeGs2Wks4cm5xOTFSTElnTUZBS2NIZlF3cHJDN2wrRkRuVTZzSXN2S3NMNkkxNVFFUDJRcXgrbEF1cVFGS0tmNlhpVm0xaHhWRlIwZ0t0RWJRZzEzbkFxcHZXekVNclErTUMvRjJqTC9vazIxUndaSXhreWtRc0pqZld5VTlxSWtRNHR3ancyNDNrQ0hRU0UvQVV1UVRGbGtyL0dvNUs4US9neER5M3J0WnIwbEhsdkhxd2V3M2tPcFFmbkJjNnhmaE5oakJYL0dEMUtZNHBieUpyODNkczYwYWo2ZVNtUUJsbmMrYnNiWElFOStMTDg4dThzZFg3dHlGS3dQMjlEMFkvbGRhSnVCNURkQVZpZXVvS0FmUGR2Y3UrWWR6cUEvWTZKeitNTm5DdEhXUDRjLzNsTWZDOWRoOFBhOE9ZU002Mk5idWV4Z0poV09kM09kYkhTZ2ZzS1dUckJLT1FTejYrT0UwVWlHNzFxd0dMNUxMNWgxTFQiLCJtYWMiOiJmMjEwMzg0YTk5ZDBmNWYwN2M1ZmY2MjE1MWQ3NDU1Mzg3NGRlZjRlMTI1ODA2NWFhZTRhMGMxY2UzY2M0NzcyIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2076416331\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-781563274 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">iD3eOztlFQTk9Wu93brk2PH5kNLllwdWSLzbHLjf</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">atMfj5qpj5gKx4OQYPP5PbQzRbuF7iB8X4zv4aOY</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-781563274\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-445417210 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 07 Jun 2025 22:36:40 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IlRrczJIU1ZsT2JQYVlINFRSNkU4QlE9PSIsInZhbHVlIjoiOTNSdlNwaEc4QmdzdzJQV3RJelhXV1ZmaU9icU1tSjR0RW5XUGQ4eDFtSTRZQTB0dUdGUmRHWTlTdm9tT1l0a3ZhK3FlVjJMbytuR1c4b1ZlNFZYMGVIb2VvNmdVdlB6NEpUSm45TE9IalFlb2R3cnVQOCttR1pCNFlxYUtLbHViRzNEK1NDa0w2bkFTcmt3SlFSYzQyRmFZV3RNQldmSU4rU2FWMVdwZ1N5eE9ENFg1UUdPVjVhN2QvYUI4d2F0ZGFYSXVqSzFNK041aDl3cExpOGtWOWlmbjloSkZSUHo1MnBLM2hNWW9VMyt2RVRDYkoxRlZndmpvV2NEaXRhZmtzcWhlalhHRUJrM1Y2V000SGVuSzRmL2FwYmsxSWJXanZJTDk4RGpWZkR6dWd4YmlybDArZk1XWHJEMDF1UHZrbndpSCthdzhyQno3T0t0WnZnNkdVa3luMnNOVHltRWc4L2NYdDRxUnQ5VkxNQUxTU1RSeW50Z242MUJiaDY0a1IyQkRVc3B6bklUY3k0K2NuMGkwbjZjdjBCZXVIR2l0MkljOUw4SVB2bk1WS0Zqbk9adXFaRlZVTURsdkxrNFB3ZjdQR1RKbm0rZTBVMEptS1ZBVmRjSFcyK0cxT0MwS1duVVZpK244V2JsK3JCRFBEa25TZVAxOEF3RGVNWE0iLCJtYWMiOiJlMGMxN2JkYjZlMTZkOGM4Njk2MDYxNDZhYmQ1MzVjZWY0Y2I1NzAwMzE5ZmRjZjIxYmE5NGQzNDNjZmNlOTBiIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 00:36:40 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6IlRSb1NhVDEwVkNyVnN0OTlPUnZOUWc9PSIsInZhbHVlIjoiY0V1Nmg3aXM2dUVyVU9xSkRVVitVODdUZ1U0c2xZQ0FTcHFGeW0wTjVtSTYyVTVBbVRuTnlrS0wweWRiYUcvRkJVelpQSXlGbkRGKzQ5emg0K1RiWHhRZzlFM3ZudjIxazd5WEtuOFVpZEdGV3lMWEJYVmZIa3QxaFZsVUJBM3FGcHlLbE5pQ04wbStWN2NheE1hRm9GYm40YUdscVo5c0tpdUgrRjdxNTM4RGgza3YvenBNcVFGeUk2NW44Y3plT2hINnhScmQ2SXM3RzNKdnpNMXdRYXU2dUVML0hjdUx2V3Nod0FtZHhaelVIN0ovNWRzVjBnMVhrZGxBRnJiUTlnbytQbGtkbHM2Sy8wd3ZyTGRNb1lxeXp6eHk0dmV5cE9KN2ZnUkdGMTNaRkZhcG1idEZ1OTVGNFQ4c0tNYncvQ3dGcHNvWlJiaGFqY1ZGbWw2QTNmaVJvNmdOOW9TcEh6Z2dKdWJTdzVleDBzYkErQk0vR05Za25VQnllRE5vdlQ0d2FPYzJvSVRYQkhYVkViWXZUblpmaWRlYVIwK2Q0TVhjL005YjdlOFQ0Zm9YT1BILzBRS3FBTTlWZ2g1d2tLQlM2ZGh0K1ZGdHFoRWZXV2NJVlBVUEhFckw0aVlFNjFzMHFmUjQ4S0NGelVIS0dUSUx0dTVNVkdvSUN6K2IiLCJtYWMiOiJmYTE5NjZiMzdiMzgyODYxMDBiOTUzODIxMDk5ZTU0YWEyOWE0YjZlNzk3MGU0M2FkZjU1OTUzOWE5ZDJjYTkyIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 00:36:40 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IlRrczJIU1ZsT2JQYVlINFRSNkU4QlE9PSIsInZhbHVlIjoiOTNSdlNwaEc4QmdzdzJQV3RJelhXV1ZmaU9icU1tSjR0RW5XUGQ4eDFtSTRZQTB0dUdGUmRHWTlTdm9tT1l0a3ZhK3FlVjJMbytuR1c4b1ZlNFZYMGVIb2VvNmdVdlB6NEpUSm45TE9IalFlb2R3cnVQOCttR1pCNFlxYUtLbHViRzNEK1NDa0w2bkFTcmt3SlFSYzQyRmFZV3RNQldmSU4rU2FWMVdwZ1N5eE9ENFg1UUdPVjVhN2QvYUI4d2F0ZGFYSXVqSzFNK041aDl3cExpOGtWOWlmbjloSkZSUHo1MnBLM2hNWW9VMyt2RVRDYkoxRlZndmpvV2NEaXRhZmtzcWhlalhHRUJrM1Y2V000SGVuSzRmL2FwYmsxSWJXanZJTDk4RGpWZkR6dWd4YmlybDArZk1XWHJEMDF1UHZrbndpSCthdzhyQno3T0t0WnZnNkdVa3luMnNOVHltRWc4L2NYdDRxUnQ5VkxNQUxTU1RSeW50Z242MUJiaDY0a1IyQkRVc3B6bklUY3k0K2NuMGkwbjZjdjBCZXVIR2l0MkljOUw4SVB2bk1WS0Zqbk9adXFaRlZVTURsdkxrNFB3ZjdQR1RKbm0rZTBVMEptS1ZBVmRjSFcyK0cxT0MwS1duVVZpK244V2JsK3JCRFBEa25TZVAxOEF3RGVNWE0iLCJtYWMiOiJlMGMxN2JkYjZlMTZkOGM4Njk2MDYxNDZhYmQ1MzVjZWY0Y2I1NzAwMzE5ZmRjZjIxYmE5NGQzNDNjZmNlOTBiIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 00:36:40 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6IlRSb1NhVDEwVkNyVnN0OTlPUnZOUWc9PSIsInZhbHVlIjoiY0V1Nmg3aXM2dUVyVU9xSkRVVitVODdUZ1U0c2xZQ0FTcHFGeW0wTjVtSTYyVTVBbVRuTnlrS0wweWRiYUcvRkJVelpQSXlGbkRGKzQ5emg0K1RiWHhRZzlFM3ZudjIxazd5WEtuOFVpZEdGV3lMWEJYVmZIa3QxaFZsVUJBM3FGcHlLbE5pQ04wbStWN2NheE1hRm9GYm40YUdscVo5c0tpdUgrRjdxNTM4RGgza3YvenBNcVFGeUk2NW44Y3plT2hINnhScmQ2SXM3RzNKdnpNMXdRYXU2dUVML0hjdUx2V3Nod0FtZHhaelVIN0ovNWRzVjBnMVhrZGxBRnJiUTlnbytQbGtkbHM2Sy8wd3ZyTGRNb1lxeXp6eHk0dmV5cE9KN2ZnUkdGMTNaRkZhcG1idEZ1OTVGNFQ4c0tNYncvQ3dGcHNvWlJiaGFqY1ZGbWw2QTNmaVJvNmdOOW9TcEh6Z2dKdWJTdzVleDBzYkErQk0vR05Za25VQnllRE5vdlQ0d2FPYzJvSVRYQkhYVkViWXZUblpmaWRlYVIwK2Q0TVhjL005YjdlOFQ0Zm9YT1BILzBRS3FBTTlWZ2g1d2tLQlM2ZGh0K1ZGdHFoRWZXV2NJVlBVUEhFckw0aVlFNjFzMHFmUjQ4S0NGelVIS0dUSUx0dTVNVkdvSUN6K2IiLCJtYWMiOiJmYTE5NjZiMzdiMzgyODYxMDBiOTUzODIxMDk5ZTU0YWEyOWE0YjZlNzk3MGU0M2FkZjU1OTUzOWE5ZDJjYTkyIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 00:36:40 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-445417210\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">iD3eOztlFQTk9Wu93brk2PH5kNLllwdWSLzbHLjf</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>16</span>\n  \"<span class=sf-dump-key>pos</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-key>3</span> => <span class=sf-dump-note>array:9</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"3 characters\">&#1581;&#1605;&#1583;</span>\"\n      \"<span class=sf-dump-key>quantity</span>\" => <span class=sf-dump-num>1</span>\n      \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"5 characters\">10.00</span>\"\n      \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str>3</span>\"\n      \"<span class=sf-dump-key>tax</span>\" => <span class=sf-dump-num>0</span>\n      \"<span class=sf-dump-key>subtotal</span>\" => <span class=sf-dump-num>10.0</span>\n      \"<span class=sf-dump-key>originalquantity</span>\" => <span class=sf-dump-num>19</span>\n      \"<span class=sf-dump-key>product_tax</span>\" => \"<span class=sf-dump-str>-</span>\"\n      \"<span class=sf-dump-key>product_tax_id</span>\" => <span class=sf-dump-num>0</span>\n    </samp>]\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n"}}