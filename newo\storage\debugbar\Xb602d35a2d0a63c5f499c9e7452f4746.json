{"__meta": {"id": "Xb602d35a2d0a63c5f499c9e7452f4746", "datetime": "2025-06-07 22:18:34", "utime": **********.47389, "method": "POST", "uri": "/chats/favorites", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749334713.071992, "end": **********.473927, "duration": 1.40193510055542, "duration_str": "1.4s", "measures": [{"label": "Booting", "start": 1749334713.071992, "relative_start": 0, "end": **********.306766, "relative_end": **********.306766, "duration": 1.234774112701416, "duration_str": "1.23s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.306786, "relative_start": 1.2347941398620605, "end": **********.473931, "relative_end": 4.0531158447265625e-06, "duration": 0.1671450138092041, "duration_str": "167ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45039864, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/favorites", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getFavorites", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "favorites", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=454\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:454-477</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.01839, "accumulated_duration_str": "18.39ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.3848789, "duration": 0.01568, "duration_str": "15.68ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 85.264}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.426317, "duration": 0.00121, "duration_str": "1.21ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 85.264, "width_percent": 6.58}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (15) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.445471, "duration": 0.0015, "duration_str": "1.5ms", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 91.843, "width_percent": 8.157}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "cN6pwMHluqMWM9hLaRtOtRasnQF8pfN3ghxjsOfa", "_previous": "array:1 [\n  \"url\" => \"http://localhost/support\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/favorites", "status_code": "<pre class=sf-dump id=sf-dump-1099896778 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1099896778\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1482887599 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1482887599\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1633174270 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">cN6pwMHluqMWM9hLaRtOtRasnQF8pfN3ghxjsOfa</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1633174270\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">http://localhost/support</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1993 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=a091af98-270d-406e-ab7e-33a6e610d6e2414686; _clck=nups8l%7C2%7Cfwk%7C0%7C1960; _clsk=ij6lzq%7C1749334707149%7C5%7C1%7Ck.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6ImczZU1uRytyaWNnaHZmMlhIUVVZcXc9PSIsInZhbHVlIjoiTzBiaTZPVlRBbFVtMlNqR1UwZHpYdWh4aWRhY3pvYTVrZEluMC95L1hVU1BLN3B2M29CV2F4ZC9sOFc3OGNiWWNvUys3S2kxNmxLVmVjeGZOelBuWE9vRk9NZGdPNjRIMTE2MWlMYUJHMUxzSFVET3lwNUM5Uk9Nd1JDMnhLRDBhbEZ4a2FNdmhJbVQrN3RFMVpHZW9yVmJxdEFnOGN0RTh4Q055eko0TXRRQUlhbmZ0bk11YXZzaXQ1MUdMVXJpYkFYOUJTTVB4UDFGVDB1WWdPMWF2NzRicjRhS2tvbHJFOGRWRjg2elhjaGx6VDFMS0hyNkpEVG1OMFB6ZzAyMWxmdDJ4RWgwU01RVGVmcEZuVDNVcjhNVVcxTHZyMkEwS1hWaFhuRlo2NWtYcjQxQ1c3VnA4SmNCSzNlNlpGNXdqcnhzclhnWmsreGVyWUsrZTdhenRmR1VGSXU5Rzhmd3g4UUZheVorSjhqUDJjUGl3eFBCSjc3RFkrT1BwdzNLM3BoQmtDMTAwcVdHMFdTSzd3Z2QweThrcmZtZ1VXSWJRUEJJakM1dDNNK0s3RGVxL2ZIUmtyMzVKM2J4L1hwRDFucWNxTC8zai9yZXNpUi8xTDhTbWZJczh2QnF2NFJ0OGtLdUNURVYra2lnelVERUNtelYxVW9jR1FyUmxRcHUiLCJtYWMiOiI1Y2UwZGY5ODU0ZjcxZDMxN2JjZWYzMjJjMTk2NjExZDQyMTNhYmUxNjkzYzMyYjQ0OWFlOTMwYzljNTBkNmQ1IiwidGFnIjoiIn0%3D; test_session=eyJpdiI6InEwMlhGMDB1QTNZYTMvMjRybDRhU0E9PSIsInZhbHVlIjoid1RHRnJnbjA4OFJqMnV4Y1M3SjVqeFJmZ3M1TWdiWWs3Y3h5NG1iU3ZGbzdLbG9LRDZ0WjNOVkJsVmFmOWlYaWNHL2JQVTgrV3JydVhRb1hwVS8vb2F2THZmYU1LODFiTFZra1FMdlBuUm1yTVo5WndIZEl1N2JVL1ZhMnNrZDV4aWJsT01DekF3K0F1QkVzOTZ6clg0T3V5dmR5RXFJbEdiQi9nYW55Z0lpU2lKMm5oQzlWclVNdERsUGRJQldwZ1FBYm45OXVmaCtESnpQU2VnclJJWXltbGJhSXMrUUpHK0lma0hReVMxU0t6Wk5pTEV1SHprUUJheXlSZE9CUzBVSnRleHhBVFdPR1hiTTVqbjlaWDZRZXpuODNyNDYxUFlTL0FEQzA3SXhGMVZuWUtXSXVFVTFyd3UvZnJPclRCa1dhcEtsSTYyTjFsM3pNemlpYUVNZGRTUTJ2QWNNb3V5Z24xWGhzcmhKZWNnZ0NEUHFhaytkZngvbEdpcEx4MnFBT3AvWGM1Q1BFQTRRK2VvYmozNzl2ajFTaGt4NWJQbmRkU1hzcFZwNnJKNlFXaFRLdmxnODN0N09xRkcvc0t2QXJOa0JzK2tCUThndUpDNUh2ejJYOFZBWjZJajQyQ3ZLLzNTSmZVUEtka216SkZLZ1M2MllRb3lMNng4bUwiLCJtYWMiOiIwZTBhM2QzYzRmNjcyODNhMWJmN2ZjMGU1OGVmODg0NDdkZTA2MDc1OWI0MGFkMThjZGYzZWU2ZDBmYTAxZmEyIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">cN6pwMHluqMWM9hLaRtOtRasnQF8pfN3ghxjsOfa</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Eo35AMmp3Bkf2zsyHLroae0N6MdUih7xJ0ojLwSF</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-133208944 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 07 Jun 2025 22:18:34 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6ImdaTE9aczJKdDJSaEYvNStVVmc1cVE9PSIsInZhbHVlIjoiTWEvUlhuUGJick8rRUlIbXFRSTNpYklDMUxjUUdOdVpyQ1k0QTMyZ1dPbFNOVFkvK2ZGTWEzRXdhR3ppM2xpbWVVcmJ6QjhIV2syZWhUZ09ET0pSWE56ekl0MXR2MVJmMEYwek8zVUhwc3dqbkxNaHkxS1djZkN1US9XeWVDTWxTd3cvUFdaQytMZG96RnhxTmNtS1NDN0JCalJvKzl5aEVwRHBMY0N1YW85MWE5dkpXbjNta3NNdmJKV0t4SHZYR1NuVjZ5K1pFQkU4KzVQdlIwYWpvcHJJRHEvVUlYZUFsZlZSTDBhV2tUU3ZyVEgvYmtsVWZTbGtONFFucktlSGdoK2FPUkt2TWhkUjZWRTlvNUV4Y2RVUExLMXROMHZ1RGc4cjlIRGpZUkVYTmRNSWNudkNjeDNNZTFUcCtYbTFZSzByOE80T3VpWHo0T2hGdEsybHBSME9JazlMWFU0dTJiR3JTOWxiMVJvbnRKUFgyam9VSjNiMWRBQkxyeFRoSlEzNWU2eEdIeUhRQUlCQzFRcGIvenp5UC9XRVZEZWdYZnhrZDZOR21HNHVVbi9Ed1RVdDZLSWRjUFQ4TGNIMjlPL29TWDFiSksxaGh1bzRJV1ovRTN6eHRoTklTVUJDdytzcE1iTmdGZnEycVpjV1NuY2FmdVVzeEo4eHl6NS8iLCJtYWMiOiJjYTkyY2MwZTgxOTI1NmU0MGM0MzczYWIyMWJhYmI4YWRmMTUxZjFmOTVjNzFlNTI5ODUwZTc2YzkyYzRiNjg0IiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 00:18:34 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6IlpSVlcwRGMrRk1Jd3NKLzEyZkFkVkE9PSIsInZhbHVlIjoiVjhGdGVaRW4xb3l2VmUvREVFeGQ0Y0JaWkVmanJYNFZrKytGaTFpWUZTWGdXRmFiQ1FCc1hZa0lnK0RJSWtoTzd3MHkxS3NDQ0h2TjJwNGJCcjNSeTFzZjFrS0RaM25DYS9qcVpLdGpZcDYwSE9FakdXQ0xLWS8ydWNTbytvaVptOGpSQWxSMno4NHdveGJXUW5FdFZuODhPZkRMd0FDaWh5R1Y3bG00TEdSeEhFTjZWbENMK0VjYy9VZUJudUJtc3llQmQ1VXFTN1hFRjBhcS92N0UwQThLZDhLNURZWEdKTUF5SGcwbHkwcnlTbm5ydHV2eS9XOWNKbUpaVmNNRzZKL21kN3dmSDJBdE80Y3pIU09sWEh1a2hKcU8yTitwQldydHpvclVVcGk3djVyRklTZnpwS0JDY0dvZHQ1Vi9IdE4vSS80WjRnR2ZMUzYxL25KZnB3TTcwVmdMVjMzSUVDai9PeE51anljYkxCcExpRDVndytFcjk3M3J3VTdoWE9uYThwaTJlVXdoUHJkUHhhejFHUDllblRScm05WmdEazBSMmV1ZzVoM0xoU3J2cXVvbXBHWjd1M3RVSmhYWldhdklqL3N1dU1ySWFleGtrOEVqalBIZWt5VkYrS3MrRTMyZU5ZT2ZVbStzRWREWU9QRHBXVm1EeXY5RmZMSEMiLCJtYWMiOiI2ZjdhODkxZTI2ZmY4NDExOTkyMjI1YjUyZmQwY2E2Y2EwNzFlZTdjOGJhMjQ4ZmM5MGExNDU3ZjgzYjFmOTYzIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 00:18:34 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6ImdaTE9aczJKdDJSaEYvNStVVmc1cVE9PSIsInZhbHVlIjoiTWEvUlhuUGJick8rRUlIbXFRSTNpYklDMUxjUUdOdVpyQ1k0QTMyZ1dPbFNOVFkvK2ZGTWEzRXdhR3ppM2xpbWVVcmJ6QjhIV2syZWhUZ09ET0pSWE56ekl0MXR2MVJmMEYwek8zVUhwc3dqbkxNaHkxS1djZkN1US9XeWVDTWxTd3cvUFdaQytMZG96RnhxTmNtS1NDN0JCalJvKzl5aEVwRHBMY0N1YW85MWE5dkpXbjNta3NNdmJKV0t4SHZYR1NuVjZ5K1pFQkU4KzVQdlIwYWpvcHJJRHEvVUlYZUFsZlZSTDBhV2tUU3ZyVEgvYmtsVWZTbGtONFFucktlSGdoK2FPUkt2TWhkUjZWRTlvNUV4Y2RVUExLMXROMHZ1RGc4cjlIRGpZUkVYTmRNSWNudkNjeDNNZTFUcCtYbTFZSzByOE80T3VpWHo0T2hGdEsybHBSME9JazlMWFU0dTJiR3JTOWxiMVJvbnRKUFgyam9VSjNiMWRBQkxyeFRoSlEzNWU2eEdIeUhRQUlCQzFRcGIvenp5UC9XRVZEZWdYZnhrZDZOR21HNHVVbi9Ed1RVdDZLSWRjUFQ4TGNIMjlPL29TWDFiSksxaGh1bzRJV1ovRTN6eHRoTklTVUJDdytzcE1iTmdGZnEycVpjV1NuY2FmdVVzeEo4eHl6NS8iLCJtYWMiOiJjYTkyY2MwZTgxOTI1NmU0MGM0MzczYWIyMWJhYmI4YWRmMTUxZjFmOTVjNzFlNTI5ODUwZTc2YzkyYzRiNjg0IiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 00:18:34 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6IlpSVlcwRGMrRk1Jd3NKLzEyZkFkVkE9PSIsInZhbHVlIjoiVjhGdGVaRW4xb3l2VmUvREVFeGQ0Y0JaWkVmanJYNFZrKytGaTFpWUZTWGdXRmFiQ1FCc1hZa0lnK0RJSWtoTzd3MHkxS3NDQ0h2TjJwNGJCcjNSeTFzZjFrS0RaM25DYS9qcVpLdGpZcDYwSE9FakdXQ0xLWS8ydWNTbytvaVptOGpSQWxSMno4NHdveGJXUW5FdFZuODhPZkRMd0FDaWh5R1Y3bG00TEdSeEhFTjZWbENMK0VjYy9VZUJudUJtc3llQmQ1VXFTN1hFRjBhcS92N0UwQThLZDhLNURZWEdKTUF5SGcwbHkwcnlTbm5ydHV2eS9XOWNKbUpaVmNNRzZKL21kN3dmSDJBdE80Y3pIU09sWEh1a2hKcU8yTitwQldydHpvclVVcGk3djVyRklTZnpwS0JDY0dvZHQ1Vi9IdE4vSS80WjRnR2ZMUzYxL25KZnB3TTcwVmdMVjMzSUVDai9PeE51anljYkxCcExpRDVndytFcjk3M3J3VTdoWE9uYThwaTJlVXdoUHJkUHhhejFHUDllblRScm05WmdEazBSMmV1ZzVoM0xoU3J2cXVvbXBHWjd1M3RVSmhYWldhdklqL3N1dU1ySWFleGtrOEVqalBIZWt5VkYrS3MrRTMyZU5ZT2ZVbStzRWREWU9QRHBXVm1EeXY5RmZMSEMiLCJtYWMiOiI2ZjdhODkxZTI2ZmY4NDExOTkyMjI1YjUyZmQwY2E2Y2EwNzFlZTdjOGJhMjQ4ZmM5MGExNDU3ZjgzYjFmOTYzIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 00:18:34 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-133208944\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1842425977 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">cN6pwMHluqMWM9hLaRtOtRasnQF8pfN3ghxjsOfa</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"24 characters\">http://localhost/support</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1842425977\", {\"maxDepth\":0})</script>\n"}}