{"__meta": {"id": "X0f020a629cf8d7fca36ac7b3b9f1eeff", "datetime": "2025-06-07 22:18:34", "utime": **********.473913, "method": "POST", "uri": "/chats/getContacts", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749334713.071992, "end": **********.473945, "duration": 1.4019529819488525, "duration_str": "1.4s", "measures": [{"label": "Booting", "start": 1749334713.071992, "relative_start": 0, "end": **********.294859, "relative_end": **********.294859, "duration": 1.2228670120239258, "duration_str": "1.22s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.294881, "relative_start": 1.2228891849517822, "end": **********.473949, "relative_end": 4.0531158447265625e-06, "duration": 0.17906785011291504, "duration_str": "179ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45037400, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/getContacts", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getContacts", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "contacts.get", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=333\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:333-390</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.02655, "accumulated_duration_str": "26.55ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.376078, "duration": 0.02429, "duration_str": "24.29ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 91.488}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.427674, "duration": 0.0011, "duration_str": "1.1ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 91.488, "width_percent": 4.143}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (15) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.4457572, "duration": 0.00116, "duration_str": "1.16ms", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 95.631, "width_percent": 4.369}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "cN6pwMHluqMWM9hLaRtOtRasnQF8pfN3ghxjsOfa", "_previous": "array:1 [\n  \"url\" => \"http://localhost/support\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/getContacts", "status_code": "<pre class=sf-dump id=sf-dump-288236739 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-288236739\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1118106385 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1118106385\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1472077848 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">cN6pwMHluqMWM9hLaRtOtRasnQF8pfN3ghxjsOfa</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1472077848\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">http://localhost/support</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1993 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=a091af98-270d-406e-ab7e-33a6e610d6e2414686; _clck=nups8l%7C2%7Cfwk%7C0%7C1960; _clsk=ij6lzq%7C1749334707149%7C5%7C1%7Ck.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6ImczZU1uRytyaWNnaHZmMlhIUVVZcXc9PSIsInZhbHVlIjoiTzBiaTZPVlRBbFVtMlNqR1UwZHpYdWh4aWRhY3pvYTVrZEluMC95L1hVU1BLN3B2M29CV2F4ZC9sOFc3OGNiWWNvUys3S2kxNmxLVmVjeGZOelBuWE9vRk9NZGdPNjRIMTE2MWlMYUJHMUxzSFVET3lwNUM5Uk9Nd1JDMnhLRDBhbEZ4a2FNdmhJbVQrN3RFMVpHZW9yVmJxdEFnOGN0RTh4Q055eko0TXRRQUlhbmZ0bk11YXZzaXQ1MUdMVXJpYkFYOUJTTVB4UDFGVDB1WWdPMWF2NzRicjRhS2tvbHJFOGRWRjg2elhjaGx6VDFMS0hyNkpEVG1OMFB6ZzAyMWxmdDJ4RWgwU01RVGVmcEZuVDNVcjhNVVcxTHZyMkEwS1hWaFhuRlo2NWtYcjQxQ1c3VnA4SmNCSzNlNlpGNXdqcnhzclhnWmsreGVyWUsrZTdhenRmR1VGSXU5Rzhmd3g4UUZheVorSjhqUDJjUGl3eFBCSjc3RFkrT1BwdzNLM3BoQmtDMTAwcVdHMFdTSzd3Z2QweThrcmZtZ1VXSWJRUEJJakM1dDNNK0s3RGVxL2ZIUmtyMzVKM2J4L1hwRDFucWNxTC8zai9yZXNpUi8xTDhTbWZJczh2QnF2NFJ0OGtLdUNURVYra2lnelVERUNtelYxVW9jR1FyUmxRcHUiLCJtYWMiOiI1Y2UwZGY5ODU0ZjcxZDMxN2JjZWYzMjJjMTk2NjExZDQyMTNhYmUxNjkzYzMyYjQ0OWFlOTMwYzljNTBkNmQ1IiwidGFnIjoiIn0%3D; test_session=eyJpdiI6InEwMlhGMDB1QTNZYTMvMjRybDRhU0E9PSIsInZhbHVlIjoid1RHRnJnbjA4OFJqMnV4Y1M3SjVqeFJmZ3M1TWdiWWs3Y3h5NG1iU3ZGbzdLbG9LRDZ0WjNOVkJsVmFmOWlYaWNHL2JQVTgrV3JydVhRb1hwVS8vb2F2THZmYU1LODFiTFZra1FMdlBuUm1yTVo5WndIZEl1N2JVL1ZhMnNrZDV4aWJsT01DekF3K0F1QkVzOTZ6clg0T3V5dmR5RXFJbEdiQi9nYW55Z0lpU2lKMm5oQzlWclVNdERsUGRJQldwZ1FBYm45OXVmaCtESnpQU2VnclJJWXltbGJhSXMrUUpHK0lma0hReVMxU0t6Wk5pTEV1SHprUUJheXlSZE9CUzBVSnRleHhBVFdPR1hiTTVqbjlaWDZRZXpuODNyNDYxUFlTL0FEQzA3SXhGMVZuWUtXSXVFVTFyd3UvZnJPclRCa1dhcEtsSTYyTjFsM3pNemlpYUVNZGRTUTJ2QWNNb3V5Z24xWGhzcmhKZWNnZ0NEUHFhaytkZngvbEdpcEx4MnFBT3AvWGM1Q1BFQTRRK2VvYmozNzl2ajFTaGt4NWJQbmRkU1hzcFZwNnJKNlFXaFRLdmxnODN0N09xRkcvc0t2QXJOa0JzK2tCUThndUpDNUh2ejJYOFZBWjZJajQyQ3ZLLzNTSmZVUEtka216SkZLZ1M2MllRb3lMNng4bUwiLCJtYWMiOiIwZTBhM2QzYzRmNjcyODNhMWJmN2ZjMGU1OGVmODg0NDdkZTA2MDc1OWI0MGFkMThjZGYzZWU2ZDBmYTAxZmEyIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">cN6pwMHluqMWM9hLaRtOtRasnQF8pfN3ghxjsOfa</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Eo35AMmp3Bkf2zsyHLroae0N6MdUih7xJ0ojLwSF</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-712829265 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 07 Jun 2025 22:18:34 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6ImJIOXJjdjlzcmVTdElmdGR0QkFRSmc9PSIsInZhbHVlIjoiSmFyTldSb0JFTUVualExeGE3VWRrSXpWb0FNS0FQeHNsd0JPZ0EzUVNRTVdVYUdqR3BhUlRZSG96aDIwOVQ3WnQraVJaQVlLaWN6WEZvRU1sU2drZEpqMno5Mzhhei8rTnpQaDloT2JPWmNWeTQ4b1AxamtqeXk2MitnbG1Vb1JtWVNHT2d5NldDY3dMVVhMMzN3MVBmTlFEUXZMVmdIVlFabTFnaElSMG1wMHZSKyt4b29reWRISHRlQVhDRjJrT29kbUNPdkU3bUVWZUcvQVo5SU40ZG9KUm1SYkRxWkgrcXpNREU5OFpScy9aamhjVyt2azZ4d1VKaDhBWXhyeFZUNTRwMFdFMUc3cGNRSnR4VWh4bEVRYThhTVVnVVZtckYzeU1ocm1hdTdicmVwSW8yVnVOdmVualJ0NEF4b2Y5SDVOWXhURjN2NXRsS2tCamNkYmZDeXJ0MXZMd2l3Ri9HRWJBTlUrWUFpdWMvV1owZEpUUXJEUkNWZzZlRFBXaml3TmRpbzZJOHJBQkFrTHRMTHBIQ20xVWU2a2xJUHBkUXNaOXVxMjZmQ1VkQWFLVnRaUEJqSHRKZnBKVkZXbFloSE5YL0dHUEVoSDFsa045R1lZYkRKNWg4cjI3ZVB5ZGIyR3lONEZTa3hRbnBKdHBGcEpOSkFLV0dNQTJWWm0iLCJtYWMiOiIxNjhiYTU5NjY1YTA0NGViZDFkMmE5NDU3MmQyODI4MjRlMmMyMGY0ODM4YjQ0NWIyOWM0ZmU3NjY0OTNjOTc0IiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 00:18:34 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6IkdyYlVjdlFDUlBNL2JMZjFDUnBLdlE9PSIsInZhbHVlIjoiZGpwVGU2anltOVFLZ3hsSXg2dlM3cFZCRkVNU3AwRnVWbVVWTnBHSm1vaXNSZ2x5UkEveHFveXM5UGl5SHN1WE1ZallWUTR4cVJPUFk0WEI1aGpkeGVCZjVXOXpsblRUamFzdE40Y054K2pWelpIV0o1OVZXVEtyNDlSNHhPN3dWNkVyRSsrQ3dsVDE0RmtvNmx0L2Z1UTY5RmN6Nmw1NUFlMFJNUytsWDlRZ3pyaHp1UFJQZi9lSk1VWk1hY0MraHBoc1lqWmF2bVhjaEd2aklUVmJucGlERWlhbTZjcWIxKysyNW5QU21ncm1BcE40cVdMV0toandQSXJ2K2ZYUlhUeXphMUFJUm04MUN2THpmTEZCQncrYUR6a2ZjakdFOXR5Sm1JRzg0QW10MXZkbVk2ZSt2OEc4WkswV1BBVk5FNitqYUdUM21BVWpYUG1BSk5pSGRFRktJYXhFcUtUQnBiOVpPc1dmM0FEcm1saEJDQUU0T2RUZUtrVWtCQVdGbVJkTEFnY0Q0RU1vL0JvRDNQNCt2Qk94UzVmbGhlU1p3TmpDN0dSOVl0dDNSQlphTDE0cU1nRzd3Z1FSQTZZWXVhUXdDVnpDNm01SlRGbThERmhualYzbE15QmZESm9zeDczZjMvd1UxOHU3VGVoNTlUTVdSMzhoM1htejZBYkYiLCJtYWMiOiIwNmM2Mjk5ZTU4NWQ1MThhYTc4OTZjZTE2ZDg5MzAzM2U2ODVjMDY5YzU2NDM3MGVlNTkxYWU5YjdlOWZlNTdjIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 00:18:34 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6ImJIOXJjdjlzcmVTdElmdGR0QkFRSmc9PSIsInZhbHVlIjoiSmFyTldSb0JFTUVualExeGE3VWRrSXpWb0FNS0FQeHNsd0JPZ0EzUVNRTVdVYUdqR3BhUlRZSG96aDIwOVQ3WnQraVJaQVlLaWN6WEZvRU1sU2drZEpqMno5Mzhhei8rTnpQaDloT2JPWmNWeTQ4b1AxamtqeXk2MitnbG1Vb1JtWVNHT2d5NldDY3dMVVhMMzN3MVBmTlFEUXZMVmdIVlFabTFnaElSMG1wMHZSKyt4b29reWRISHRlQVhDRjJrT29kbUNPdkU3bUVWZUcvQVo5SU40ZG9KUm1SYkRxWkgrcXpNREU5OFpScy9aamhjVyt2azZ4d1VKaDhBWXhyeFZUNTRwMFdFMUc3cGNRSnR4VWh4bEVRYThhTVVnVVZtckYzeU1ocm1hdTdicmVwSW8yVnVOdmVualJ0NEF4b2Y5SDVOWXhURjN2NXRsS2tCamNkYmZDeXJ0MXZMd2l3Ri9HRWJBTlUrWUFpdWMvV1owZEpUUXJEUkNWZzZlRFBXaml3TmRpbzZJOHJBQkFrTHRMTHBIQ20xVWU2a2xJUHBkUXNaOXVxMjZmQ1VkQWFLVnRaUEJqSHRKZnBKVkZXbFloSE5YL0dHUEVoSDFsa045R1lZYkRKNWg4cjI3ZVB5ZGIyR3lONEZTa3hRbnBKdHBGcEpOSkFLV0dNQTJWWm0iLCJtYWMiOiIxNjhiYTU5NjY1YTA0NGViZDFkMmE5NDU3MmQyODI4MjRlMmMyMGY0ODM4YjQ0NWIyOWM0ZmU3NjY0OTNjOTc0IiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 00:18:34 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6IkdyYlVjdlFDUlBNL2JMZjFDUnBLdlE9PSIsInZhbHVlIjoiZGpwVGU2anltOVFLZ3hsSXg2dlM3cFZCRkVNU3AwRnVWbVVWTnBHSm1vaXNSZ2x5UkEveHFveXM5UGl5SHN1WE1ZallWUTR4cVJPUFk0WEI1aGpkeGVCZjVXOXpsblRUamFzdE40Y054K2pWelpIV0o1OVZXVEtyNDlSNHhPN3dWNkVyRSsrQ3dsVDE0RmtvNmx0L2Z1UTY5RmN6Nmw1NUFlMFJNUytsWDlRZ3pyaHp1UFJQZi9lSk1VWk1hY0MraHBoc1lqWmF2bVhjaEd2aklUVmJucGlERWlhbTZjcWIxKysyNW5QU21ncm1BcE40cVdMV0toandQSXJ2K2ZYUlhUeXphMUFJUm04MUN2THpmTEZCQncrYUR6a2ZjakdFOXR5Sm1JRzg0QW10MXZkbVk2ZSt2OEc4WkswV1BBVk5FNitqYUdUM21BVWpYUG1BSk5pSGRFRktJYXhFcUtUQnBiOVpPc1dmM0FEcm1saEJDQUU0T2RUZUtrVWtCQVdGbVJkTEFnY0Q0RU1vL0JvRDNQNCt2Qk94UzVmbGhlU1p3TmpDN0dSOVl0dDNSQlphTDE0cU1nRzd3Z1FSQTZZWXVhUXdDVnpDNm01SlRGbThERmhualYzbE15QmZESm9zeDczZjMvd1UxOHU3VGVoNTlUTVdSMzhoM1htejZBYkYiLCJtYWMiOiIwNmM2Mjk5ZTU4NWQ1MThhYTc4OTZjZTE2ZDg5MzAzM2U2ODVjMDY5YzU2NDM3MGVlNTkxYWU5YjdlOWZlNTdjIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 00:18:34 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-712829265\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-736709839 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">cN6pwMHluqMWM9hLaRtOtRasnQF8pfN3ghxjsOfa</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"24 characters\">http://localhost/support</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-736709839\", {\"maxDepth\":0})</script>\n"}}