{"__meta": {"id": "X4dd27eebbe65c29782b9cad1c8e5839c", "datetime": "2025-06-07 22:21:28", "utime": **********.950867, "method": "GET", "uri": "/customer/check/delivery?customer_id=6", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749334887.65463, "end": **********.950906, "duration": 1.2962760925292969, "duration_str": "1.3s", "measures": [{"label": "Booting", "start": 1749334887.65463, "relative_start": 0, "end": **********.806786, "relative_end": **********.806786, "duration": 1.152156114578247, "duration_str": "1.15s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.806808, "relative_start": 1.1521780490875244, "end": **********.950911, "relative_end": 5.0067901611328125e-06, "duration": 0.1441030502319336, "duration_str": "144ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 43911704, "peak_usage_str": "42MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET customer/check/delivery", "middleware": "web, verified", "controller": "App\\Http\\Controllers\\CustomerController@checkDelivery", "namespace": null, "prefix": "", "where": [], "as": "customer.check.delivery", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FCustomerController.php&line=365\" onclick=\"\">app/Http/Controllers/CustomerController.php:365-378</a>"}, "queries": {"nb_statements": 2, "nb_failed_statements": 0, "accumulated_duration": 0.0063100000000000005, "accumulated_duration_str": "6.31ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 16 limit 1", "type": "query", "params": [], "bindings": ["16"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/AuthManager.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\AuthManager.php", "line": 57}, {"index": 23, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 33}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.9107249, "duration": 0.00495, "duration_str": "4.95ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 78.447}, {"sql": "select * from `customers` where `customers`.`id` = '6' limit 1", "type": "query", "params": [], "bindings": ["6"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/CustomerController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\CustomerController.php", "line": 368}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.9275892, "duration": 0.00136, "duration_str": "1.36ms", "memory": 0, "memory_str": null, "filename": "CustomerController.php:368", "source": "app/Http/Controllers/CustomerController.php:368", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FCustomerController.php&line=368", "ajax": false, "filename": "CustomerController.php", "line": "368"}, "connection": "ty", "start_percent": 78.447, "width_percent": 21.553}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\Customer": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FCustomer.php&line=1", "ajax": false, "filename": "Customer.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "iD3eOztlFQTk9Wu93brk2PH5kNLllwdWSLzbHLjf", "_previous": "array:1 [\n  \"url\" => \"http://localhost/hrm-dashboard\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "16", "pos": "array:1 [\n  3 => array:9 [\n    \"name\" => \"حمد\"\n    \"quantity\" => 1\n    \"price\" => \"10.00\"\n    \"id\" => \"3\"\n    \"tax\" => 0\n    \"subtotal\" => 10.0\n    \"originalquantity\" => 19\n    \"product_tax\" => \"-\"\n    \"product_tax_id\" => 0\n  ]\n]"}, "request": {"path_info": "/customer/check/delivery", "status_code": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>customer_id</span>\" => \"<span class=sf-dump-str>6</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">iD3eOztlFQTk9Wu93brk2PH5kNLllwdWSLzbHLjf</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1938 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=17ceeen%7C2%7Cfwk%7C0%7C1960; _clsk=1yftgw7%7C1749334872942%7C5%7C1%7Ck.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IkpSMkl6NXBBVDF5MWZQT2NySFJlbVE9PSIsInZhbHVlIjoiYzNYS09NZGVYcWRBREVyRjJhczU0cTdPNWRsRmgveEp2UzRBWnMxWTM4SjVqb3NOaGxYR2p2SkF1cmJjOXg1V25rdUovb3Q2MVh4RlJyRi9xYk9BbGlKMTJlTzRQelJuZnVET20vME5ZT3IrYmJoNCtVNWt6dlZaU0lsekduZW0rMWVVbFU3N2E4bjdxYUFyaERzdHlTLzlvc3EzMlRWS0dLeUtFMGp4c0EzZGdGN3dWVmEyQ1dSWXZtalRRTzMrT1pScUlPKzJzQW5xeVBCVGNUOWVmZVltYzE3aUMzUFQwemR1Y3Awdk8yejVvbCsyRXN2YnlrcGFMbHZ3QmxpTWZSVFZjbTNyVE5YUHB4L3BiTXRJQ1lLNXpFaUluZURqY3ZOdUx3QXBTTXhvNjEvYXNnVkczRXlZTW5jQW4xTjBjTGp0cWxFMUZNQlBsNDNrVFVWbTAyb1hjTjB5eit1SWJNdzh2WUcxY3Uzem5ESkVIVUdEa3R6c2d0UXhRTHo0WVZWYjEwbzRJRE4rMm9qc1VsZjVnSFhkU0M1a0hXeFZnTENqQXdLbDVZTGFObkduaGdHNkszNGp0ZC9IRWJwK0dnMks4OFhsUWY1ejJLSWVPRzEvVUlkZDhIcDN5SUVrMkJ0MER0dmtGWFVKQ2svQzVwN1NSSVRSeWxjK1FpUlkiLCJtYWMiOiJhZDlhNGMwMmM3NGViZGZjZTQwYTNiMDk3OWZmYTFiZTIwMmViMDBjYzZjMmFjMWUzMjM1MzQ2OGNlMzI3OGQzIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6IlBlbERxM25nT1hHQ1prYUNhaVhSTnc9PSIsInZhbHVlIjoialdHRG0wZHVMWmtYaHIwQ2p4TGFiOE5JRDJ1eWNrK1JWU3B2R3hSSFErZUs0UWt6UnNaR1hxZmFnVzBhdm54N000VzZTNVVXMElZQWhSdVVmVy8xaExKTCtOSzVYSmVraEI5U0JhWDlnWDNhc0NQcDdMejZYN1N2bHJsWG8xaDZoR2FsV0pXV0hNTnY2NkVBZFZkOWdPQVBjVlNLOGZWbjJxNTdCRTJoUlJoMk05bGp1R2dCM05HMGswNkVxRDJqYWhMdjA3Y0RyYVFoMzJGT2pYdndRQWdmb20xUDJjb3ZzajdiSXNEYzFkY1RGRkU2OVJ6cGtvNUdjbXFCVE5ZcXFDdXRGMWgxUjNtUmVybUZFRzNmS1Z3czhXYkphZmVUTWlQbVBvYlk5OUVDcmZYVkhyR29Kb1pNY0FYVnU3SGhHenVka2JwUHY3QS9QdUdHZU8wNlltU2liaEJkd3ZPTkFRdHJ0a1R5bnAxd0FJbXowQS9UL0ZNK2lBbS9pdnRoVnY2U0Z6dGt6Z1ZyQXRQcjJDckRDQzRTcDdMUnBFcy9KeGtiTWgzNTV0WWFJVDdaZldaR25SbnUyYjYzNEgvRXB1bHA0UE4vUW1POVFVaEVma1ZvSnh2eXRzeFNhc1Bqd2k2ZTgvTW1VNUZLbkoyYUY5RDg2SEFaU3hkbVpNUnYiLCJtYWMiOiJjMGQ0YWExMmZkYzNmZDRjZDNmYmE5YjQ0NWE1N2JjMTY2NTNkZDM5MGY3ZGZhYTc3MDI1NGZmNjk3MzUzMDk4IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-345309422 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">iD3eOztlFQTk9Wu93brk2PH5kNLllwdWSLzbHLjf</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">atMfj5qpj5gKx4OQYPP5PbQzRbuF7iB8X4zv4aOY</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-345309422\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-464888502 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 07 Jun 2025 22:21:28 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IkdMTDRwWGpENVJIKzI0TGVNelRmQVE9PSIsInZhbHVlIjoiUTN4REE4dlBkc1RKUFFvTitPS25DNjZ0eTJaa3JBQXBvNk9IMkhnM2lOLzBNMktnSHV1ZURxQ2gwNFQzaWNRRUJOcjZoVmVBRFR6Rno2aGM3YmczSjRzSG5kZE5MdTlvbHYzMnM2b1lhbThFNStONC9aOHcwQVIyZmZNUGR6Y3VRWU1FdVVkbGRkZGxiMVF1U25xMTVhY2hXWFJIVktQaUQvYnJ4MVpDdTE3RFVhUXplM25GT1kzZTBhTlc1TkdxM2NhWEhNcXNYcnYxdTZhemttMVRaQUp3V0w2K3BrSlhwQ1Y1RjU4NTZLNm1aTlQwMVNmN1VCQVNkOTQ1amxkZHd3MFYrR1NVUFZ5TjdOWmxkQXQ2VjF3Nndrb1FsSWpsZ01HQi9HTEJxWnZ1bHUycXArU1g5dE5PZHpSNWdza3B1MHg5bXdCcnZDaVZGWkxLY2h5NWFRaHMvMkZwTWI4VzZvblBpUWtzRk1nWVBMYmZjYkx0elRMZGNZVTVqMVJuWGhvSEljZGU3MWdUVG54ZEhGeFk1bGpQNDM3WGZya2JNNTlDN3o0MUJER005V3dtTXRCVXFzNGV5SzFnL0k3MnpjSTRGd09VTlpBaWRWT1I4MzFmQ1RZNTZkbGt4VlIrOE93RHplRHlDVnJScGZjelRIbE16SUQra0VEYXZjZXAiLCJtYWMiOiJlY2U2Mzc2NzRiZjVlMDBiODc0ZDgxZDRkODQ5MDVkZTM3Y2ZmZjhjZWIzYzFlMjA2ZDE2MDgyZTdmYjExODg4IiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 00:21:28 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6Ild0QnFHYW44MUcxL1AxalcvSnBacWc9PSIsInZhbHVlIjoiZEVBVjhOQWFlR1JPSkdSTzNHT3FvY0RVMmxDcjFjYkM0aGh4NW9vL200eTdweUFOQkZqeFJaUEd2Tm44dFBzbFVhNzN2RnVST0RWbGk1UG80VXNlMGdyLzZqZGM2dXQzcU5TbWt2Z2htbW9RYm14TWI0RC90MUFsU1BDaEJjYWgvdjBHeXA1bFRVNjk0ZDVsdjJOeFBVTXFXajZnd1I5bDlHdDI5aWpSNVNrWFNJU2VjNHdYYUFXV3JxM1g5S0hWV28wbUZsaTN1blM3emlvYzJ0UURqQ1VwOU5HSU12ZktlOWlOY3ZjWVVTbjNZeXFYTjlYKzIrQ25zQTJtU2VvUnZ2OE9aUmFpYlJ4alk2MFZGNkw0ZGpvV2xWcmQ5OStFOG5HZnVIcWdmazREcGpPV0FDWHRHMDVmVXkwTE54TUJ6aDk5R3lhR3o2T04xaHd5Ritlc3FmOGVqcFcrb3h5NnY1UzhXUUhEQlk2TlpTZTZVbVJJOFk3YzMyUUUzRENLek9mU3RPQzdqRjhiYzhLOFpudFU0WGVnMGRFWUZhMDRGNG1vVEN4ZXpxMXZERTcwSEhYclBkL2pGOVBWYWZOVWoveE5Sa0w4bDFWTm9PRGkxRjZFQlJUQlJrdVpSQm1LVHc0dUxDdjgyQ3VESHpkQlBsbHhxR2hNbVFYd0RpNXYiLCJtYWMiOiJlMzY1OTAwYTdjZTkyYzljMWZkM2VlMWM0Mzc4NmU0NGM3ZjkzZDE1NTJjYjU5NzdjYTJmZGExMjNiM2YwZjVmIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 00:21:28 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IkdMTDRwWGpENVJIKzI0TGVNelRmQVE9PSIsInZhbHVlIjoiUTN4REE4dlBkc1RKUFFvTitPS25DNjZ0eTJaa3JBQXBvNk9IMkhnM2lOLzBNMktnSHV1ZURxQ2gwNFQzaWNRRUJOcjZoVmVBRFR6Rno2aGM3YmczSjRzSG5kZE5MdTlvbHYzMnM2b1lhbThFNStONC9aOHcwQVIyZmZNUGR6Y3VRWU1FdVVkbGRkZGxiMVF1U25xMTVhY2hXWFJIVktQaUQvYnJ4MVpDdTE3RFVhUXplM25GT1kzZTBhTlc1TkdxM2NhWEhNcXNYcnYxdTZhemttMVRaQUp3V0w2K3BrSlhwQ1Y1RjU4NTZLNm1aTlQwMVNmN1VCQVNkOTQ1amxkZHd3MFYrR1NVUFZ5TjdOWmxkQXQ2VjF3Nndrb1FsSWpsZ01HQi9HTEJxWnZ1bHUycXArU1g5dE5PZHpSNWdza3B1MHg5bXdCcnZDaVZGWkxLY2h5NWFRaHMvMkZwTWI4VzZvblBpUWtzRk1nWVBMYmZjYkx0elRMZGNZVTVqMVJuWGhvSEljZGU3MWdUVG54ZEhGeFk1bGpQNDM3WGZya2JNNTlDN3o0MUJER005V3dtTXRCVXFzNGV5SzFnL0k3MnpjSTRGd09VTlpBaWRWT1I4MzFmQ1RZNTZkbGt4VlIrOE93RHplRHlDVnJScGZjelRIbE16SUQra0VEYXZjZXAiLCJtYWMiOiJlY2U2Mzc2NzRiZjVlMDBiODc0ZDgxZDRkODQ5MDVkZTM3Y2ZmZjhjZWIzYzFlMjA2ZDE2MDgyZTdmYjExODg4IiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 00:21:28 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6Ild0QnFHYW44MUcxL1AxalcvSnBacWc9PSIsInZhbHVlIjoiZEVBVjhOQWFlR1JPSkdSTzNHT3FvY0RVMmxDcjFjYkM0aGh4NW9vL200eTdweUFOQkZqeFJaUEd2Tm44dFBzbFVhNzN2RnVST0RWbGk1UG80VXNlMGdyLzZqZGM2dXQzcU5TbWt2Z2htbW9RYm14TWI0RC90MUFsU1BDaEJjYWgvdjBHeXA1bFRVNjk0ZDVsdjJOeFBVTXFXajZnd1I5bDlHdDI5aWpSNVNrWFNJU2VjNHdYYUFXV3JxM1g5S0hWV28wbUZsaTN1blM3emlvYzJ0UURqQ1VwOU5HSU12ZktlOWlOY3ZjWVVTbjNZeXFYTjlYKzIrQ25zQTJtU2VvUnZ2OE9aUmFpYlJ4alk2MFZGNkw0ZGpvV2xWcmQ5OStFOG5HZnVIcWdmazREcGpPV0FDWHRHMDVmVXkwTE54TUJ6aDk5R3lhR3o2T04xaHd5Ritlc3FmOGVqcFcrb3h5NnY1UzhXUUhEQlk2TlpTZTZVbVJJOFk3YzMyUUUzRENLek9mU3RPQzdqRjhiYzhLOFpudFU0WGVnMGRFWUZhMDRGNG1vVEN4ZXpxMXZERTcwSEhYclBkL2pGOVBWYWZOVWoveE5Sa0w4bDFWTm9PRGkxRjZFQlJUQlJrdVpSQm1LVHc0dUxDdjgyQ3VESHpkQlBsbHhxR2hNbVFYd0RpNXYiLCJtYWMiOiJlMzY1OTAwYTdjZTkyYzljMWZkM2VlMWM0Mzc4NmU0NGM3ZjkzZDE1NTJjYjU5NzdjYTJmZGExMjNiM2YwZjVmIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 00:21:28 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-464888502\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1345090545 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">iD3eOztlFQTk9Wu93brk2PH5kNLllwdWSLzbHLjf</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"30 characters\">http://localhost/hrm-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>16</span>\n  \"<span class=sf-dump-key>pos</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-key>3</span> => <span class=sf-dump-note>array:9</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"3 characters\">&#1581;&#1605;&#1583;</span>\"\n      \"<span class=sf-dump-key>quantity</span>\" => <span class=sf-dump-num>1</span>\n      \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"5 characters\">10.00</span>\"\n      \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str>3</span>\"\n      \"<span class=sf-dump-key>tax</span>\" => <span class=sf-dump-num>0</span>\n      \"<span class=sf-dump-key>subtotal</span>\" => <span class=sf-dump-num>10.0</span>\n      \"<span class=sf-dump-key>originalquantity</span>\" => <span class=sf-dump-num>19</span>\n      \"<span class=sf-dump-key>product_tax</span>\" => \"<span class=sf-dump-str>-</span>\"\n      \"<span class=sf-dump-key>product_tax_id</span>\" => <span class=sf-dump-num>0</span>\n    </samp>]\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1345090545\", {\"maxDepth\":0})</script>\n"}}