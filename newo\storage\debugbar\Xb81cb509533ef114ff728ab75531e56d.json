{"__meta": {"id": "Xb81cb509533ef114ff728ab75531e56d", "datetime": "2025-06-07 22:21:27", "utime": **********.636075, "method": "GET", "uri": "/customer/check/warehouse?customer_id=6&warehouse_id=8", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749334886.288441, "end": **********.636108, "duration": 1.3476669788360596, "duration_str": "1.35s", "measures": [{"label": "Booting", "start": 1749334886.288441, "relative_start": 0, "end": **********.474761, "relative_end": **********.474761, "duration": 1.1863200664520264, "duration_str": "1.19s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.474785, "relative_start": 1.1863441467285156, "end": **********.636111, "relative_end": 3.0994415283203125e-06, "duration": 0.16132593154907227, "duration_str": "161ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45178416, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET customer/check/warehouse", "middleware": "web, verified, auth, XSS, revalidate", "controller": "App\\Http\\Controllers\\CustomerController@checkWarehouse", "namespace": null, "prefix": "", "where": [], "as": "customer.check.warehouse", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FCustomerController.php&line=383\" onclick=\"\">app/Http/Controllers/CustomerController.php:383-397</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.02115, "accumulated_duration_str": "21.15ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 16 limit 1", "type": "query", "params": [], "bindings": ["16"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.56051, "duration": 0.01877, "duration_str": "18.77ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 88.747}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.605407, "duration": 0.00114, "duration_str": "1.14ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 88.747, "width_percent": 5.39}, {"sql": "select * from `customers` where `customers`.`id` = '6' limit 1", "type": "query", "params": [], "bindings": ["6"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/CustomerController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\CustomerController.php", "line": 388}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.6159139, "duration": 0.00124, "duration_str": "1.24ms", "memory": 0, "memory_str": null, "filename": "CustomerController.php:388", "source": "app/Http/Controllers/CustomerController.php:388", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FCustomerController.php&line=388", "ajax": false, "filename": "CustomerController.php", "line": "388"}, "connection": "ty", "start_percent": 94.137, "width_percent": 5.863}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\Customer": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FCustomer.php&line=1", "ajax": false, "filename": "Customer.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "iD3eOztlFQTk9Wu93brk2PH5kNLllwdWSLzbHLjf", "_previous": "array:1 [\n  \"url\" => \"http://localhost/hrm-dashboard\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "16", "pos": "array:1 [\n  3 => array:9 [\n    \"name\" => \"حمد\"\n    \"quantity\" => 1\n    \"price\" => \"10.00\"\n    \"id\" => \"3\"\n    \"tax\" => 0\n    \"subtotal\" => 10.0\n    \"originalquantity\" => 19\n    \"product_tax\" => \"-\"\n    \"product_tax_id\" => 0\n  ]\n]"}, "request": {"path_info": "/customer/check/warehouse", "status_code": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>customer_id</span>\" => \"<span class=sf-dump-str>6</span>\"\n  \"<span class=sf-dump-key>warehouse_id</span>\" => \"<span class=sf-dump-str>8</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-21277780 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">iD3eOztlFQTk9Wu93brk2PH5kNLllwdWSLzbHLjf</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1938 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=17ceeen%7C2%7Cfwk%7C0%7C1960; _clsk=1yftgw7%7C1749334872942%7C5%7C1%7Ck.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6Ii9Uc2h5blphcU9kcnBEQ1I0aXAyNmc9PSIsInZhbHVlIjoiMStyU2ZNL2pMc2hURUIrWmRoMFUyd0IxRGdlVEpDWFFURTZIVlJXRUpHakRDdjc0djdrdXFXdjAxd0JQcEhVMHNuN3ZIQ0xRbndWNXJkWnpJSXZuMTlseS9Wa3ZUSkxWK05MWnUyZC9kcm8zbmdFK1ZFdFdUZkRsY1NYTDBQaURPWGJNQ0IxZUF1aFZSZlVnOGplMEtXVXAzTkpSMGpra0lpVS83UUVGUy8zNkMyL0Y3alN5a1JEWjU0dGs4V3JqakxXYWxadmZTV1lIWDFocUFRRmhtczE1S3h5UXBmZHVWSWpVeHBnN1R0Rk9PR0ZUWUpwRld1RUI3cmQ3cy9wT2lwYitPQk8yTmJDbnE5T0ZpR0FrK3dNblN2WVN4VVlRMXk0Q3lWQ0w3VUU5cU9PZGlJcC9HV3F3bktxZGY3UnBZZmc2akNoUnpuTE0ySjg2KzVQdEFIc05TLzBxVHhzRi9sTFZZM1Y2eHpwVER4cXhrOTVLaVNRN1RmK3NIQk9QUHVUU1JOZXJGSnpHN1ptN3pOb1E0aHZqWVV4cGZNYWw3b09QSnVZa2U1RHlNZ3RNYlltRXpiRDVzSWNQVzI0R0xxOGRKL2xJUEpqZUhMU0xIWXI2N0dkTjRndUVSMXZkVWlQeW5xbWJNK0ZDYlo5SXVySElMYmtNeTdSS1NjQTUiLCJtYWMiOiIwNTNkNGNlMzhmM2NjNzQ1ZDEyMzc5OTI2YmMwY2ZjYjIxN2EzOTQ2NWNiY2ZhMjk2NjdkNzk5ODE4Yzk2NzliIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6Ikt3cWgxVG4yZjdvQmh1WmNwci9zQ1E9PSIsInZhbHVlIjoiakl4cTNVdTdCQTYyUDdOTHN2QVpncDZDbitQb2srRUkxbG1OMUVWQ0tTNURGTUZKcFRvalhVdXFiL0JMdi9JN2ZvckExUVFTQXBlOUllTVFFMVN3VzlseHVBejBMeURQYjBjTTdQZ0JBdHNPcmlFVm1uRGtSOXg5aFhnVGlwQkZaeWhQb3VRaGdxZGhFaTRzR3BGSnE4blZxemdyVEwwalVRaHQyZFBOWjREY1g0ZTBSRGlrUGlYdUVmeUpxVXJ3ejY0THJQZFpvQitvbVRXY0daMDJ3WW0xRHRITlVoaHRHanNyVkRweEJ6eDdXL3R1enRaakFpTkwrMjlaNnBsM2xXZjBHaER0OUZxLzhRYzY5U2tiZ3lDcm8zSEpFWCtIdGVrZzRaOHBvMzd4azJtalRLVk9ibHhxTy9PRTBQcnIzdnErZSt2VXNqMTFsWThrYTEwT3BnUVdYNWpzUEpGWE8rcXNxWVpHaUl3bzgrWjZLa3AvbFJsV0xnZlkzNUZIZW1SN1NGS2VhVUhYa3VtM3JIRmlnNzVYTUJSU0RkUHQxVy9lbnplSlJ3S2pseUN2SUlHTkdvYUk3VzdqZUQrYTFuTUlHMm1yejFXak9MbUszYmJOVGZmL2xCaER1bjR1Mkl5N0ltVld0bkhvSkppeXl3VmcwdVpUQVl0L2k4TjIiLCJtYWMiOiIwOGE2YTM0NmIzYjc2YTM0YjA5Nzg3OWI2YmQzMzQwY2I0NDRjZGU5N2NkMjYyZTMzMmNlOTkyMjdhMWRkNWVmIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-21277780\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-616535511 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">iD3eOztlFQTk9Wu93brk2PH5kNLllwdWSLzbHLjf</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">atMfj5qpj5gKx4OQYPP5PbQzRbuF7iB8X4zv4aOY</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-616535511\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-2036585884 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 07 Jun 2025 22:21:27 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-methods</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">POST, GET, OPTIONS, PUT, DELETE</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-headers</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"66 characters\">Content-Type, Accept, Authorization, X-Requested-With, Application</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IkpSMkl6NXBBVDF5MWZQT2NySFJlbVE9PSIsInZhbHVlIjoiYzNYS09NZGVYcWRBREVyRjJhczU0cTdPNWRsRmgveEp2UzRBWnMxWTM4SjVqb3NOaGxYR2p2SkF1cmJjOXg1V25rdUovb3Q2MVh4RlJyRi9xYk9BbGlKMTJlTzRQelJuZnVET20vME5ZT3IrYmJoNCtVNWt6dlZaU0lsekduZW0rMWVVbFU3N2E4bjdxYUFyaERzdHlTLzlvc3EzMlRWS0dLeUtFMGp4c0EzZGdGN3dWVmEyQ1dSWXZtalRRTzMrT1pScUlPKzJzQW5xeVBCVGNUOWVmZVltYzE3aUMzUFQwemR1Y3Awdk8yejVvbCsyRXN2YnlrcGFMbHZ3QmxpTWZSVFZjbTNyVE5YUHB4L3BiTXRJQ1lLNXpFaUluZURqY3ZOdUx3QXBTTXhvNjEvYXNnVkczRXlZTW5jQW4xTjBjTGp0cWxFMUZNQlBsNDNrVFVWbTAyb1hjTjB5eit1SWJNdzh2WUcxY3Uzem5ESkVIVUdEa3R6c2d0UXhRTHo0WVZWYjEwbzRJRE4rMm9qc1VsZjVnSFhkU0M1a0hXeFZnTENqQXdLbDVZTGFObkduaGdHNkszNGp0ZC9IRWJwK0dnMks4OFhsUWY1ejJLSWVPRzEvVUlkZDhIcDN5SUVrMkJ0MER0dmtGWFVKQ2svQzVwN1NSSVRSeWxjK1FpUlkiLCJtYWMiOiJhZDlhNGMwMmM3NGViZGZjZTQwYTNiMDk3OWZmYTFiZTIwMmViMDBjYzZjMmFjMWUzMjM1MzQ2OGNlMzI3OGQzIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 00:21:27 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6IlBlbERxM25nT1hHQ1prYUNhaVhSTnc9PSIsInZhbHVlIjoialdHRG0wZHVMWmtYaHIwQ2p4TGFiOE5JRDJ1eWNrK1JWU3B2R3hSSFErZUs0UWt6UnNaR1hxZmFnVzBhdm54N000VzZTNVVXMElZQWhSdVVmVy8xaExKTCtOSzVYSmVraEI5U0JhWDlnWDNhc0NQcDdMejZYN1N2bHJsWG8xaDZoR2FsV0pXV0hNTnY2NkVBZFZkOWdPQVBjVlNLOGZWbjJxNTdCRTJoUlJoMk05bGp1R2dCM05HMGswNkVxRDJqYWhMdjA3Y0RyYVFoMzJGT2pYdndRQWdmb20xUDJjb3ZzajdiSXNEYzFkY1RGRkU2OVJ6cGtvNUdjbXFCVE5ZcXFDdXRGMWgxUjNtUmVybUZFRzNmS1Z3czhXYkphZmVUTWlQbVBvYlk5OUVDcmZYVkhyR29Kb1pNY0FYVnU3SGhHenVka2JwUHY3QS9QdUdHZU8wNlltU2liaEJkd3ZPTkFRdHJ0a1R5bnAxd0FJbXowQS9UL0ZNK2lBbS9pdnRoVnY2U0Z6dGt6Z1ZyQXRQcjJDckRDQzRTcDdMUnBFcy9KeGtiTWgzNTV0WWFJVDdaZldaR25SbnUyYjYzNEgvRXB1bHA0UE4vUW1POVFVaEVma1ZvSnh2eXRzeFNhc1Bqd2k2ZTgvTW1VNUZLbkoyYUY5RDg2SEFaU3hkbVpNUnYiLCJtYWMiOiJjMGQ0YWExMmZkYzNmZDRjZDNmYmE5YjQ0NWE1N2JjMTY2NTNkZDM5MGY3ZGZhYTc3MDI1NGZmNjk3MzUzMDk4IiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 00:21:27 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IkpSMkl6NXBBVDF5MWZQT2NySFJlbVE9PSIsInZhbHVlIjoiYzNYS09NZGVYcWRBREVyRjJhczU0cTdPNWRsRmgveEp2UzRBWnMxWTM4SjVqb3NOaGxYR2p2SkF1cmJjOXg1V25rdUovb3Q2MVh4RlJyRi9xYk9BbGlKMTJlTzRQelJuZnVET20vME5ZT3IrYmJoNCtVNWt6dlZaU0lsekduZW0rMWVVbFU3N2E4bjdxYUFyaERzdHlTLzlvc3EzMlRWS0dLeUtFMGp4c0EzZGdGN3dWVmEyQ1dSWXZtalRRTzMrT1pScUlPKzJzQW5xeVBCVGNUOWVmZVltYzE3aUMzUFQwemR1Y3Awdk8yejVvbCsyRXN2YnlrcGFMbHZ3QmxpTWZSVFZjbTNyVE5YUHB4L3BiTXRJQ1lLNXpFaUluZURqY3ZOdUx3QXBTTXhvNjEvYXNnVkczRXlZTW5jQW4xTjBjTGp0cWxFMUZNQlBsNDNrVFVWbTAyb1hjTjB5eit1SWJNdzh2WUcxY3Uzem5ESkVIVUdEa3R6c2d0UXhRTHo0WVZWYjEwbzRJRE4rMm9qc1VsZjVnSFhkU0M1a0hXeFZnTENqQXdLbDVZTGFObkduaGdHNkszNGp0ZC9IRWJwK0dnMks4OFhsUWY1ejJLSWVPRzEvVUlkZDhIcDN5SUVrMkJ0MER0dmtGWFVKQ2svQzVwN1NSSVRSeWxjK1FpUlkiLCJtYWMiOiJhZDlhNGMwMmM3NGViZGZjZTQwYTNiMDk3OWZmYTFiZTIwMmViMDBjYzZjMmFjMWUzMjM1MzQ2OGNlMzI3OGQzIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 00:21:27 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6IlBlbERxM25nT1hHQ1prYUNhaVhSTnc9PSIsInZhbHVlIjoialdHRG0wZHVMWmtYaHIwQ2p4TGFiOE5JRDJ1eWNrK1JWU3B2R3hSSFErZUs0UWt6UnNaR1hxZmFnVzBhdm54N000VzZTNVVXMElZQWhSdVVmVy8xaExKTCtOSzVYSmVraEI5U0JhWDlnWDNhc0NQcDdMejZYN1N2bHJsWG8xaDZoR2FsV0pXV0hNTnY2NkVBZFZkOWdPQVBjVlNLOGZWbjJxNTdCRTJoUlJoMk05bGp1R2dCM05HMGswNkVxRDJqYWhMdjA3Y0RyYVFoMzJGT2pYdndRQWdmb20xUDJjb3ZzajdiSXNEYzFkY1RGRkU2OVJ6cGtvNUdjbXFCVE5ZcXFDdXRGMWgxUjNtUmVybUZFRzNmS1Z3czhXYkphZmVUTWlQbVBvYlk5OUVDcmZYVkhyR29Kb1pNY0FYVnU3SGhHenVka2JwUHY3QS9QdUdHZU8wNlltU2liaEJkd3ZPTkFRdHJ0a1R5bnAxd0FJbXowQS9UL0ZNK2lBbS9pdnRoVnY2U0Z6dGt6Z1ZyQXRQcjJDckRDQzRTcDdMUnBFcy9KeGtiTWgzNTV0WWFJVDdaZldaR25SbnUyYjYzNEgvRXB1bHA0UE4vUW1POVFVaEVma1ZvSnh2eXRzeFNhc1Bqd2k2ZTgvTW1VNUZLbkoyYUY5RDg2SEFaU3hkbVpNUnYiLCJtYWMiOiJjMGQ0YWExMmZkYzNmZDRjZDNmYmE5YjQ0NWE1N2JjMTY2NTNkZDM5MGY3ZGZhYTc3MDI1NGZmNjk3MzUzMDk4IiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 00:21:27 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2036585884\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1051531809 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">iD3eOztlFQTk9Wu93brk2PH5kNLllwdWSLzbHLjf</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"30 characters\">http://localhost/hrm-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>16</span>\n  \"<span class=sf-dump-key>pos</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-key>3</span> => <span class=sf-dump-note>array:9</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"3 characters\">&#1581;&#1605;&#1583;</span>\"\n      \"<span class=sf-dump-key>quantity</span>\" => <span class=sf-dump-num>1</span>\n      \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"5 characters\">10.00</span>\"\n      \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str>3</span>\"\n      \"<span class=sf-dump-key>tax</span>\" => <span class=sf-dump-num>0</span>\n      \"<span class=sf-dump-key>subtotal</span>\" => <span class=sf-dump-num>10.0</span>\n      \"<span class=sf-dump-key>originalquantity</span>\" => <span class=sf-dump-num>19</span>\n      \"<span class=sf-dump-key>product_tax</span>\" => \"<span class=sf-dump-str>-</span>\"\n      \"<span class=sf-dump-key>product_tax_id</span>\" => <span class=sf-dump-num>0</span>\n    </samp>]\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1051531809\", {\"maxDepth\":0})</script>\n"}}