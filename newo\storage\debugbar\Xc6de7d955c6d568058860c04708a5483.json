{"__meta": {"id": "Xc6de7d955c6d568058860c04708a5483", "datetime": "2025-06-07 22:18:21", "utime": **********.706944, "method": "GET", "uri": "/account-dashboard", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.191255, "end": **********.706982, "duration": 1.****************, "duration_str": "1.52s", "measures": [{"label": "Booting", "start": **********.191255, "relative_start": 0, "end": **********.490519, "relative_end": **********.490519, "duration": 1.****************, "duration_str": "1.3s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.490546, "relative_start": 1.****************, "end": **********.706987, "relative_end": 5.0067901611328125e-06, "duration": 0.*****************, "duration_str": "216ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": ********, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET account-dashboard", "middleware": "web, verified, auth, XSS, revalidate", "controller": "App\\Http\\Controllers\\DashboardController@account_dashboard_index", "namespace": null, "prefix": "", "where": [], "as": "dashboard", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FDashboardController.php&line=81\" onclick=\"\">app/Http/Controllers/DashboardController.php:81-181</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.022410000000000006, "accumulated_duration_str": "22.41ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.600816, "duration": 0.019420000000000003, "duration_str": "19.42ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 86.658}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.6462462, "duration": 0.00116, "duration_str": "1.16ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 86.658, "width_percent": 5.176}, {"sql": "select * from `migrations`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/rachidlaasri/laravel-installer/src/Helpers/MigrationsHelper.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\rachidlaasri\\laravel-installer\\src\\Helpers\\MigrationsHelper.php", "line": 29}, {"index": 14, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\XSS.php", "line": 40}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 16, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.680508, "duration": 0.00183, "duration_str": "1.83ms", "memory": 0, "memory_str": null, "filename": "MigrationsHelper.php:29", "source": "vendor/rachidlaasri/laravel-installer/src/Helpers/MigrationsHelper.php:29", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Frachidlaasri%2Flaravel-installer%2Fsrc%2FHelpers%2FMigrationsHelper.php&line=29", "ajax": false, "filename": "MigrationsHelper.php", "line": "29"}, "connection": "ty", "start_percent": 91.834, "width_percent": 8.166}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "cN6pwMHluqMWM9hLaRtOtRasnQF8pfN3ghxjsOfa", "_previous": "array:1 [\n  \"url\" => \"http://localhost/users\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "1", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/account-dashboard", "status_code": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:15</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/users</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1993 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=a091af98-270d-406e-ab7e-33a6e610d6e2414686; _clck=nups8l%7C2%7Cfwk%7C0%7C1960; _clsk=ij6lzq%7C1749334697734%7C4%7C1%7Ck.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IkdRVjZSOENjY3dJUnJyNEZDMDE1amc9PSIsInZhbHVlIjoidmdVaXdYR3pwMUhnRElOcEVTa0JTdHZtUHU3SVhpNnBNeTRBWTFnV1BsYTEwTlVRVEJqRWxaVlM2UjZPZzFSaWdFNmlDdnhhb1dKRDZFM0d0aHc1Y0xBemtEbGVpU1pkNTBKQkJGYUYzRklpUEFVWkZSY1htNENVRVJQbE5xQThpUmpUTVVLanNMUXZwNU5vcGJ5bVN4VmdUaFJVSDBldWhQbVZIOThpVWpwdEE1eFkyeFp4Uk1TL2tpOHVWK0tiZlNqS1NaU2J2RElzZTFSQzBHclpNZFJ2aGZpMTN6Y0NUQ3pDWTBCdEhSSzc2RjI0NEU3MDJPL3ptSVVSZytFMFpGNklSWWMwQmk3SUZBZWNqODQ1V3ZUcSsxck1XWmlDWTR4Nkl5VGxHamx1a2dpbjRkMHVaV0pVTU51ZW9YRktiTG8wcFUzY3N4UVlnU2pPNGpFWUl0eHNvaWpneTV5aVhjOXZBRWxRWDhqcWlIVzRnV0pNSDdyWVB1Z0ZXa3M3QWhVT0w1bEZPQXh6dmpUYnZmbWx6N1l3ME5lNzZROUd0c3h5Y2o0cGtEVXZJMjhLZXlqUElXOTU5b1E2NUxSRGNJeU0zUksrVUFjdTNqTVFJRDVvUm00R1FwZW9DQ045TjU1b0IzVVQ5TkFBTzJkcC96VEpLUHI5MzAwc2dwVWYiLCJtYWMiOiIxZGYyNmRhM2UxMzgwNGMxNjA5YThmMDUwODNlNGQ1MmVjYjM0MmUwODk2MWM5MTJlOTQzZjAzMzgyNjI4NTJkIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6IkZNSmhZUnpxUmg2V3ZsdU5RV0o0NWc9PSIsInZhbHVlIjoiZFNIZjJReGZmU0VaeG5KV04yK1hJSmVpZ3d1NFVud0IxM2pUUDVhcjM1UnBtbjR2dit1MHlyN0FvYWlkNDRsZk91NFVzaDdRQVN2SThTV0VpSStmbTQ1eFZIaU1nZC9RRHpCcTNqcGlMN25Od3hOUHduUTFnNmJiMHhQbnhPS1o2ZE5iakpNZ0VQZnB2TUNrWGw0aXg3d1hUR05MeGR1WEtnOW52SFdFQzlVUWxVSm95RmJ5Q2NYVFg2VmptcU1vcHdZWFpmNlBUb3Y4RlZnVDQ4VUVLUzQ2d3BmaUVaak15SlZ6RTJUNDU4UjFyY282SlM3alUrTXU1OTh4Qmh6MUhsWlgzaFZibERqeFU1UUp1ZSs3TjNod0pScVExaWxqeDc2dWo5NDVNam9uajExMXdTN1YxM21ZdDlTQmIxcGRFaWxIUy8vTmVkSlJpZzdIaXQ4OWFxVjJFTEs1aUIxSGFYakhVSEd0Tmw4WUZFWDR1a0d3Q1VUdzZ2cmh6ZGRmN2xRSkpJeXZzazdybUdlckt0V1pqS1V6cTVDcXYyc0hBVVI2cDFJbGVDUEJsemV5LzVocUlKcXR5ZVJBaFhqVUlxbGJVL0ZQOGVoYUxVNjF6S3hGdW04ck9FVUc2M3BqL3FMWmptY3pwQVkyVXFYMGJORTZxWnFpSmQxNEc1MEEiLCJtYWMiOiI3MDVmMWViZDQwNDAzZTA3MzU2ZjlhMmYwNThlMTg4NTM4YzczNTdjMjU3YTU4NGUzYWZiNmEzMzU2NDYwOTNjIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1286812686 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">cN6pwMHluqMWM9hLaRtOtRasnQF8pfN3ghxjsOfa</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">PmFRgUqysTWH0qB2ROlhITKoLIETZGW2KykVXtOx</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1286812686\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-604071942 data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 07 Jun 2025 22:18:21 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"26 characters\">http://localhost/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-methods</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">POST, GET, OPTIONS, PUT, DELETE</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-headers</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"66 characters\">Content-Type, Accept, Authorization, X-Requested-With, Application</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IlM1LzRWQ1JuS09oL1JXNlBIRG90aHc9PSIsInZhbHVlIjoibXhnUlJuYzZNZ2Z5MHNSYVRYRG5yS09zK2tXdDcvb05wSjg0aUhWZVpPSGl3aW1TL0xwa0lONFNwS3Y5MW42VmVFMDNLWHYvSEFUNUpTVGwyNkdQb1duYXlRS0ZyNkx3Q2IzdTZ6a1BOYW43Y0VaeDhQQmlXUWMzenpxaEZJbGFUUWxQcS9xWHlFV282RFYwdTNhU294OWkvaEd1dnZPWDBTV2R1RmtZMFpkMEN2eUxNTk5ZeVg0amNjVzdjNnBIeXlvZUZFckhiemlrTzBzTTBibDNMZDZ6M1FJMXEvY1hPUVpGdTVxTS96bWpTOEJoUjdKKzFYOWVIclIybkVIWlVYb0tBWHIzVjJwZVg5Yk83T0tVZ2JhL3ZtaER5OG9xcXFNODhSelc1Tkh4OS9lYmdFR0swZnFGd25EempYM1JHQUE5Sk5HZVVOWmMrNWU0WjN1UXU2cXVhYmdLMTR6dzBDUXpiQlk5R1BEdTJLS0FNU2JmSEV4NkQybUdRNG5oODgrMlBoMzdhLzNPdmJuSGFubnRBaW9taHE3V3FHbFJ0RWxKemlHQ1lqZGlzYmhiLzQyT25kU0N1RURJcVYxS0dFbUJ3MHpCRWdYZ0V3OGgrTVpWRnhORnFFQ0RZcCtIZEYvN1A1aU1sVGpsanZaWitZeVhmTGhta1pSbkNUb0wiLCJtYWMiOiIyMDE4YTUxMTQwMWIyZTQ4MmZjMDc0YjFkMGQzYTEzMDYxZWYwZWY2MzM0MGFlMmUwMzM0YjllMzE0NzBhN2Y4IiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 00:18:21 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6IjdnMHV1VFZsRG5oOGl0dkhja2pqN1E9PSIsInZhbHVlIjoiUVVFakF2cHVNekFSbnhtdXc4UTNtRjV6WFJ3c05lV1BPWkhwSm85VG1GcitxZGRNWXZtOE1yTzBGOWJ5YWJBVk5XZzk3WUkrUy9qQWpsQzFLYkZ4K29lS1o2eHhFVmpsTUZ5enJDbWZpSlVDclFtQmJqYjZ0RHNCSEdXUnBsRUpGWVUzUElSSVFBcy9JUUVHVWVJaExrVzV5NVlXRG9oeHhsMjVnbXowcXZkSUJmUzgwNHQ3eVlsKy9RSGxLWDZ0TXpCNmFHT3hsYUJLL01wY3RScXA2WUYrbEtUMXQ1emZOcHFWS2RDcHhhQmtNYTh1bGdZeWFybjBiUzJxRzZzK0xYbWRRWEZCanVvSFc3ci9jRFdNUndES0l5YzZ1NDhvNllrVmlsY1JCd1hTeS9uN09TbWtrOWU4THFUeFFhMHF2emZJOHlFekttREVwZE5JMFF6ZnhOaGNHQUlpcVV6UjFpbEsyRnZGSFhHL3VvSHNaREJBQlNLckNwZHl4eFdwblpXbmRRNWY3bEt2VGJtZ1ZIdkNKVW43aEtxT2ZoZlpIQ0Z1M2ZNcENjQWo5TkRlS0U0V2RXMjZibnFxaHJTemVnaEZDVld6WDNIZEFEZ3d5a3ZOWEE4amI0WWJxTnNtNjNkS3UxUzg1Sldic1R3c3hqamR6ZFBOWmE0bWh6RFUiLCJtYWMiOiI1ZWY2OWM4YTAzODUwZDQ3ZDAzMGU4OWJlNTNkNGM5NWQ4N2E3Y2Y0MTYzN2FkY2FiYWQwNGY3MWRlN2EzYmFiIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 00:18:21 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IlM1LzRWQ1JuS09oL1JXNlBIRG90aHc9PSIsInZhbHVlIjoibXhnUlJuYzZNZ2Z5MHNSYVRYRG5yS09zK2tXdDcvb05wSjg0aUhWZVpPSGl3aW1TL0xwa0lONFNwS3Y5MW42VmVFMDNLWHYvSEFUNUpTVGwyNkdQb1duYXlRS0ZyNkx3Q2IzdTZ6a1BOYW43Y0VaeDhQQmlXUWMzenpxaEZJbGFUUWxQcS9xWHlFV282RFYwdTNhU294OWkvaEd1dnZPWDBTV2R1RmtZMFpkMEN2eUxNTk5ZeVg0amNjVzdjNnBIeXlvZUZFckhiemlrTzBzTTBibDNMZDZ6M1FJMXEvY1hPUVpGdTVxTS96bWpTOEJoUjdKKzFYOWVIclIybkVIWlVYb0tBWHIzVjJwZVg5Yk83T0tVZ2JhL3ZtaER5OG9xcXFNODhSelc1Tkh4OS9lYmdFR0swZnFGd25EempYM1JHQUE5Sk5HZVVOWmMrNWU0WjN1UXU2cXVhYmdLMTR6dzBDUXpiQlk5R1BEdTJLS0FNU2JmSEV4NkQybUdRNG5oODgrMlBoMzdhLzNPdmJuSGFubnRBaW9taHE3V3FHbFJ0RWxKemlHQ1lqZGlzYmhiLzQyT25kU0N1RURJcVYxS0dFbUJ3MHpCRWdYZ0V3OGgrTVpWRnhORnFFQ0RZcCtIZEYvN1A1aU1sVGpsanZaWitZeVhmTGhta1pSbkNUb0wiLCJtYWMiOiIyMDE4YTUxMTQwMWIyZTQ4MmZjMDc0YjFkMGQzYTEzMDYxZWYwZWY2MzM0MGFlMmUwMzM0YjllMzE0NzBhN2Y4IiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 00:18:21 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6IjdnMHV1VFZsRG5oOGl0dkhja2pqN1E9PSIsInZhbHVlIjoiUVVFakF2cHVNekFSbnhtdXc4UTNtRjV6WFJ3c05lV1BPWkhwSm85VG1GcitxZGRNWXZtOE1yTzBGOWJ5YWJBVk5XZzk3WUkrUy9qQWpsQzFLYkZ4K29lS1o2eHhFVmpsTUZ5enJDbWZpSlVDclFtQmJqYjZ0RHNCSEdXUnBsRUpGWVUzUElSSVFBcy9JUUVHVWVJaExrVzV5NVlXRG9oeHhsMjVnbXowcXZkSUJmUzgwNHQ3eVlsKy9RSGxLWDZ0TXpCNmFHT3hsYUJLL01wY3RScXA2WUYrbEtUMXQ1emZOcHFWS2RDcHhhQmtNYTh1bGdZeWFybjBiUzJxRzZzK0xYbWRRWEZCanVvSFc3ci9jRFdNUndES0l5YzZ1NDhvNllrVmlsY1JCd1hTeS9uN09TbWtrOWU4THFUeFFhMHF2emZJOHlFekttREVwZE5JMFF6ZnhOaGNHQUlpcVV6UjFpbEsyRnZGSFhHL3VvSHNaREJBQlNLckNwZHl4eFdwblpXbmRRNWY3bEt2VGJtZ1ZIdkNKVW43aEtxT2ZoZlpIQ0Z1M2ZNcENjQWo5TkRlS0U0V2RXMjZibnFxaHJTemVnaEZDVld6WDNIZEFEZ3d5a3ZOWEE4amI0WWJxTnNtNjNkS3UxUzg1Sldic1R3c3hqamR6ZFBOWmE0bWh6RFUiLCJtYWMiOiI1ZWY2OWM4YTAzODUwZDQ3ZDAzMGU4OWJlNTNkNGM5NWQ4N2E3Y2Y0MTYzN2FkY2FiYWQwNGY3MWRlN2EzYmFiIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 00:18:21 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-604071942\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1081786184 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">cN6pwMHluqMWM9hLaRtOtRasnQF8pfN3ghxjsOfa</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/users</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1081786184\", {\"maxDepth\":0})</script>\n"}}