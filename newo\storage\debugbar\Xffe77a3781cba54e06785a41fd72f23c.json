{"__meta": {"id": "Xffe77a3781cba54e06785a41fd72f23c", "datetime": "2025-06-07 22:36:35", "utime": **********.431664, "method": "GET", "uri": "/add-to-cart/3/pos", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749335794.681462, "end": **********.431687, "duration": 0.7502250671386719, "duration_str": "750ms", "measures": [{"label": "Booting", "start": 1749335794.681462, "relative_start": 0, "end": **********.265603, "relative_end": **********.265603, "duration": 0.5841410160064697, "duration_str": "584ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.26562, "relative_start": 0.5841579437255859, "end": **********.43169, "relative_end": 2.86102294921875e-06, "duration": 0.16606998443603516, "duration_str": "166ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 53610400, "peak_usage_str": "51MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET add-to-cart/{id}/{session}", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\ProductServiceController@addToCart", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FProductServiceController.php&line=1236\" onclick=\"\">app/Http/Controllers/ProductServiceController.php:1236-1493</a>"}, "queries": {"nb_statements": 6, "nb_failed_statements": 0, "accumulated_duration": 0.02008, "accumulated_duration_str": "20.08ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 16 limit 1", "type": "query", "params": [], "bindings": ["16"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.325715, "duration": 0.01466, "duration_str": "14.66ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 73.008}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.354687, "duration": 0.00086, "duration_str": "860μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 73.008, "width_percent": 4.283}, {"sql": "select `permissions`.*, `model_has_permissions`.`model_id` as `pivot_model_id`, `model_has_permissions`.`permission_id` as `pivot_permission_id`, `model_has_permissions`.`model_type` as `pivot_model_type` from `permissions` inner join `model_has_permissions` on `permissions`.`id` = `model_has_permissions`.`permission_id` where `model_has_permissions`.`model_id` = 16 and `model_has_permissions`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["16", "App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 326}, {"index": 21, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 566}], "start": **********.381434, "duration": 0.00108, "duration_str": "1.08ms", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:326", "source": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php:326", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=326", "ajax": false, "filename": "HasPermissions.php", "line": "326"}, "connection": "ty", "start_percent": 77.291, "width_percent": 5.378}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (16) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 312}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 26, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}], "start": **********.385773, "duration": 0.0011, "duration_str": "1.1ms", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 82.669, "width_percent": 5.478}, {"sql": "select * from `product_services` where `product_services`.`id` = '3' limit 1", "type": "query", "params": [], "bindings": ["3"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/ProductServiceController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\ProductServiceController.php", "line": 1240}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.395427, "duration": 0.00099, "duration_str": "990μs", "memory": 0, "memory_str": null, "filename": "ProductServiceController.php:1240", "source": "app/Http/Controllers/ProductServiceController.php:1240", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FProductServiceController.php&line=1240", "ajax": false, "filename": "ProductServiceController.php", "line": "1240"}, "connection": "ty", "start_percent": 88.147, "width_percent": 4.93}, {"sql": "select sum(`quantity`) as aggregate from `warehouse_products` where `product_id` = 3 and exists (select * from `warehouses` where `warehouse_products`.`warehouse_id` = `warehouses`.`id` and `created_by` = 15)", "type": "query", "params": [], "bindings": ["3", "15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/ProductService.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\ProductService.php", "line": 155}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/ProductServiceController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\ProductServiceController.php", "line": 1244}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.40435, "duration": 0.00139, "duration_str": "1.39ms", "memory": 0, "memory_str": null, "filename": "ProductService.php:155", "source": "app/Models/ProductService.php:155", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FProductService.php&line=155", "ajax": false, "filename": "ProductService.php", "line": "155"}, "connection": "ty", "start_percent": 93.078, "width_percent": 6.922}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}, "App\\Models\\ProductService": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FProductService.php&line=1", "ajax": false, "filename": "ProductService.php", "line": "?"}}}, "count": 3, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 1, "messages": [{"message": "[\n  ability => manage product & service,\n  result => true,\n  user => 16,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1136872336 data-indent-pad=\"  \"><span class=sf-dump-note>manage product & service</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"24 characters\">manage product &amp; service</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>16</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1136872336\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.393758, "xdebug_link": null}]}, "session": {"_token": "iD3eOztlFQTk9Wu93brk2PH5kNLllwdWSLzbHLjf", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "16", "pos": "array:1 [\n  3 => array:9 [\n    \"name\" => \"حمد\"\n    \"quantity\" => 1\n    \"price\" => \"10.00\"\n    \"id\" => \"3\"\n    \"tax\" => 0\n    \"subtotal\" => 10.0\n    \"originalquantity\" => 19\n    \"product_tax\" => \"-\"\n    \"product_tax_id\" => 0\n  ]\n]"}, "request": {"path_info": "/add-to-cart/3/pos", "status_code": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-67042255 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">iD3eOztlFQTk9Wu93brk2PH5kNLllwdWSLzbHLjf</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1938 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=17ceeen%7C2%7Cfwk%7C0%7C1960; _clsk=1yftgw7%7C1749334872942%7C5%7C1%7Ck.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6ImZEQy9qN2dnblZYQzFnM2lZWG1zVmc9PSIsInZhbHVlIjoiTzJXTlB3bDY2MktUMmVqVU9HeXJSZlFuVzNPc2FXcVFXM014a0xPSXliWjR0cEVQT0gxcWs2M2prM1BxNlB6WEd4MU5Ebmp3Z3FaeGZzY3dDY01qMW54Q3N1QlNzdWw2UDhyREpBU2o3WU5sMnN5MTRPM1hHZXNZTDVKRy9Qblh4ckIybldnQVBJQUQ5cThrMThITzRtWmVBckpIcmlESFNQRU9OVXhXbHZlZE9ROXUxT1B3ZEsrdHF4MHgybHJvMFY1Sm1FZHVENUZ1OHkvSWd2QlovWHhON3BFbktlcnFTUFhsYzFVTGdLNVViTlNYTmVCTG41ZGR4S0lyM0c4Y2gwTEVycGlMYW02cEswWWRvcU9xTGxvWGFsbVQyYTkzNDc5WUtrRnNZRGJtS2xDWk8zSDR0c3lxVzJxeUFEcFJ4NUhtK3k4eXJXYUtBclFhdHFLeC9Eb0VkRE9iNU9vK1BsQW80LzB1a3RxT1AyTWJiNXlUV3hvZ01rOFY1Nlg1U202NlcxS3kvcmYwYWhOeFc4SlhXSEt5SDlQVkpBV1hzbHREaXRSa2Z4SlV5YUQ0UFR5dE5sQjllWlE0ZWhpQnJTR0p6RWJWK3FZMzNuRCtqOFZkdEN0YXdJaXovTE91SGhyWUpRTGtmZEN3REcwNUxsQzdVaHpxTlhVVzltZmwiLCJtYWMiOiI1NDg5NDQ4YTgxODQ4MDlmMGRiYWU0MGIwMzFiMjBjYjc3YmNiNzM0NzFhMTFlMjIzNmU3YTcyZjA5ZTAwODk3IiwidGFnIjoiIn0%3D; test_session=eyJpdiI6Ikxld0VqTkYrYmhrak5PSEJVUDk1d0E9PSIsInZhbHVlIjoiRngwbGpnelFjNE5WbUNEYkdXQ3crQldYZi9LbURkTUNzRmVhQ2hXTUdQWXdvS3U3cWhMNVZQZzR2UXNSN2ovVGhXREtyMms1RTU2YjBUWHFlME9ZdzJsY1F3b09wSTBQYXVlUjVybTl6OGJldXdhWFlSMmJOTElQRFFCSUxWNGtUbG5PemhUODlNOUdKTWdsQkZVSXVMbXczTXdUTllmQ0JjMWlVMEJ6QlAyMVdPN0MrUENPV25zTFZYWGNqaFBiaENZU2ZTM3lHb1g1VjZUNHlaWWNHeTROWWRQaHkwemxrZzl5bEU4L0xCWDdOL2RDRGNYR2N4S1ZMYkJXelk3SndiLzdNL244b2FDTTN1NnYzQy80NzhkY3BUbFRKL09wSHMxeGpIL0RrRzBoWkdidU9XQVVEVGlkWkdpVm5CVjVEdnZhV2xRY2NzcU1zOG5XaTMwKzhjUFhycTNaRExUYnoza3p4dk9RZ0VkSWNnYUdxR0dvaXFibXVETXRrWEpUSGd1ZjFoNW9RcExaRjRmeElBSEZacDlod05ZNGJOYUhZVjZGVkJGNTkzUHZ3OXdESlkzWE9xVzFlbWhUUW5WYllaTUEwZVJkbFRDRFdTdGJnU3VwVGFPejZOVUtvNVBLNnpGSGZWdHNpRFVTbDdheGZLL3NTQ0hYL24zSEpWYnoiLCJtYWMiOiJmZmJmMjJlYTQyMjVjMDNkZWYxMzhkMDM5ZmIxMjNlMjgyYzNiMzk4YjU3M2E1ODg5MGI5MWQ2N2Q0Yzg5ZTliIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-67042255\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1973282717 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">iD3eOztlFQTk9Wu93brk2PH5kNLllwdWSLzbHLjf</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">atMfj5qpj5gKx4OQYPP5PbQzRbuF7iB8X4zv4aOY</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1973282717\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-139984847 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 07 Jun 2025 22:36:35 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Ilp3Uyt6dW13VnJwQXc2YU5KU1NkdWc9PSIsInZhbHVlIjoiVlZKYXJDeFg5UEJLWHBUWnhXWjZpMmdMRUowQ1FEUTREZ1dFV0R2bThpeS9rRWFvMlJCNHhaaFZ0eTJCREtFTUZ6QmlnZHhyQkZpNGdvUGV6aHFkbnd6aFlGMkpPQXBmM2VSallOTHRiaUpxNG9Fb2NnRktoaFo0bUpKbFVLcGV3d2xtcmsvQ1d0UXNPWGdrRUw1NU9FU1pxTlFmdlFZamNNUlRLNElFcTk5UUhCdjBodTNCaTNCUUxESjVUMFlCYTAzVGsvUEFJdU9NeXZuTUZic3UyMzhRSHdQc2l6WVphbGNMcUZsMnJQYVZ3YktvZVkvbSs5ekNrdWk5aWhhdGUrUzBOZ1p5b2duOEJock5oWUpKRklKQmMxM1RKQjJ2U0RwOWVtNVpLQlJWc2hiandrV3Q0OHpGYTRhL1dTR1A4WnAvUmgzUEZERTlvY0dONUlMYzk0bUdnSE4wa0hVeTZBRkRVZnF3dldzQ08yMU94ZWM5UnNEdlJyems5dnNwSG10emVtVExnV1B2eW1LNWdpSjNWL3c4OGZGSDZBUkFib2VmRmNia1JoVVNuZXExSExFbC9pT29VNWlEZDZmSHVxV3VKL3l3bkdscUs5VGd2Q3JKQUlPRVEyTm9BeFd5NjY2aWthSjlVU0FOZ2FWUUJIYkhBS2FHS0xYTkFPK1YiLCJtYWMiOiI0NjNlMTcwOWViOWFmNjM3MmZlZDAwZDRjZmU5YWVkMDNkOTdmMWY3YWEyNmQ1YzI2YWEyYzlmMjYwZjUwNWI1IiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 00:36:35 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6ImVPVkU4Y01FTkxqVzhjSUlHVExYNEE9PSIsInZhbHVlIjoiZjZqRmNJUTBmV1VpaXhNN2VVcnhVbXdoTnB3b1A3UVppUkpaL2NpeWFkVGlTWU1pdEh1UWRETkU3QktSMFpDeGgvZDB6aUJ5OEpDYVRjSmgxZmlqb0NTaDZhMjlrVWJxMWIrV1MzWk5Zb21CS2dCaFFnMUp4b2ZVbzhDU2tqUlBlekpvYVdHV0xScjFkM0ZONmhLQkJDekU2MjVrRDd2WG10aU84Y2ZwWGFGcWVpMVFkRHY2WGZzU3BvbFdtS0lVbCt1SHdmcGRIMHhaaTVHd1pabmpqYStjWDVnQzd6UzNlTGl3bTU4Y3htc01hdnZ2Z1h5cjI3QXdxVU1RckJYNU1BRTMwUVN3cmFwOFFsUFFoZUIwWkdKQ0lrY3FtYVJ6QnU3bjk1NnhNZmc0aDI1UlhRMDJDVkZKd0VDRW4vdTZrTWJOYm1Ybmw4VzFiemgxTW9QRFBCS21lZE5VNlFjdDBoZzZHYndyNHpCZlhBYXEySlFGdEFIS3hQKzM1Q3BHTXJyQnZMK3RNRzQvNzNDYjJzZEpSTkxidWJsU3ZKQWNtQ2tMZytHVUY2Ukx5cGxaQ3VNeHRIa0o2RnpZVmsraXlIMVVyTU91ZE45OG1ycisycHlGN3RKckl6YTROQ2oxeHNhVTA2alVuN05KY0lwRmxrVkhVME8ySFAxQ2NpaEIiLCJtYWMiOiJhOGI4NjQ4NmZiNGI1OTJmZTc0ODdlMmExZDE5Y2Q0MjMwMjRkMWVlOTU1YjU0NmE1MjVmZTI3MjM5YjZmZGFhIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 00:36:35 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Ilp3Uyt6dW13VnJwQXc2YU5KU1NkdWc9PSIsInZhbHVlIjoiVlZKYXJDeFg5UEJLWHBUWnhXWjZpMmdMRUowQ1FEUTREZ1dFV0R2bThpeS9rRWFvMlJCNHhaaFZ0eTJCREtFTUZ6QmlnZHhyQkZpNGdvUGV6aHFkbnd6aFlGMkpPQXBmM2VSallOTHRiaUpxNG9Fb2NnRktoaFo0bUpKbFVLcGV3d2xtcmsvQ1d0UXNPWGdrRUw1NU9FU1pxTlFmdlFZamNNUlRLNElFcTk5UUhCdjBodTNCaTNCUUxESjVUMFlCYTAzVGsvUEFJdU9NeXZuTUZic3UyMzhRSHdQc2l6WVphbGNMcUZsMnJQYVZ3YktvZVkvbSs5ekNrdWk5aWhhdGUrUzBOZ1p5b2duOEJock5oWUpKRklKQmMxM1RKQjJ2U0RwOWVtNVpLQlJWc2hiandrV3Q0OHpGYTRhL1dTR1A4WnAvUmgzUEZERTlvY0dONUlMYzk0bUdnSE4wa0hVeTZBRkRVZnF3dldzQ08yMU94ZWM5UnNEdlJyems5dnNwSG10emVtVExnV1B2eW1LNWdpSjNWL3c4OGZGSDZBUkFib2VmRmNia1JoVVNuZXExSExFbC9pT29VNWlEZDZmSHVxV3VKL3l3bkdscUs5VGd2Q3JKQUlPRVEyTm9BeFd5NjY2aWthSjlVU0FOZ2FWUUJIYkhBS2FHS0xYTkFPK1YiLCJtYWMiOiI0NjNlMTcwOWViOWFmNjM3MmZlZDAwZDRjZmU5YWVkMDNkOTdmMWY3YWEyNmQ1YzI2YWEyYzlmMjYwZjUwNWI1IiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 00:36:35 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6ImVPVkU4Y01FTkxqVzhjSUlHVExYNEE9PSIsInZhbHVlIjoiZjZqRmNJUTBmV1VpaXhNN2VVcnhVbXdoTnB3b1A3UVppUkpaL2NpeWFkVGlTWU1pdEh1UWRETkU3QktSMFpDeGgvZDB6aUJ5OEpDYVRjSmgxZmlqb0NTaDZhMjlrVWJxMWIrV1MzWk5Zb21CS2dCaFFnMUp4b2ZVbzhDU2tqUlBlekpvYVdHV0xScjFkM0ZONmhLQkJDekU2MjVrRDd2WG10aU84Y2ZwWGFGcWVpMVFkRHY2WGZzU3BvbFdtS0lVbCt1SHdmcGRIMHhaaTVHd1pabmpqYStjWDVnQzd6UzNlTGl3bTU4Y3htc01hdnZ2Z1h5cjI3QXdxVU1RckJYNU1BRTMwUVN3cmFwOFFsUFFoZUIwWkdKQ0lrY3FtYVJ6QnU3bjk1NnhNZmc0aDI1UlhRMDJDVkZKd0VDRW4vdTZrTWJOYm1Ybmw4VzFiemgxTW9QRFBCS21lZE5VNlFjdDBoZzZHYndyNHpCZlhBYXEySlFGdEFIS3hQKzM1Q3BHTXJyQnZMK3RNRzQvNzNDYjJzZEpSTkxidWJsU3ZKQWNtQ2tMZytHVUY2Ukx5cGxaQ3VNeHRIa0o2RnpZVmsraXlIMVVyTU91ZE45OG1ycisycHlGN3RKckl6YTROQ2oxeHNhVTA2alVuN05KY0lwRmxrVkhVME8ySFAxQ2NpaEIiLCJtYWMiOiJhOGI4NjQ4NmZiNGI1OTJmZTc0ODdlMmExZDE5Y2Q0MjMwMjRkMWVlOTU1YjU0NmE1MjVmZTI3MjM5YjZmZGFhIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 00:36:35 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-139984847\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">iD3eOztlFQTk9Wu93brk2PH5kNLllwdWSLzbHLjf</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>16</span>\n  \"<span class=sf-dump-key>pos</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-key>3</span> => <span class=sf-dump-note>array:9</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"3 characters\">&#1581;&#1605;&#1583;</span>\"\n      \"<span class=sf-dump-key>quantity</span>\" => <span class=sf-dump-num>1</span>\n      \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"5 characters\">10.00</span>\"\n      \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str>3</span>\"\n      \"<span class=sf-dump-key>tax</span>\" => <span class=sf-dump-num>0</span>\n      \"<span class=sf-dump-key>subtotal</span>\" => <span class=sf-dump-num>10.0</span>\n      \"<span class=sf-dump-key>originalquantity</span>\" => <span class=sf-dump-num>19</span>\n      \"<span class=sf-dump-key>product_tax</span>\" => \"<span class=sf-dump-str>-</span>\"\n      \"<span class=sf-dump-key>product_tax_id</span>\" => <span class=sf-dump-num>0</span>\n    </samp>]\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n"}}