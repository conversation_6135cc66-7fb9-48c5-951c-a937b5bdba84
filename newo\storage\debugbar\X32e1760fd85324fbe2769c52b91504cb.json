{"__meta": {"id": "X32e1760fd85324fbe2769c52b91504cb", "datetime": "2025-06-07 23:58:28", "utime": **********.497646, "method": "POST", "uri": "/chats/getContacts", "ip": "127.0.0.1"}, "php": {"version": "8.3.16", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749340707.474401, "end": **********.497678, "duration": 1.0232770442962646, "duration_str": "1.02s", "measures": [{"label": "Booting", "start": 1749340707.474401, "relative_start": 0, "end": **********.361208, "relative_end": **********.361208, "duration": 0.8868069648742676, "duration_str": "887ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.361225, "relative_start": 0.8868238925933838, "end": **********.497682, "relative_end": 4.0531158447265625e-06, "duration": 0.13645720481872559, "duration_str": "136ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45044352, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/getContacts", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getContacts", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "contacts.get", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=333\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:333-390</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.02746, "accumulated_duration_str": "27.46ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.423935, "duration": 0.02514, "duration_str": "25.14ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 91.551}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.468396, "duration": 0.00103, "duration_str": "1.03ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 91.551, "width_percent": 3.751}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (15) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.480778, "duration": 0.0012900000000000001, "duration_str": "1.29ms", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 95.302, "width_percent": 4.698}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "cN6pwMHluqMWM9hLaRtOtRasnQF8pfN3ghxjsOfa", "_previous": "array:1 [\n  \"url\" => \"http://localhost:8000/productservice\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/getContacts", "status_code": "<pre class=sf-dump id=sf-dump-1064085489 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1064085489\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-451011104 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-451011104\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1708034787 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">cN6pwMHluqMWM9hLaRtOtRasnQF8pfN3ghxjsOfa</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1708034787\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1172262955 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">http://localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"36 characters\">http://localhost:8000/productservice</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1995 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=a091af98-270d-406e-ab7e-33a6e610d6e2414686; _clck=nups8l%7C2%7Cfwk%7C0%7C1960; XSRF-TOKEN=eyJpdiI6Ii9IYkdPOTNRZlNtbEJkS3BpZ0JFaHc9PSIsInZhbHVlIjoib1hETUZDVTRDN2dhWjlDYzlBbWJGVStsVk9peFV4dDNXZU9OMnpwUXhTSXN1ZHpVZ216bmt5Ni9BVDFYR0svUEJpODFndzFwcmhUelNheVVINlF6dkNUaWlWWkpLN1VKd2F0S3J3dVhweUVRT3IwQmI4YUoyeUVVM1ZBVTdQSmNnY0RPRElYMVRQbzhJUXhtTXFwT05sT05RSDFZVjRaQ3NtVHhPYWxQL3RpYkFGTXJqcVNyaFFMckFqcWZyYUx0YmlqMmFFRmc3Nng5d2sxOTdEK3BuUmhwVWd5dGpiV3g3NFlsNlZJeXcyOXFhSVFyZ2RwRW0reDVDbGpveVFCYVlWZkxJMmdWUUVDZTJwdDJVSWJObTBNZURORFA5SEVQR2t3bGxuSkRFVU9rcW1LVjE1WTAyMFJGc0RFK2cxSDNJSlhYM1ltTDNTbWlFV2dCM2Y5d3REOUt3eHAyQkJSMVRSaGtUMWtoMWYvb0dMdzBnNHlYL3RsYmkybDNjTjdzZlJ5NWdHYXU4NVBmaDdjNm5Mcm9jMFZ6TFROTFE1ZU9iQVFoM0RnQitFK01DZExVSTNaY3BGMHJHM1IreDdHUGJUVWpacTcrcWpkUHhWNm9Ua1FXWkJEcGlCajBUbllieWw3MzYrbW10QzJlTFpzbmlmeVNvbVdKelFMT3NpYkQiLCJtYWMiOiI5YTJjNmMwZjUzZjRkM2Q1Y2E4NzcyOGI0ZTQ3MTFkNzQ4NzI2MzRlNTEyYTgzZjcxMmU4ODllNTg3NmJmNTY2IiwidGFnIjoiIn0%3D; test_session=eyJpdiI6IlRnRTlGTWxmL2NDelZIVyszTmltL2c9PSIsInZhbHVlIjoieFFPa2IxbkYwbEZYOWlEc1Axa0ZNcytzTkxvOG9VUWI3bW5hTXhFQmtETkRFZkZPSVZjVldTWTh4QVkzYUNwdGV4MjIrRG5ORSt6d2lFRThQTDJtdWpreFFBT0EvMHlDTEFBcjNqQlRxc0FZRE9ENG1rOGNjVWZUbmNMdGkrS1p3Y1M4UXR0SWxTemVTR0N1WDdzMjJTTDFva2UyZTRXc29YN1BEeG5jUVRBQnNrMzY2cWY2OE5vdXFNbytmNzM3TDVHNDF0NFRIU01FRFdjOFJlbjNMYUt3bWtzU2c3ankwMSt5a0l4ZzhRSE5jS3l5cTROOHMxazRlMEVKSS8yQnp3VWkyU2tkTmY3ZUVLT3hDc2todFVOUEdNN1NNSUlyZ0tJZm1QSllCaDN1NmpKOXhOWktkYUljUFRTaUU4VTYrSFV2NjFhMHRlRFNzZXJWMDRBTjJLNmlpaXBtTWhTaVdxU2Y0cE9qMWswaHBycnJSdTR4N3FmdnNENElUZGQreFNoeDI5NkVyRWRmajdyM0praFhVbGV6NTJDcUxYaFFhTlpCY2hkY2piWUJBTlBGU203STNWRmhjMTM3RytnZjBvL0t4N01sYXZOUDJYTGc1NUlXcnZCQXlKS0JleXM3Q29VbnBvMmMrcWsrZmNmdmJDQ1BOYXh1MlF2bTMxT2YiLCJtYWMiOiIyNzQ4ZDliNjdlMjVlYmZkYTNjM2IyZDgyNWM5YmUyZWY2ZDcyNGRjYmZiZGE3NGVmNWU3OTkyMmMxZjY4MjdjIiwidGFnIjoiIn0%3D; _clsk=1dgl8io%7C1749340706378%7C22%7C1%7Ck.clarity.ms%2Fcollect</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1172262955\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1145587875 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">cN6pwMHluqMWM9hLaRtOtRasnQF8pfN3ghxjsOfa</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Eo35AMmp3Bkf2zsyHLroae0N6MdUih7xJ0ojLwSF</span>\"\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1145587875\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1155122351 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 07 Jun 2025 23:58:28 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"39 characters\">http://localhost:8000/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6ImVPclFFc3Q5QkxsbUo0bys0Y2lIT2c9PSIsInZhbHVlIjoibnJMK3pwWXk5WDFMQ3hHOXVPRDV0NHQ3M3NQdWFhRXAxTnRHbE1rY3NtWkJrL1Z4UjdrR1hXUkp5c2x3d2o2TjNnaTduT0trWFY2azY3SUl0N0hkSkJWRWpiek4xTGJMaHp0clczcFJhdWpCWjhqZXcvalBiWksvS2hnZkQvR29QRm5JVFR6RU8xaDZiRnJ1V1lxUWdPRnFCNXRYOFdNM2tpdlAzeTFaT3IrOXdmMnZsV3NrMWZkbEpyQXhxUHV5MzJsRmh6bXY1NE93bHh0YTdaV3J4aUcwZFFmV0F4WDd0b3ZHc3loTHBSazNQUmJlK2g5VE40RUk5U0F5RFo1S05hWFRDL1RaaGJWelZleE9ET3YweHMvcW5FaXJWaVVxUGlQVFB3UVFKN0hnRmNFcGxaS3lRd1pwa2RYOW9CNE5TcEtzYlcxdnYrMHY4Zmg3emNTS0lKcXFUZHE3ZG1mS2Z6Rk4yRXJTa0k3ZWR1Y0FBdGFuaUhBdlJSWm1iMDBCUkpSM1B3QUhRMHdKckpvNnBmSjhWTG9Mc295OVZaRDRaWnBmN1lzc2xNS3RWNEdsUDRtWitjSzZQTEdiNFdmY0Q4RlVOVG56andYaHhGMGlqM1hXajRNNktUSnloaUV3UFErNVFEMjQ5V2JCZTdFbm5COVZWSWwxZzJJQllpdkgiLCJtYWMiOiJmY2E2NWQwZTI5Mzg1OWIyYzY4ZGYwYmUyOWYxMjRhMzVmMmNiMWIzNDc1N2Y1YjkyODFiMDM2MmM5NmNhNDI5IiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 01:58:28 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6IklhVEZDY0VYKy95WWZaclI4WmFiK2c9PSIsInZhbHVlIjoiUlJISHJnZnVVNUppQ2dxUE9JN0hJNzNGdWVZbGc4U1lQZmEyMXBjR0hIWHcza1ZreVJOQ0h0dWJYeHorbDltek5hRXRpcW04VnZVM3ZSZE1qUU1yY3BucGJaSTZ4TS9sN0pGcU00cVAwVlhKOHEwa3lHRXAyUGtnTldPN1FtTFcxdUdNYlRNbjNXeTB4WnM2UG8zM3hrWTg5SUozNUhtQjJQZHUyY3Vzd1hjYkttSUdsNXNvZGxlUGFYUWJTTGw3dkNBbkVITjEySTJVSElPc0x6ODVZYlhBOUJVekI5Y09nMDZ4N3NSS3RWRmI2VFBJOWtIWEdabEdWUWI4QVMzcmZmUUNMYXE0azRDWnREbEpHZENML2htTmVab1FQUXN2UEJiMXJFdVJkVWd5ZlA4dU5rNEkrZEFPaTFGTmJjTGJSOXgrMUdtV0hFdDdadnBBa1JpWFdZSGVEdzRFdjFjcUFQWm00eUs0dU1XWUtTZ2dBV1F1ODI2M1hxQkZZWkhaOG1NWnhRd0RYYklMS0xzbFB5NytUcHl4VkhnVTNvMmMxNk45dVU3MmdsMXFDZUorYjI2YXV2azBDcFF6S0xKbUU1R0lrRzN2LzhzaHZ0aWMyRStjQjYxdDR4bUtFS1hZbGhYTWNuSTQ4eHEzTzFWT1dlTCtFdk1lREMrclkzVW4iLCJtYWMiOiJlODVmNDFmZDVlNjQ5MDNlZjhhYmEzZGNhYjczZjlhNjljNTgxODUwZWExNjBkMDI5MTQ1YmU2MzIzMTM1MmIzIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 01:58:28 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6ImVPclFFc3Q5QkxsbUo0bys0Y2lIT2c9PSIsInZhbHVlIjoibnJMK3pwWXk5WDFMQ3hHOXVPRDV0NHQ3M3NQdWFhRXAxTnRHbE1rY3NtWkJrL1Z4UjdrR1hXUkp5c2x3d2o2TjNnaTduT0trWFY2azY3SUl0N0hkSkJWRWpiek4xTGJMaHp0clczcFJhdWpCWjhqZXcvalBiWksvS2hnZkQvR29QRm5JVFR6RU8xaDZiRnJ1V1lxUWdPRnFCNXRYOFdNM2tpdlAzeTFaT3IrOXdmMnZsV3NrMWZkbEpyQXhxUHV5MzJsRmh6bXY1NE93bHh0YTdaV3J4aUcwZFFmV0F4WDd0b3ZHc3loTHBSazNQUmJlK2g5VE40RUk5U0F5RFo1S05hWFRDL1RaaGJWelZleE9ET3YweHMvcW5FaXJWaVVxUGlQVFB3UVFKN0hnRmNFcGxaS3lRd1pwa2RYOW9CNE5TcEtzYlcxdnYrMHY4Zmg3emNTS0lKcXFUZHE3ZG1mS2Z6Rk4yRXJTa0k3ZWR1Y0FBdGFuaUhBdlJSWm1iMDBCUkpSM1B3QUhRMHdKckpvNnBmSjhWTG9Mc295OVZaRDRaWnBmN1lzc2xNS3RWNEdsUDRtWitjSzZQTEdiNFdmY0Q4RlVOVG56andYaHhGMGlqM1hXajRNNktUSnloaUV3UFErNVFEMjQ5V2JCZTdFbm5COVZWSWwxZzJJQllpdkgiLCJtYWMiOiJmY2E2NWQwZTI5Mzg1OWIyYzY4ZGYwYmUyOWYxMjRhMzVmMmNiMWIzNDc1N2Y1YjkyODFiMDM2MmM5NmNhNDI5IiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 01:58:28 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6IklhVEZDY0VYKy95WWZaclI4WmFiK2c9PSIsInZhbHVlIjoiUlJISHJnZnVVNUppQ2dxUE9JN0hJNzNGdWVZbGc4U1lQZmEyMXBjR0hIWHcza1ZreVJOQ0h0dWJYeHorbDltek5hRXRpcW04VnZVM3ZSZE1qUU1yY3BucGJaSTZ4TS9sN0pGcU00cVAwVlhKOHEwa3lHRXAyUGtnTldPN1FtTFcxdUdNYlRNbjNXeTB4WnM2UG8zM3hrWTg5SUozNUhtQjJQZHUyY3Vzd1hjYkttSUdsNXNvZGxlUGFYUWJTTGw3dkNBbkVITjEySTJVSElPc0x6ODVZYlhBOUJVekI5Y09nMDZ4N3NSS3RWRmI2VFBJOWtIWEdabEdWUWI4QVMzcmZmUUNMYXE0azRDWnREbEpHZENML2htTmVab1FQUXN2UEJiMXJFdVJkVWd5ZlA4dU5rNEkrZEFPaTFGTmJjTGJSOXgrMUdtV0hFdDdadnBBa1JpWFdZSGVEdzRFdjFjcUFQWm00eUs0dU1XWUtTZ2dBV1F1ODI2M1hxQkZZWkhaOG1NWnhRd0RYYklMS0xzbFB5NytUcHl4VkhnVTNvMmMxNk45dVU3MmdsMXFDZUorYjI2YXV2azBDcFF6S0xKbUU1R0lrRzN2LzhzaHZ0aWMyRStjQjYxdDR4bUtFS1hZbGhYTWNuSTQ4eHEzTzFWT1dlTCtFdk1lREMrclkzVW4iLCJtYWMiOiJlODVmNDFmZDVlNjQ5MDNlZjhhYmEzZGNhYjczZjlhNjljNTgxODUwZWExNjBkMDI5MTQ1YmU2MzIzMTM1MmIzIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 01:58:28 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1155122351\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-860953211 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">cN6pwMHluqMWM9hLaRtOtRasnQF8pfN3ghxjsOfa</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"36 characters\">http://localhost:8000/productservice</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-860953211\", {\"maxDepth\":0})</script>\n"}}