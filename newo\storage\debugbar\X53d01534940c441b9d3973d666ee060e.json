{"__meta": {"id": "X53d01534940c441b9d3973d666ee060e", "datetime": "2025-06-07 22:18:20", "utime": **********.184766, "method": "GET", "uri": "/account-dashboard", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.809027, "end": **********.184802, "duration": 1.****************, "duration_str": "1.38s", "measures": [{"label": "Booting", "start": **********.809027, "relative_start": 0, "end": **********.980009, "relative_end": **********.980009, "duration": 1.****************, "duration_str": "1.17s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.980032, "relative_start": 1.****************, "end": **********.184805, "relative_end": 2.86102294921875e-06, "duration": 0.**************, "duration_str": "205ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": ********, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET account-dashboard", "middleware": "web, verified, auth, XSS, revalidate", "controller": "App\\Http\\Controllers\\DashboardController@account_dashboard_index", "namespace": null, "prefix": "", "where": [], "as": "dashboard", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FDashboardController.php&line=81\" onclick=\"\">app/Http/Controllers/DashboardController.php:81-181</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.022250000000000002, "accumulated_duration_str": "22.25ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.078022, "duration": 0.01921, "duration_str": "19.21ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 86.337}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.125288, "duration": 0.00134, "duration_str": "1.34ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 86.337, "width_percent": 6.022}, {"sql": "select * from `migrations`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/rachidlaasri/laravel-installer/src/Helpers/MigrationsHelper.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\rachidlaasri\\laravel-installer\\src\\Helpers\\MigrationsHelper.php", "line": 29}, {"index": 14, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\XSS.php", "line": 40}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 16, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.16121, "duration": 0.0017, "duration_str": "1.7ms", "memory": 0, "memory_str": null, "filename": "MigrationsHelper.php:29", "source": "vendor/rachidlaasri/laravel-installer/src/Helpers/MigrationsHelper.php:29", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Frachidlaasri%2Flaravel-installer%2Fsrc%2FHelpers%2FMigrationsHelper.php&line=29", "ajax": false, "filename": "MigrationsHelper.php", "line": "29"}, "connection": "ty", "start_percent": 92.36, "width_percent": 7.64}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "cN6pwMHluqMWM9hLaRtOtRasnQF8pfN3ghxjsOfa", "_previous": "array:1 [\n  \"url\" => \"http://localhost/users\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "1", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/account-dashboard", "status_code": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:15</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/users</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1993 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=a091af98-270d-406e-ab7e-33a6e610d6e2414686; _clck=nups8l%7C2%7Cfwk%7C0%7C1960; _clsk=ij6lzq%7C1749334697734%7C4%7C1%7Ck.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IjdsbENYTWNDeDdhM0pCRzFkdExPbWc9PSIsInZhbHVlIjoiOGlLcDgzWE5yeUo1eEtqRnpSZmFFdnVFV0VIQnV2OVovNjhEd0s1b2lRdVY4Q09Ea2IzV2tZaXh6b24vV1hlQ25ZS1Yxc0pPbWlXQi9mVzR6YUdSSXJyMjZ6UFJvL0pFYTZFSldFNVJERE0zTzhRQW1WSTN0MllSdU40ODVJek80SEZRNHMxcDNSc3QzMmJXMnJML3lkeG91clhIeCt6aThBZE5rUFg1TlpHNzljSmgvMTdvTWhWMEpPd21RNWRUNWFiak5qaWdRSkpzbmFkSHQ4VjZ5SitrcnU1VVZXZUVFUDY5M1ZCTStuWDRBTzVsZ2Q3NFZ3eVdKeVlxNEZMSmgwakxPTzNpS3d3ZWhmZ3VEK2VNbmVvWTgxNFZGaHNDOVo3d0p0c0NzMWhUYXBibWtiWVJsTkhJTzVZYnlDMEFuVlFQejBWQ2JqREdVcWExV3FtMy9vbDdmanR0V1ZUbnQzNDFialB4MFZ5ZnBRSU9PcHVkUHU3VlhCU1B4L25JMkh3djFwU0tIeFg3WmdLMFV1UzhqSXM1S3czVWxaNUNqMStKajJrdUpvZG5uSk5qd1F6SDJDcnhLazhoSCswR0g2RmVDdWZkUEpZL0MxZFloSHhVS29SdTByL0pFQmtUSkF6cWhLMWJDRm5WQ2ljUFRDODV1S2h4K3MzUGI2cmoiLCJtYWMiOiI3MjZhNzY4YTVlMTUyMzM0YjMwYWQ3OTA2N2MyOWQ4OTQ3MjFhMjg4ODFjODNmMTg4ZmY0OWVlMDFjZWJlZDdmIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6InRmWWlCc3VoMVBtZDdSeDlYNE1iUGc9PSIsInZhbHVlIjoibThJczdvdSs5aE9wblZXZ2R5c1Byb1h0aHJLUnZhV3RQd3FaVE1LM0xoYmxSNjZGa2lnWTllUWtSWGZwRXZiWjBtZmZmK1pZVThxY3czTEp1SUhaOGdCbzdIbHEweGh4bmlnS0syTzNuT0lsMHY0bkVxM0FRNmpORU4rUVdSa2h1T1JCcHB5c1JoSDdzd3FFK1Z3eFFXWjVvVlJob0lVamVtYlpjdUZUdTllOUd2UkRwN3pSVUZPMUg5TGZadkZHTU9DSkswU1R1dWdHdEJlbnhaNkZTNHlUajlBaDA5YzVrREdWZ3k0VU95YVJNeWs3Zi9MdENnalFSZ01XZEV3ZjJCUFBHZ2xyZ0JSelVDeE41L1VOeUVCM0NQUHExZXlUK2E2bWtINFJ4WnREU25RVnVjUG1mRnR6eDFvMUlsckNweDNISEF4QUZMUmtuNjVobVFEVmpMWHJBL0R2R3ZHOWhwTkh3ZW93ZlVYcmMveEpUUlZOQkFVNmZ1Ky9yeUdUUEVWUXYwT3AveklvdGZ4WFgrdkthZVhFZFZ2VHdObzBPQXdYYzF5TFRZWEtEakRLcU91WEhheThVRGtoKzYydUN1blQzaFBndEJxZ3JlNUt0UmVNb1hNRUQyTFUxaW9VR3JOUWw3L3VSVURpQWlzbG5vSGhPYm11R2NEZW5yWG0iLCJtYWMiOiJmZGRkNzgyZGJhNTY5NDhlNzI5NTkzOGRlOGQ5YzBhMDZhZWU0NTYwNjRmOTU5YTY0NmVhNWE4ZDc1NzRhMWJlIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-896442721 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">cN6pwMHluqMWM9hLaRtOtRasnQF8pfN3ghxjsOfa</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">PmFRgUqysTWH0qB2ROlhITKoLIETZGW2KykVXtOx</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-896442721\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-222865184 data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 07 Jun 2025 22:18:20 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"26 characters\">http://localhost/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-methods</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">POST, GET, OPTIONS, PUT, DELETE</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-headers</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"66 characters\">Content-Type, Accept, Authorization, X-Requested-With, Application</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6InovcmdBOURtVmtra1E3SytuekJ5Umc9PSIsInZhbHVlIjoieUlkNFBZbzgxM1NISTRGT2pjWGJxc09VMXJBVjhXOVhtNUpXSFBVWS9oNk92eDFiT3Y2SW1iUndpNjhKVUtHM1RjMHNPblZac0J3V2dhdVJsWTlvTExCNHZBdmNzcTJIdjgvbm1ia3BGMnBGWEpxeThTNk1ETzFqMk9lMlZxOURPcGdJRWFpNHpNdXNzTCtmazU5M0tMWWZUclIzcTdScDFzZDh1MzNsd2VIZTBjck1KVTJEYVdkYTNWWlI2b1NrNGhHSTE2akFxOHZXUWh4bnBNdEVnNy90QjhodE9yd2xIaElzWE1HU01rUlkxUkFmN1NUVi81Rjc4NEovZlJPTU14QzNjR0o2bXJ4TmZvTlljNndyVC9Vdy9haXBNSFM4L0laaEZCQVBvVU4rOTViSnVkTnhWZ1pDUnR2YXp0anoxUXlQNkhhYWFVRmw2dTVnMU0rZnRscUpuVEUvSks0V2ptY1dQUVN4ZGhaYURTK01wYmQ5QjNhN1BlMit2dm1oUVdwOVVCOFFuS3o1WS9oZUpTNkxpQXFEMmxHcGVCY28xVFFiSHpPcVdGdmhYZDRXUzJLR01qa2hwa3FCSzFnR0FWN3VUdTNMN2QvWHdJUGwzeURON3JyTUlGbkhvZHlRd1Z5M2x1cFlrTys4bEhDckxGQ2lKNjd3MHppM3lBYVEiLCJtYWMiOiJhZTEzYzk2NGEzZDI0M2M2NmYzMGU3YzhlMGIyYTU4ZTI3NDEzZjNmZThkZTgyNmNlY2MwYjYzMzg0Mzk1NzE4IiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 00:18:20 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6IlVJb0YvMDlLSnZadXdWVEJFazBhMXc9PSIsInZhbHVlIjoidk5leTNnT2xBd0tUdjlBRHdwL1loTzVIUjlhdlllaEpLRUx2K2xZU2tQSTBWZSsrYm14VGFqYURsek16NmxneGp2ckRDOTYxVjNmbDlRTzAyd09MdHdFTk4vbktzVUwvZ1V4b2lUMWJYalJKeGpMTU9UeEE1a2wvK3FZQVdvM0hvN3dlMTZDbTJMOGJnUThxWlVqU1B0d25MR2swUjQ0YXhkMWdPdjFRVjhLdnoybGpmK2xlamlkMC9Oakl6N1A0YTh5cWZZUVBVRjVMdzFpQlpaS1NUS212V2w0ZDBFdWJOOVJKbUx5MnF6RDdNWjdUaEdCbThoc3ViRjR5M01CUHhkR29QOE1sSXNWbzFpd010Y2dZM3JTaHRwWWFyQStwTngwVCs1V01HMWxZSVVuak12UGNnRm5PUnNucUVqL3hTQ1pTbEJhMCt6Z3F6OVlhUjFWeEZXbUU5aFBkTmtzU0I4M1lxcllHWE1YVlM2SklUMmVMRVRyTWxnUTJlMCtYWFF6YTVsNVpUdFBRTDhNWXFEQlpWdGRKMCtNRmxRSFZyZGlhRnE5blpWWitPMmtJMjNhdWhMTHE1dkcvcmo3VnBnT2JzeXFhRXF6V09EL2JBOXA0SkovMnFSSUhDK2t1Ukg0U3lac245TlFJSWdrMFNNdTZqNzNsWGZmN1daYlIiLCJtYWMiOiI5NzU2NGZiMGZjMmI5YzgxMjBmMjg0YjMwMTU2ZTUyMTkyNGRhNDdiZjc4ZmZmOTlhMjgzMTY4ZTlmZWM1ZDk3IiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 00:18:20 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6InovcmdBOURtVmtra1E3SytuekJ5Umc9PSIsInZhbHVlIjoieUlkNFBZbzgxM1NISTRGT2pjWGJxc09VMXJBVjhXOVhtNUpXSFBVWS9oNk92eDFiT3Y2SW1iUndpNjhKVUtHM1RjMHNPblZac0J3V2dhdVJsWTlvTExCNHZBdmNzcTJIdjgvbm1ia3BGMnBGWEpxeThTNk1ETzFqMk9lMlZxOURPcGdJRWFpNHpNdXNzTCtmazU5M0tMWWZUclIzcTdScDFzZDh1MzNsd2VIZTBjck1KVTJEYVdkYTNWWlI2b1NrNGhHSTE2akFxOHZXUWh4bnBNdEVnNy90QjhodE9yd2xIaElzWE1HU01rUlkxUkFmN1NUVi81Rjc4NEovZlJPTU14QzNjR0o2bXJ4TmZvTlljNndyVC9Vdy9haXBNSFM4L0laaEZCQVBvVU4rOTViSnVkTnhWZ1pDUnR2YXp0anoxUXlQNkhhYWFVRmw2dTVnMU0rZnRscUpuVEUvSks0V2ptY1dQUVN4ZGhaYURTK01wYmQ5QjNhN1BlMit2dm1oUVdwOVVCOFFuS3o1WS9oZUpTNkxpQXFEMmxHcGVCY28xVFFiSHpPcVdGdmhYZDRXUzJLR01qa2hwa3FCSzFnR0FWN3VUdTNMN2QvWHdJUGwzeURON3JyTUlGbkhvZHlRd1Z5M2x1cFlrTys4bEhDckxGQ2lKNjd3MHppM3lBYVEiLCJtYWMiOiJhZTEzYzk2NGEzZDI0M2M2NmYzMGU3YzhlMGIyYTU4ZTI3NDEzZjNmZThkZTgyNmNlY2MwYjYzMzg0Mzk1NzE4IiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 00:18:20 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6IlVJb0YvMDlLSnZadXdWVEJFazBhMXc9PSIsInZhbHVlIjoidk5leTNnT2xBd0tUdjlBRHdwL1loTzVIUjlhdlllaEpLRUx2K2xZU2tQSTBWZSsrYm14VGFqYURsek16NmxneGp2ckRDOTYxVjNmbDlRTzAyd09MdHdFTk4vbktzVUwvZ1V4b2lUMWJYalJKeGpMTU9UeEE1a2wvK3FZQVdvM0hvN3dlMTZDbTJMOGJnUThxWlVqU1B0d25MR2swUjQ0YXhkMWdPdjFRVjhLdnoybGpmK2xlamlkMC9Oakl6N1A0YTh5cWZZUVBVRjVMdzFpQlpaS1NUS212V2w0ZDBFdWJOOVJKbUx5MnF6RDdNWjdUaEdCbThoc3ViRjR5M01CUHhkR29QOE1sSXNWbzFpd010Y2dZM3JTaHRwWWFyQStwTngwVCs1V01HMWxZSVVuak12UGNnRm5PUnNucUVqL3hTQ1pTbEJhMCt6Z3F6OVlhUjFWeEZXbUU5aFBkTmtzU0I4M1lxcllHWE1YVlM2SklUMmVMRVRyTWxnUTJlMCtYWFF6YTVsNVpUdFBRTDhNWXFEQlpWdGRKMCtNRmxRSFZyZGlhRnE5blpWWitPMmtJMjNhdWhMTHE1dkcvcmo3VnBnT2JzeXFhRXF6V09EL2JBOXA0SkovMnFSSUhDK2t1Ukg0U3lac245TlFJSWdrMFNNdTZqNzNsWGZmN1daYlIiLCJtYWMiOiI5NzU2NGZiMGZjMmI5YzgxMjBmMjg0YjMwMTU2ZTUyMTkyNGRhNDdiZjc4ZmZmOTlhMjgzMTY4ZTlmZWM1ZDk3IiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 00:18:20 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-222865184\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1192111611 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">cN6pwMHluqMWM9hLaRtOtRasnQF8pfN3ghxjsOfa</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/users</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1192111611\", {\"maxDepth\":0})</script>\n"}}