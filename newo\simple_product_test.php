<?php
// اختبار بسيط لإنشاء منتج
// تشغيل من المتصفح: /simple_product_test.php

require_once __DIR__ . '/vendor/autoload.php';

$app = require_once __DIR__ . '/bootstrap/app.php';
$app->make(Illuminate\Contracts\Console\Kernel::class)->bootstrap();

use App\Models\User;
use App\Models\ProductService;
use App\Models\ProductServiceCategory;
use App\Models\ProductServiceUnit;
use App\Models\ChartOfAccount;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;

echo "<h1>اختبار إنشاء منتج بسيط</h1>";

try {
    // البحث عن مستخدم
    $user = User::where('type', 'company')->first();
    if (!$user) {
        echo "<p style='color: red;'>❌ لا يوجد مستخدم للاختبار</p>";
        exit;
    }
    
    echo "<p style='color: green;'>✅ المستخدم: {$user->name} (ID: {$user->id})</p>";
    
    // تسجيل دخول المستخدم
    Auth::login($user);
    echo "<p style='color: green;'>✅ تم تسجيل الدخول</p>";
    
    // التحقق من الاتصال بقاعدة البيانات
    $connection = DB::connection()->getPdo();
    echo "<p style='color: green;'>✅ الاتصال بقاعدة البيانات ناجح</p>";
    
    // التحقق من الجداول
    $tables = ['product_services', 'product_service_categories', 'product_service_units', 'chart_of_accounts'];
    foreach ($tables as $table) {
        $count = DB::table($table)->count();
        echo "<p>📊 جدول {$table}: {$count} سجل</p>";
    }
    
    // التحقق من البيانات المطلوبة
    echo "<h2>التحقق من البيانات المطلوبة:</h2>";
    
    // الفئات
    $categories = DB::table('product_service_categories')
        ->where('type', 'product & service')
        ->where('created_by', $user->creatorId())
        ->get();
    
    echo "<p>الفئات: " . count($categories) . "</p>";
    if (count($categories) == 0) {
        echo "<p style='color: orange;'>⚠️ إنشاء فئة تجريبية...</p>";
        
        DB::table('product_service_categories')->insert([
            'name' => 'فئة تجريبية',
            'type' => 'product & service',
            'color' => '#007bff',
            'created_by' => $user->creatorId(),
            'created_at' => now(),
            'updated_at' => now()
        ]);
        
        echo "<p style='color: green;'>✅ تم إنشاء فئة تجريبية</p>";
        $categories = DB::table('product_service_categories')
            ->where('type', 'product & service')
            ->where('created_by', $user->creatorId())
            ->get();
    }
    
    // الوحدات
    $units = DB::table('product_service_units')
        ->where('created_by', $user->creatorId())
        ->get();
    
    echo "<p>الوحدات: " . count($units) . "</p>";
    if (count($units) == 0) {
        echo "<p style='color: orange;'>⚠️ إنشاء وحدة تجريبية...</p>";
        
        DB::table('product_service_units')->insert([
            'name' => 'قطعة',
            'created_by' => $user->creatorId(),
            'created_at' => now(),
            'updated_at' => now()
        ]);
        
        echo "<p style='color: green;'>✅ تم إنشاء وحدة تجريبية</p>";
        $units = DB::table('product_service_units')
            ->where('created_by', $user->creatorId())
            ->get();
    }
    
    // حسابات الإيرادات
    $incomeAccounts = DB::table('chart_of_accounts')
        ->leftJoin('chart_of_account_types', 'chart_of_account_types.id', '=', 'chart_of_accounts.type')
        ->where('chart_of_account_types.name', 'income')
        ->where('chart_of_accounts.created_by', $user->creatorId())
        ->select('chart_of_accounts.*')
        ->get();
    
    echo "<p>حسابات الإيرادات: " . count($incomeAccounts) . "</p>";
    
    // حسابات المصروفات
    $expenseAccounts = DB::table('chart_of_accounts')
        ->leftJoin('chart_of_account_types', 'chart_of_account_types.id', '=', 'chart_of_accounts.type')
        ->whereIn('chart_of_account_types.name', ['Expenses', 'Costs of Goods Sold'])
        ->where('chart_of_accounts.created_by', $user->creatorId())
        ->select('chart_of_accounts.*')
        ->get();
    
    echo "<p>حسابات المصروفات: " . count($expenseAccounts) . "</p>";
    
    if (count($incomeAccounts) == 0 || count($expenseAccounts) == 0) {
        echo "<p style='color: red;'>❌ لا توجد حسابات كافية. يرجى إنشاء حسابات الإيرادات والمصروفات أولاً.</p>";
        echo "<p>يمكنك إنشاء الحسابات من: <a href='/chart-of-account/create'>/chart-of-account/create</a></p>";
        exit;
    }
    
    // إنشاء منتج تجريبي
    echo "<h2>إنشاء منتج تجريبي:</h2>";
    
    $category = $categories->first();
    $unit = $units->first();
    $incomeAccount = $incomeAccounts->first();
    $expenseAccount = $expenseAccounts->first();
    
    $testSku = 'TEST-' . time();
    
    echo "<p>الفئة: {$category->name} (ID: {$category->id})</p>";
    echo "<p>الوحدة: {$unit->name} (ID: {$unit->id})</p>";
    echo "<p>حساب الإيرادات: {$incomeAccount->name} (ID: {$incomeAccount->id})</p>";
    echo "<p>حساب المصروفات: {$expenseAccount->name} (ID: {$expenseAccount->id})</p>";
    echo "<p>SKU: {$testSku}</p>";
    
    // إنشاء المنتج باستخدام Query Builder
    $productId = DB::table('product_services')->insertGetId([
        'name' => 'منتج تجريبي - ' . date('Y-m-d H:i:s'),
        'sku' => $testSku,
        'sale_price' => 100.00,
        'purchase_price' => 80.00,
        'quantity' => 10,
        'type' => 'product',
        'category_id' => $category->id,
        'unit_id' => $unit->id,
        'sale_chartaccount_id' => $incomeAccount->id,
        'expense_chartaccount_id' => $expenseAccount->id,
        'created_by' => $user->creatorId(),
        'tax_id' => '',
        'description' => 'منتج تجريبي لاختبار النظام',
        'created_at' => now(),
        'updated_at' => now()
    ]);
    
    if ($productId) {
        echo "<p style='color: green;'>✅ تم إنشاء منتج تجريبي بنجاح! (ID: {$productId})</p>";
        
        // التحقق من حفظ المنتج
        $savedProduct = DB::table('product_services')->where('id', $productId)->first();
        if ($savedProduct) {
            echo "<p style='color: green;'>✅ تم التحقق من حفظ المنتج في قاعدة البيانات</p>";
            echo "<p>اسم المنتج: {$savedProduct->name}</p>";
            echo "<p>SKU: {$savedProduct->sku}</p>";
            echo "<p>سعر البيع: {$savedProduct->sale_price}</p>";
            echo "<p>سعر الشراء: {$savedProduct->purchase_price}</p>";
        } else {
            echo "<p style='color: red;'>❌ لم يتم العثور على المنتج في قاعدة البيانات</p>";
        }
    } else {
        echo "<p style='color: red;'>❌ فشل في إنشاء المنتج</p>";
    }
    
    // عرض آخر 5 منتجات
    echo "<h2>آخر 5 منتجات:</h2>";
    $recentProducts = DB::table('product_services')
        ->where('created_by', $user->creatorId())
        ->orderBy('created_at', 'desc')
        ->limit(5)
        ->get();
    
    if (count($recentProducts) > 0) {
        echo "<ul>";
        foreach ($recentProducts as $product) {
            echo "<li>{$product->name} (SKU: {$product->sku}) - {$product->created_at}</li>";
        }
        echo "</ul>";
    } else {
        echo "<p>لا توجد منتجات</p>";
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ خطأ: " . $e->getMessage() . "</p>";
    echo "<pre>" . $e->getTraceAsString() . "</pre>";
}

echo "<hr>";
echo "<p><a href='/productservice'>العودة إلى صفحة المنتجات</a></p>";
echo "<p><a href='/debug-product-creation'>صفحة التشخيص المتقدمة</a></p>";
?>
