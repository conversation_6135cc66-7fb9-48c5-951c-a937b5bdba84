{"__meta": {"id": "Xe43a1d27fcb9994fdc94819be4633c59", "datetime": "2025-06-07 22:18:07", "utime": **********.918052, "method": "POST", "uri": "/chats/favorites", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749334686.334405, "end": **********.918097, "duration": 1.5836920738220215, "duration_str": "1.58s", "measures": [{"label": "Booting", "start": 1749334686.334405, "relative_start": 0, "end": **********.687578, "relative_end": **********.687578, "duration": 1.353173017501831, "duration_str": "1.35s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.687608, "relative_start": 1.3532030582427979, "end": **********.918102, "relative_end": 5.0067901611328125e-06, "duration": 0.23049402236938477, "duration_str": "230ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45085624, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/favorites", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getFavorites", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "favorites", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=454\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:454-477</a>"}, "queries": {"nb_statements": 4, "nb_failed_statements": 0, "accumulated_duration": 0.021990000000000003, "accumulated_duration_str": "21.99ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.7863162, "duration": 0.01696, "duration_str": "16.96ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 77.126}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.832551, "duration": 0.00134, "duration_str": "1.34ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 77.126, "width_percent": 6.094}, {"sql": "select * from `migrations`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/rachidlaasri/laravel-installer/src/Helpers/MigrationsHelper.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\rachidlaasri\\laravel-installer\\src\\Helpers\\MigrationsHelper.php", "line": 29}, {"index": 14, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\XSS.php", "line": 40}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 16, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.865652, "duration": 0.00202, "duration_str": "2.02ms", "memory": 0, "memory_str": null, "filename": "MigrationsHelper.php:29", "source": "vendor/rachidlaasri/laravel-installer/src/Helpers/MigrationsHelper.php:29", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Frachidlaasri%2Flaravel-installer%2Fsrc%2FHelpers%2FMigrationsHelper.php&line=29", "ajax": false, "filename": "MigrationsHelper.php", "line": "29"}, "connection": "ty", "start_percent": 83.22, "width_percent": 9.186}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (1) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.888103, "duration": 0.0016699999999999998, "duration_str": "1.67ms", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 92.406, "width_percent": 7.594}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "cN6pwMHluqMWM9hLaRtOtRasnQF8pfN3ghxjsOfa", "_previous": "array:1 [\n  \"url\" => \"http://localhost/dashboard\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "1", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/favorites", "status_code": "<pre class=sf-dump id=sf-dump-539910164 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-539910164\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-2146117749 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-2146117749\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-873440813 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">cN6pwMHluqMWM9hLaRtOtRasnQF8pfN3ghxjsOfa</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-873440813\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-773168679 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"26 characters\">http://localhost/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1993 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=a091af98-270d-406e-ab7e-33a6e610d6e2414686; _clck=nups8l%7C2%7Cfwk%7C0%7C1960; _clsk=ij6lzq%7C1749334667398%7C2%7C1%7Ck.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6Im8rL2ppQXlSQ1hKK2ZoUzFUUGNuR0E9PSIsInZhbHVlIjoiYXlLazNDVTc0NUt3VHdmR2dnQXdmWjJYbEh3ZEZxd3QvNGRUd0UzWndheWcwRHQ0UUNjS25Sb2RRamttVWNmTXdrb0svdDVyODN4dDBaK1l1K0RYT1F1NGx1eTh4Vk5xQjhWUE8yS1dVcjVGekZxSHQ3TUpobEMyWjJDS3d4UUVKcUhMYndjVENHankyNjlqRWJOZkVFc3V4eWRGZXhKc2ZtOTFxbElPQjhONy9hQWx2VHB0TERvYXpqVWp3OGQ2Vmx1eWthL1dNUE9TWG1EdE9MWTV5ak55dDRublh0OVc4WC9sdEg0ZFV1R3dGL04yWDVYeHpRb0lnNXlwT21meUdJcXNHT1diNHhVeXoxdUZrYXFuUTlSVG1MZFhrQ3ZFTlBYWGZSNWFFZndhclVCOHd5djZkckNIQ1c3OUlwZUI3NmxRNzlRd0VMNGQxa0VPSkJUVWhCanBDSzRBTUprUi8veWlnTnNUWmFMbmE0ODdrL0pFTGRZb3l4SzlLY09LOHZseEtpcHoxRFFvSElaRXNCQ2RFWGxSbklRTDVidGQzaDYyamR3aFpjZ0Q5dWVCVFhaSCtOSkJITlJ0UGYxUDhmR0RBUEtndWlaY1VndXQ0MnBsWEw3OVBQcmxOdm5GdzVVRUV6TS9WSXhsR3dWQ2c4U3FscGlyQU9YYlp0SmIiLCJtYWMiOiIzMThmZmRiMTZhZTNmODA1NTQ3OTg1OTJlNjdhMDgwMTgwMmJkNTJiZGQzZjk1YzJlMjEwMmYyMjE4MGM2NDY3IiwidGFnIjoiIn0%3D; test_session=eyJpdiI6IlBKclpxNllYeU9rK2QxeksvUnMvdmc9PSIsInZhbHVlIjoidWpUaDF1U2V2WEpIeFNUZUYydkU4R1lNM2QxdUxVQ1NzMFN4Ry9wVlBpQmNyUzdvUW9yTEFxWkxrWGZWRzV1aGlSSCsvT1lSRWlLNEY2SjZUWm1WbW5VdXFNOG5CZll0dlBXUTVaWGthdzNpN05ySmpCVXVLenp2OXVLelcyZFl4SUgvYllZcTA3RHpoWU9iRExEUHFrczZGYU1BWklzYmoxeXFTL2k1VnVvdFNVRDRDYytraHpycFRHSmJYbDhZSFluV2svNHRDS2ZDc1o2L1N2Rk9uMDFJMlFkUWQ1ejl0dGpCVUZRcVhBazVQUTYwNVhiWTF3dnpsZk14QURUN0pRWkVrMkJhZyswbi9RWmlobGphazFXUm9XUWZZOGZaOG9OT1crMm9QRnYxQ3daUDVlSHkxSnpWK25oSmZtVklVVThRWnNDbGVGdU9MeWFXMlExMzREZklMYmQ3Rm00WGZFdUpYR2RhMjNMWXhHRlZZVjMwREVoNzh6TTBJbjQ0SnVLRXljbFVoa1dENDBCU01YTFZVT1dIK0tSK0pMWittZjhSLzdJdSt6NTBHWU1PSU00MzlvQmJGd0RKazZOU2ptbEJVSWlFQ3ZYQnVGeHo3R0lvS2lKVkxKZXZ0QjhBVFkwTjUyNm5hVHNRYnoyZkoxbmZmbzhKQS83cVpXQ2ciLCJtYWMiOiI4OTJmM2EwN2MwOGYwZjljZWVmNGEzOTQzZjc1ZWMzYjlmZmRkNmJhZWY0OWJkNGE1NTdiYzlhNzUxMjA1OTc0IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-773168679\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1558721540 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">cN6pwMHluqMWM9hLaRtOtRasnQF8pfN3ghxjsOfa</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">PmFRgUqysTWH0qB2ROlhITKoLIETZGW2KykVXtOx</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1558721540\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-355474377 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 07 Jun 2025 22:18:07 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6ImUyTUltMEFGMjQvNDdYNEtvZ3g5OGc9PSIsInZhbHVlIjoibWZTMGxZVWdFZUZ1cGRtRk1kREJsb0k0cTcvZXJndWlNSW40dXZUc3ZFY1VhaE5wakgxWGtOWnJmdE5NMUI0MUhNT2hXRWEyVjdIZHRmU3RtMGRiQnVNWW5sYVpKbWlNcG5wQzBwOGtPWUxYRVRQK0t3Rjk0S1lvQVRJc1ZaOWxXeUNwem9pb0txN0FrZ09WTmZ1SGFnTEhIMjJTNDIrVDJwaThnZzlaZnlnbVVuZGd3anU4TUlOL3RabXBRU3NHRUpmNzVuQnh5VkNITEh5Y3RrQUJERisvTXBoUGtCNzZESVo0cW83S0pFelpUdmxYbS9JdmREOXlQN2xZS0RXVHMzcUs2OEtDRHhlN2FnYWVxcmZkVDhEZlRVaFowK1NzWllDTVJQdkFSVlltK2RVQkY5Vyt5SkdpUlBiallqRlFRQklkd0NkeFFjOTVDNnhMM0p6aXB1MElWdjV1YmZwTVpwSFM0WStyOGVpYVY3M0pYcXF5VjRPSGZJcjViUVpxQzB2M0RmNWNxVGYwQlN0TDc4ZFM3V3pQRDlzV1o1alhtdjZJcXE3V3hoY0l1S3dLM29hUWQyaE5kdUJmNElSaW9hbzFINzNEcFg2TDdyazJnUnlRdUhVenFoSE5BNENWV09VNVI3Ris1S2tmTERVZG5Sb1NsZDlyNHBSdmoyckwiLCJtYWMiOiIyYTU1YzhmZTBmNTg4NGY3NmUxM2YxOGJiMzhlZmQzOGEzZjA1ZGNmZTBhYWYxNjJiOTY4OTNlMDVhOWY5NTZkIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 00:18:07 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6IlByK3ZpNG5SL09hR3VJbHNwK1Bob2c9PSIsInZhbHVlIjoiZkxaS21SdTZXQko3ajdtc2VwcnFKaGVWcHZjaCtxL0JjMHZrSVpHQVAzTTF4Z3VxOE5NYno2ZkV1Y2Vyb2tmeHRsdlRwWHNId1F0bUM4RjNTSTFwQ0UxVjdySVI1b0dqUUlkeEg2OFFsK1M0aDlmUGhTQXBKUEV4ZXByRlZ1cERqMVBtaU1nNnZMMExrRFduSG0rK0hDVHB6YnMxTmRQZi9hMEZvN2VPbjJWeEIvbFV4a2hudU1FaWlRb0hMM2JNcXFDTWorVFlURFVxbDEzVmV6dkVKWU0zU0ZOcWpKcUhnZTNXS2d4bitUL0lybW1xUit6R2tYc0hGUEE3eWRiVSttMVhSbEJwMktidnVSVnJETE16VDRNYWRYU0MwY1l6TWdrZm0rTzk1V01wcTlIOFJiQlpPNUM5cllvcVVNdjZQZ25LNHZub29PZ3g0T1pTbWNYYUlpQWN5TmZyQk9wTW1qY3g2c2JGVFFtTVA3Y3lxRVA4SXlWbVVUNU1oU1lKQ2xhZ0JiaHo1RWFReTljVVpKVUxnN2psTGgyZVQrc3JmSm5ieTFIcmh4ZkgwbTdzdWdydGZzNlBYWkZzNG1MYjdIdVY1T3JZQWNKOXVhVlUxN0ljMWJCaFNMSGllUnRaeHpZT2ZWS3Q0R2xWcURHUjhnKzBVSFNCWWRqd0U4aWkiLCJtYWMiOiI0NzFiYmMzODcxMTY3NDE1ZmRiOTk5ZDMyZTUwZWFhZjQzYTAwNGQ1Mzg4Y2RkMjYyMjZlZDE4M2YxZDI0MzUwIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 00:18:07 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6ImUyTUltMEFGMjQvNDdYNEtvZ3g5OGc9PSIsInZhbHVlIjoibWZTMGxZVWdFZUZ1cGRtRk1kREJsb0k0cTcvZXJndWlNSW40dXZUc3ZFY1VhaE5wakgxWGtOWnJmdE5NMUI0MUhNT2hXRWEyVjdIZHRmU3RtMGRiQnVNWW5sYVpKbWlNcG5wQzBwOGtPWUxYRVRQK0t3Rjk0S1lvQVRJc1ZaOWxXeUNwem9pb0txN0FrZ09WTmZ1SGFnTEhIMjJTNDIrVDJwaThnZzlaZnlnbVVuZGd3anU4TUlOL3RabXBRU3NHRUpmNzVuQnh5VkNITEh5Y3RrQUJERisvTXBoUGtCNzZESVo0cW83S0pFelpUdmxYbS9JdmREOXlQN2xZS0RXVHMzcUs2OEtDRHhlN2FnYWVxcmZkVDhEZlRVaFowK1NzWllDTVJQdkFSVlltK2RVQkY5Vyt5SkdpUlBiallqRlFRQklkd0NkeFFjOTVDNnhMM0p6aXB1MElWdjV1YmZwTVpwSFM0WStyOGVpYVY3M0pYcXF5VjRPSGZJcjViUVpxQzB2M0RmNWNxVGYwQlN0TDc4ZFM3V3pQRDlzV1o1alhtdjZJcXE3V3hoY0l1S3dLM29hUWQyaE5kdUJmNElSaW9hbzFINzNEcFg2TDdyazJnUnlRdUhVenFoSE5BNENWV09VNVI3Ris1S2tmTERVZG5Sb1NsZDlyNHBSdmoyckwiLCJtYWMiOiIyYTU1YzhmZTBmNTg4NGY3NmUxM2YxOGJiMzhlZmQzOGEzZjA1ZGNmZTBhYWYxNjJiOTY4OTNlMDVhOWY5NTZkIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 00:18:07 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6IlByK3ZpNG5SL09hR3VJbHNwK1Bob2c9PSIsInZhbHVlIjoiZkxaS21SdTZXQko3ajdtc2VwcnFKaGVWcHZjaCtxL0JjMHZrSVpHQVAzTTF4Z3VxOE5NYno2ZkV1Y2Vyb2tmeHRsdlRwWHNId1F0bUM4RjNTSTFwQ0UxVjdySVI1b0dqUUlkeEg2OFFsK1M0aDlmUGhTQXBKUEV4ZXByRlZ1cERqMVBtaU1nNnZMMExrRFduSG0rK0hDVHB6YnMxTmRQZi9hMEZvN2VPbjJWeEIvbFV4a2hudU1FaWlRb0hMM2JNcXFDTWorVFlURFVxbDEzVmV6dkVKWU0zU0ZOcWpKcUhnZTNXS2d4bitUL0lybW1xUit6R2tYc0hGUEE3eWRiVSttMVhSbEJwMktidnVSVnJETE16VDRNYWRYU0MwY1l6TWdrZm0rTzk1V01wcTlIOFJiQlpPNUM5cllvcVVNdjZQZ25LNHZub29PZ3g0T1pTbWNYYUlpQWN5TmZyQk9wTW1qY3g2c2JGVFFtTVA3Y3lxRVA4SXlWbVVUNU1oU1lKQ2xhZ0JiaHo1RWFReTljVVpKVUxnN2psTGgyZVQrc3JmSm5ieTFIcmh4ZkgwbTdzdWdydGZzNlBYWkZzNG1MYjdIdVY1T3JZQWNKOXVhVlUxN0ljMWJCaFNMSGllUnRaeHpZT2ZWS3Q0R2xWcURHUjhnKzBVSFNCWWRqd0U4aWkiLCJtYWMiOiI0NzFiYmMzODcxMTY3NDE1ZmRiOTk5ZDMyZTUwZWFhZjQzYTAwNGQ1Mzg4Y2RkMjYyMjZlZDE4M2YxZDI0MzUwIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 00:18:07 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-355474377\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1189787597 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">cN6pwMHluqMWM9hLaRtOtRasnQF8pfN3ghxjsOfa</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"26 characters\">http://localhost/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1189787597\", {\"maxDepth\":0})</script>\n"}}