{"__meta": {"id": "X2c6166260c1fbb76f3369ed25cce8ca2", "datetime": "2025-06-07 22:19:45", "utime": **********.13671, "method": "GET", "uri": "/", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749334783.842795, "end": **********.136747, "duration": 1.2939519882202148, "duration_str": "1.29s", "measures": [{"label": "Booting", "start": 1749334783.842795, "relative_start": 0, "end": **********.014756, "relative_end": **********.014756, "duration": 1.1719610691070557, "duration_str": "1.17s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.014778, "relative_start": 1.171983003616333, "end": **********.13675, "relative_end": 3.0994415283203125e-06, "duration": 0.12197208404541016, "duration_str": "122ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 43388168, "peak_usage_str": "41MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET /", "middleware": "web, XSS, revalidate", "controller": "App\\Http\\Controllers\\DashboardController@landingpage", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FDashboardController.php&line=59\" onclick=\"\">app/Http/Controllers/DashboardController.php:59-74</a>"}, "queries": {"nb_statements": 1, "nb_failed_statements": 0, "accumulated_duration": 0.00697, "accumulated_duration_str": "6.97ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 46}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 78}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\DashboardController.php", "line": 66}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.105602, "duration": 0.00697, "duration_str": "6.97ms", "memory": 0, "memory_str": null, "filename": "Utility.php:46", "source": "app/Models/Utility.php:46", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUtility.php&line=46", "ajax": false, "filename": "Utility.php", "line": "46"}, "connection": "ty", "start_percent": 0, "width_percent": 100}]}, "models": {"data": [], "count": 0, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "DCK491V1GUyngHueDf4bnOw7DOzApt305JPGLVcA", "_previous": "array:1 [\n  \"url\" => \"http://localhost\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/", "status_code": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1203121043 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1203121043\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1828742553 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1828742553\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1866626983 data-indent-pad=\"  \"><span class=sf-dump-note>array:15</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">none</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"138 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=17ceeen%7C2%7Cfwg%7C0%7C1960</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1866626983\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1843396509 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1843396509\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1984004896 data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 07 Jun 2025 22:19:45 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/login</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-methods</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">POST, GET, OPTIONS, PUT, DELETE</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-headers</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"66 characters\">Content-Type, Accept, Authorization, X-Requested-With, Application</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6InlqRTlFUWNUaFp5ZnBSQ0I0OTF5UEE9PSIsInZhbHVlIjoiN0hTdEhyNTRXeXdHaE55NU5xTWM4TTh0bHlBaHEyeExXNDNVbkVueEtxb0hWTGdwRXd3L0NWdW5Va0pVNlVmaTREcjFuZVZTZEZ3c01EQ2tHbGNqNUdwTXVacWtCQ3IzcDZpZ3BSNlJ3TkUxZ2Z6MVYvSE5vUFFIY1lVcEJlcldpVWlHN015cWdpTjcvQXI3RHBvSSswMVcycFhWMmh1MlhlcmxXTzNRR0c3SGI4d3hQeGdnRmdiWnFiWWMxYUFtOW12N29rVEZmQ1BYd3d3akpnV2lMMnVmTStENlBpVDIyNUJLQ216Vko5OXpsTmo4WHAxTENDZU13WkJlL1dmZE9XZWhOZDdkN0czOHVxVkVtMTd3SHhmTWQ0dm45a29JcGYwSTRybjZqclRxNksxaW1lWWpSQzYvUng3YnE1UU5rdytVVjRveXdmM3NYeG1HeHl1NlZBQ0ZLb21GTlBMSDFxQ2ZtVVFwR0hvdUZNOEV1TE5aYlVUbUZXNFArN25UZkV6ZFo1QmllMzlYM2lUalBmZUlGVHhxN3lrTDNnZ3g5MWhNa05GbVd4THVFeGNCODk0bTNTdE1QYTNMTTY2OTlFVGtBR29PSlBXdldUZ1RCeTJHa2NMWXE1eUFiMDNwakI3QjdLMEpUZ2Uxd2ZnWjRrdjBRc1c4WnBFdmZtWEsiLCJtYWMiOiI5NzAyYWVmNjhiYTkxMGE1ZGMyMzlmNzU5NjM3NDIwNDZhYmZmZTQ1OWVjNzZjZWM4MThhMDE4ZmEwZWVlODMxIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 00:19:45 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6InNOQ3RmU0VPV210Kzl1NlE1eUlMWlE9PSIsInZhbHVlIjoiUjhYQ0tVd3ZWbDMyR1dnRkN0QmtwV3ZkZk8wbWg1RHJ1OHZnc0ovcklRR2E0cVpkS2NiSS9XaHlmckZ6VkRZYVdSc2RkaUx2NU15VE1QcDZVM2xjNHJCbTdtdnRpdi9nNUY5ejRGVjdhcU5raXdnQVJVSXd0NjNXNE53TUYwSUEwcXFmekZLS25PR0IyblR1RlNyb3kveWMwcUloTlpYMHN0MGZXL00yaHd1eWwxb1F3T0FHRk5DeHVSZTZCNnZsaDV2YlNTMURRUFBJQkVFTTBEZmNJV3NyczJVaFVFeUliZ1BNeksvVVFsUTF5VTRsQWYvUFFHRjduL0wxRDhxOXU0am0rQlV1TjFSUmVtLy83MjhONzN4MjdtNnBQVi9iZWx2QmwxZzJCOUZXYUpvUk1ocGx2NzUrZXRZQjMxMGtTa0c1Zm9meThTQUhQbjhDUXVEV1FFbTJLZDR0MWNZNStIUEdpT21tbS95RWV5YW5hd2p1UWNzSjlFQ09DT3kzYU1RcGpxRVV5NVhaMUhodXhXbGtSaHloeWJDNDljSmF0QlBWcnBWNW4zSTlyYmRlaDZHSkk0REdnTjlNOXA1dkJJT0tvZDJRdVpSOHBESE1zcTRyWGsrd204NkxiVmNvdDBYU3RFbnRueVYvWHUwOW5WU3QxRDlmTE82SW5HRzkiLCJtYWMiOiI1OTRmZjRkNTlhMzhjMjE0MDhlYTQ0ZTdlMjRiMTRlYmNmNjZiNzcwYmM5ZmJjYmFkMjM1NTdhZmZiNjczY2JhIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 00:19:45 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6InlqRTlFUWNUaFp5ZnBSQ0I0OTF5UEE9PSIsInZhbHVlIjoiN0hTdEhyNTRXeXdHaE55NU5xTWM4TTh0bHlBaHEyeExXNDNVbkVueEtxb0hWTGdwRXd3L0NWdW5Va0pVNlVmaTREcjFuZVZTZEZ3c01EQ2tHbGNqNUdwTXVacWtCQ3IzcDZpZ3BSNlJ3TkUxZ2Z6MVYvSE5vUFFIY1lVcEJlcldpVWlHN015cWdpTjcvQXI3RHBvSSswMVcycFhWMmh1MlhlcmxXTzNRR0c3SGI4d3hQeGdnRmdiWnFiWWMxYUFtOW12N29rVEZmQ1BYd3d3akpnV2lMMnVmTStENlBpVDIyNUJLQ216Vko5OXpsTmo4WHAxTENDZU13WkJlL1dmZE9XZWhOZDdkN0czOHVxVkVtMTd3SHhmTWQ0dm45a29JcGYwSTRybjZqclRxNksxaW1lWWpSQzYvUng3YnE1UU5rdytVVjRveXdmM3NYeG1HeHl1NlZBQ0ZLb21GTlBMSDFxQ2ZtVVFwR0hvdUZNOEV1TE5aYlVUbUZXNFArN25UZkV6ZFo1QmllMzlYM2lUalBmZUlGVHhxN3lrTDNnZ3g5MWhNa05GbVd4THVFeGNCODk0bTNTdE1QYTNMTTY2OTlFVGtBR29PSlBXdldUZ1RCeTJHa2NMWXE1eUFiMDNwakI3QjdLMEpUZ2Uxd2ZnWjRrdjBRc1c4WnBFdmZtWEsiLCJtYWMiOiI5NzAyYWVmNjhiYTkxMGE1ZGMyMzlmNzU5NjM3NDIwNDZhYmZmZTQ1OWVjNzZjZWM4MThhMDE4ZmEwZWVlODMxIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 00:19:45 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6InNOQ3RmU0VPV210Kzl1NlE1eUlMWlE9PSIsInZhbHVlIjoiUjhYQ0tVd3ZWbDMyR1dnRkN0QmtwV3ZkZk8wbWg1RHJ1OHZnc0ovcklRR2E0cVpkS2NiSS9XaHlmckZ6VkRZYVdSc2RkaUx2NU15VE1QcDZVM2xjNHJCbTdtdnRpdi9nNUY5ejRGVjdhcU5raXdnQVJVSXd0NjNXNE53TUYwSUEwcXFmekZLS25PR0IyblR1RlNyb3kveWMwcUloTlpYMHN0MGZXL00yaHd1eWwxb1F3T0FHRk5DeHVSZTZCNnZsaDV2YlNTMURRUFBJQkVFTTBEZmNJV3NyczJVaFVFeUliZ1BNeksvVVFsUTF5VTRsQWYvUFFHRjduL0wxRDhxOXU0am0rQlV1TjFSUmVtLy83MjhONzN4MjdtNnBQVi9iZWx2QmwxZzJCOUZXYUpvUk1ocGx2NzUrZXRZQjMxMGtTa0c1Zm9meThTQUhQbjhDUXVEV1FFbTJLZDR0MWNZNStIUEdpT21tbS95RWV5YW5hd2p1UWNzSjlFQ09DT3kzYU1RcGpxRVV5NVhaMUhodXhXbGtSaHloeWJDNDljSmF0QlBWcnBWNW4zSTlyYmRlaDZHSkk0REdnTjlNOXA1dkJJT0tvZDJRdVpSOHBESE1zcTRyWGsrd204NkxiVmNvdDBYU3RFbnRueVYvWHUwOW5WU3QxRDlmTE82SW5HRzkiLCJtYWMiOiI1OTRmZjRkNTlhMzhjMjE0MDhlYTQ0ZTdlMjRiMTRlYmNmNjZiNzcwYmM5ZmJjYmFkMjM1NTdhZmZiNjczY2JhIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 00:19:45 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1984004896\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-995497355 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">DCK491V1GUyngHueDf4bnOw7DOzApt305JPGLVcA</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-995497355\", {\"maxDepth\":0})</script>\n"}}