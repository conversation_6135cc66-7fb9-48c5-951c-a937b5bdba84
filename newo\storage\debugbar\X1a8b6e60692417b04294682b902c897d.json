{"__meta": {"id": "X1a8b6e60692417b04294682b902c897d", "datetime": "2025-06-07 22:21:21", "utime": **********.552125, "method": "GET", "uri": "/search-products?search=&cat_id=0&war_id=8&session_key=pos&type=sku", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749334880.058638, "end": **********.552159, "duration": 1.493520975112915, "duration_str": "1.49s", "measures": [{"label": "Booting", "start": 1749334880.058638, "relative_start": 0, "end": **********.201274, "relative_end": **********.201274, "duration": 1.1426358222961426, "duration_str": "1.14s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.201297, "relative_start": 1.1426589488983154, "end": **********.552162, "relative_end": 2.86102294921875e-06, "duration": 0.35086488723754883, "duration_str": "351ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 53117864, "peak_usage_str": "51MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET search-products", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\ProductServiceController@searchProducts", "namespace": null, "prefix": "", "where": [], "as": "search.products", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FProductServiceController.php&line=1138\" onclick=\"\">app/Http/Controllers/ProductServiceController.php:1138-1234</a>"}, "queries": {"nb_statements": 8, "nb_failed_statements": 0, "accumulated_duration": 0.03967999999999999, "accumulated_duration_str": "39.68ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 16 limit 1", "type": "query", "params": [], "bindings": ["16"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.323197, "duration": 0.01745, "duration_str": "17.45ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 43.977}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.369035, "duration": 0.0011899999999999999, "duration_str": "1.19ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 43.977, "width_percent": 2.999}, {"sql": "select `permissions`.*, `model_has_permissions`.`model_id` as `pivot_model_id`, `model_has_permissions`.`permission_id` as `pivot_permission_id`, `model_has_permissions`.`model_type` as `pivot_model_type` from `permissions` inner join `model_has_permissions` on `permissions`.`id` = `model_has_permissions`.`permission_id` where `model_has_permissions`.`model_id` = 16 and `model_has_permissions`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["16", "App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 326}, {"index": 21, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 566}], "start": **********.425307, "duration": 0.00191, "duration_str": "1.91ms", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:326", "source": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php:326", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=326", "ajax": false, "filename": "HasPermissions.php", "line": "326"}, "connection": "ty", "start_percent": 46.976, "width_percent": 4.814}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (16) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 312}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 26, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}], "start": **********.433709, "duration": 0.00161, "duration_str": "1.61ms", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 51.789, "width_percent": 4.057}, {"sql": "select * from `warehouse_products` where `warehouse_id` = '8'", "type": "query", "params": [], "bindings": ["8"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/ProductServiceController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\ProductServiceController.php", "line": 1150}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.450501, "duration": 0.0136, "duration_str": "13.6ms", "memory": 0, "memory_str": null, "filename": "ProductServiceController.php:1150", "source": "app/Http/Controllers/ProductServiceController.php:1150", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FProductServiceController.php&line=1150", "ajax": false, "filename": "ProductServiceController.php", "line": "1150"}, "connection": "ty", "start_percent": 55.847, "width_percent": 34.274}, {"sql": "select `product_services`.*, `c`.`name` as `categoryname` from `product_services` left join `product_service_categories` as `c` on `c`.`id` = `product_services`.`category_id` where `product_services`.`type` = 'product' and `product_services`.`created_by` = 15 and `product_services`.`id` in (3) order by `product_services`.`id` desc", "type": "query", "params": [], "bindings": ["product", "15", "3"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/ProductServiceController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\ProductServiceController.php", "line": 1166}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.473783, "duration": 0.0017800000000000001, "duration_str": "1.78ms", "memory": 0, "memory_str": null, "filename": "ProductServiceController.php:1166", "source": "app/Http/Controllers/ProductServiceController.php:1166", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FProductServiceController.php&line=1166", "ajax": false, "filename": "ProductServiceController.php", "line": "1166"}, "connection": "ty", "start_percent": 90.121, "width_percent": 4.486}, {"sql": "select * from `product_service_units` where `product_service_units`.`id` in (5)", "type": "query", "params": [], "bindings": ["5"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/ProductServiceController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\ProductServiceController.php", "line": 1166}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.489748, "duration": 0.00122, "duration_str": "1.22ms", "memory": 0, "memory_str": null, "filename": "ProductServiceController.php:1166", "source": "app/Http/Controllers/ProductServiceController.php:1166", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FProductServiceController.php&line=1166", "ajax": false, "filename": "ProductServiceController.php", "line": "1166"}, "connection": "ty", "start_percent": 94.607, "width_percent": 3.075}, {"sql": "select * from `warehouse_products` where `warehouse_id` = '8' and `product_id` = 3 limit 1", "type": "query", "params": [], "bindings": ["8", "3"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/ProductService.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\ProductService.php", "line": 267}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/ProductServiceController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\ProductServiceController.php", "line": 1173}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.4980989, "duration": 0.00092, "duration_str": "920μs", "memory": 0, "memory_str": null, "filename": "ProductService.php:267", "source": "app/Models/ProductService.php:267", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FProductService.php&line=267", "ajax": false, "filename": "ProductService.php", "line": "267"}, "connection": "ty", "start_percent": 97.681, "width_percent": 2.319}]}, "models": {"data": {"App\\Models\\WarehouseProduct": {"value": 2, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FWarehouseProduct.php&line=1", "ajax": false, "filename": "WarehouseProduct.php", "line": "?"}}, "App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}, "App\\Models\\ProductService": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FProductService.php&line=1", "ajax": false, "filename": "ProductService.php", "line": "?"}}, "App\\Models\\ProductServiceUnit": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FProductServiceUnit.php&line=1", "ajax": false, "filename": "ProductServiceUnit.php", "line": "?"}}}, "count": 6, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 1, "messages": [{"message": "[ability => manage pos, result => true, user => 16, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1028057471 data-indent-pad=\"  \"><span class=sf-dump-note>manage pos</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"10 characters\">manage pos</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>16</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1028057471\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.448377, "xdebug_link": null}]}, "session": {"_token": "iD3eOztlFQTk9Wu93brk2PH5kNLllwdWSLzbHLjf", "_previous": "array:1 [\n  \"url\" => \"http://localhost/hrm-dashboard\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "16"}, "request": {"path_info": "/search-products", "status_code": "<pre class=sf-dump id=sf-dump-573901219 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-573901219\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-1271429323 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>search</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cat_id</span>\" => \"<span class=sf-dump-str>0</span>\"\n  \"<span class=sf-dump-key>war_id</span>\" => \"<span class=sf-dump-str>8</span>\"\n  \"<span class=sf-dump-key>session_key</span>\" => \"<span class=sf-dump-str title=\"3 characters\">pos</span>\"\n  \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"3 characters\">sku</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1271429323\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-464324843 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-464324843\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1772736819 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">iD3eOztlFQTk9Wu93brk2PH5kNLllwdWSLzbHLjf</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1938 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=17ceeen%7C2%7Cfwk%7C0%7C1960; _clsk=1yftgw7%7C1749334872942%7C5%7C1%7Ck.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6Ik9pQkVkMUJZMlhqNE92K1Z0QWJrTkE9PSIsInZhbHVlIjoiR1lieFI2ZDRwb0hpZ3doRWZkOHdlSVBkOXNBZUJ3UUViTTJleTZqb0RWaUlwVUtSd3Jkby95YVl4bEZUYms1bDBOM0xWYWVaQ2U0SHlHL0dnRjZCRHpSWnlkY2NURDlGdjRIZUh6aFhORWJlVDBpNHovSVZUQmNLb2NwU3J5WFVtenRacUQwY0ZzTG5zY2R4bUdDSUdQZ3lyUnNFZGMvL2hqYjNscGRzblRKS3gvTzdhakdnK1NBaFJzN2lPTzRvdExVcldDbThDUThTeWs5VDlkank1NEZSb0IwaFFlZmNMOHlTRlFkQ21aSDhvWkxoZHhQRHloWnBoaGR3b2FDRFNmMzVDSytqeDQvV08raWd3VWkyUEFHSitpaGJjU2VFbTlPMmw5aXR2cjgyTDhzMmZxWFdsVFNDV1pxYllVNnZPN29jaG53eXdySy84Sm9jN0FzS1ZPcWFBZzJtZitPQTR6TE1LTjBwdkU4cFhXV3JZUmp5cEphU0c5cHQxNGdVQkJkYkhHSEJUTnQrWU1XSG42UWQ1SjQzMnhQNkRBWXhkZFhsVlYzd3Vpc0dZZldQSklEYzNRWlRHaFdjV0lyZmhCcFpxZnVyNGNndkJWQnBZWlNDVkpzc2N3bVl3aEdmMnhWSVZialZpOVVLY3g0VW12Z28rSjhtSVYrUnVYdVciLCJtYWMiOiIyZTRjM2RhMDFmZjU3ZGIzZWRkMjQwOWEyNTI4NDAxZTBmN2I4NGU2MGNhMTQ0MzU2NTMzYTlmZjA0MTkzYmQ2IiwidGFnIjoiIn0%3D; test_session=eyJpdiI6Im5uZE50bTFZb1phcFZwaFBTRDB5M0E9PSIsInZhbHVlIjoiaC9IRjN2K2xtMkV6NkgzSUhFTE5EcFdGMXBBT1lSWjRQQ084OFhvUU5RUGdLdEFUUlpUYUFyVDAvT1Q2OGdMY05mOWFxeXRKUTdNdnFITDRlUGdmOU0vam4zdkdIck8xdVdOVW5EOXREY0RnR2kzajVONUd6Rm1JOExyQ2w4bkJqQkRPUHV0c2F5eHlncmdhVnVLbGxMK0xQbWd0RTlXNG9wdEhpYmt5OC9INnQvMGgvVXQ3MTRDOFR4OFFSVWYrWVRsMjZsakREekZkbHJpVDhSbjdCV2h1YSswVnRzUHozOHFGc0JZcmx1TlV1QzBaNlBoZnVaT29WRFVmcWhLUzRPVDlvdDc0T0ZQQlZtbTgzS21QZHpTcFJ1dlVyeWlMcEJCVGxxYnlrOTEwWHlQbXdQKy9lazlNMU9NaFJmQURBTVVITm9jUG5vMVE2dW1OZHFMd3pJMWw2UlFvU2o5MGhEM0xnZ1dRZ0hMamJCbEwzK3hsUFNoM3pmczBaYTRPZTBOemthMys0d084VmlwMWRieHlWM2FzTWcyWHF3VHYxWVVYcTd4NXNic2JWcVl1VnRGS3NoUWJ0TjFVdUZQeFRuYTF1VVc1RklEMXROREVmWjV3bnhMcFFGc05JbDVIMWNMV1dvLytMUzBuYmZYdUx5QTFOVGVBU0U1ayt3aU4iLCJtYWMiOiJiMzNkYzc4YTAwMGIzYTAzYWU4YTAzOGU4MWVjZWU2NzYzZmFhZjNkNWVhN2ZiMTA1N2Q5OTk2ZmM5MjVjNDI2IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1772736819\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1171287404 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">iD3eOztlFQTk9Wu93brk2PH5kNLllwdWSLzbHLjf</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">atMfj5qpj5gKx4OQYPP5PbQzRbuF7iB8X4zv4aOY</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1171287404\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1860176435 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 07 Jun 2025 22:21:21 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IndvUy9pQkdYR1dmZ0psWDVpSUkwMXc9PSIsInZhbHVlIjoiYmxITUgzVFBxNndhUGpWeHQzUVV6Z0d3Qmp4b0ZISXZIYmR1a2FRUzZpS1NJQzZYWHpJRlF4WlRlK0tCeUVWQkUrOE9ycGtHL0FGY2hSMjZRQ0lUYjlJZkwyZUdUNFZqUXVzaVlmVks5cjFvTUJqOThHQWd0Zjh6bG1PNjlsQWdKWVJUM1FrdmJVK2U4ZXhMbWl2M2d4Q1k2UFFaZDYwSXh6WWNLVVZZSnVXdHdkeHdpSCtuZFRKdmVtdE43L0lVeWQybGV5Z05KN3BRZEdQaFFKMDhyZGxnby9SN0tuWkgrcEZYc3hjR0d6elJ5cWFGcVc1ZTBHTWpZWEl6d1puKzFrV21JSTRaOXl3MThoR2lLYzI0cTVmSnExOXdGZ2tjc3pXVE1KYzQ1Z3FLVEgzTXdsdEs0TnVEUnNSV2hLMmEzdzRxM0I0UWxFcFpvUXA2alB2RUh0aUtUVlpCRWlMZ1RITWVlWnhRNDltWTFiU0VjM2ZUOWZIVTR6THRGbVhFamVVVldtRzNjT0dubDNsc281VTNxdVp1Ui9ZSTEvbGZnZnlwVEJPT29qaFAyQVFIaG1KeFBPcWhkN3g0bWx6MzZCZHdLd0VQWWs4SWo4Z0sxMnBPdTAzTWFmRzJleXRmZ3R3c1dRNll2bWMvVXZ2VGl2NUpKS29sNFBoRHFMbUMiLCJtYWMiOiIzNmY0NWM5MDNiOGFmZTU4NzQ3ODlmZWFkOTViMjg1NjQ2OTNkMGNiOGMyOWJlMDcyYjZiMTA2MTU5ZDE0ZGM0IiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 00:21:21 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6ImNxOEtUOFV3YmZmc3ZHRVB5Rml5QUE9PSIsInZhbHVlIjoiWFQ1dGFMTmFNUzV4emk3SFhUenAwaEVubjIzcGFXZThVZ0lNcVRpVkZmVHgxNXBWVHNLNmVvQW95RHhwWkMwZk5CVGphbXJaMlFTanVoMmVNOTZIeTdBdGdxS3RqU0NodmhPNTdjSUxmVkRrRldScS80RXZ0MUJXV3U1Qit4d2ZLa3F6cGJHQmNXRzdkSGIvUUFCaktKQWFlSjhjbFhQRGFiVFhtL0cxQ2wzUGp4NDdkUUZHTjBwRE1UOFg2ZkJ6MnpCYTdOT3JoRGpDUnZZOEU5Y3gyUVNuZ3VpSG56aFdKZFp2ZnVrVmdST2NCUS9ndytQYlBETFMvSEQyVmtIMmVhTVh3cDBaQk04amlGWmhTcEFNWlBidjA0dmxSanlCSG1zcVZuTTZWWVlVZFhhUE1RbVR3M2w3K0trVWVTRjd3RE9zUk9Qb2hWSHhyT3lBV1NlZFlLY2xtTXlBeUUyeGcrYTVMOXNoQmtGV1F3Rm5SaEQzazJxQndyK0h1dzNNQzVkYUQ5OUsrZ0RnWmpIZnBUbGJKQ1BtM1BGamt1aCtuOXc5V0RVb1BQN09VcW5tQU5lZDNjOUUybUNGaC91eVMydHowK2MrREFrVzZIais3WTZjczFDTEFONVhJUnZQNDNKWXp4aEdDV3lyaFI2V1pqYjhzQjdQWDk4ZzdoN0oiLCJtYWMiOiI3MDUzOTUyMmFhOTBjYWVkYmNmNWFkNzdhMDNkYjgxY2NiODcwZTRiMjA0ODgwZjY1YjYwYTUzZGVjMDEwYzczIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 00:21:21 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IndvUy9pQkdYR1dmZ0psWDVpSUkwMXc9PSIsInZhbHVlIjoiYmxITUgzVFBxNndhUGpWeHQzUVV6Z0d3Qmp4b0ZISXZIYmR1a2FRUzZpS1NJQzZYWHpJRlF4WlRlK0tCeUVWQkUrOE9ycGtHL0FGY2hSMjZRQ0lUYjlJZkwyZUdUNFZqUXVzaVlmVks5cjFvTUJqOThHQWd0Zjh6bG1PNjlsQWdKWVJUM1FrdmJVK2U4ZXhMbWl2M2d4Q1k2UFFaZDYwSXh6WWNLVVZZSnVXdHdkeHdpSCtuZFRKdmVtdE43L0lVeWQybGV5Z05KN3BRZEdQaFFKMDhyZGxnby9SN0tuWkgrcEZYc3hjR0d6elJ5cWFGcVc1ZTBHTWpZWEl6d1puKzFrV21JSTRaOXl3MThoR2lLYzI0cTVmSnExOXdGZ2tjc3pXVE1KYzQ1Z3FLVEgzTXdsdEs0TnVEUnNSV2hLMmEzdzRxM0I0UWxFcFpvUXA2alB2RUh0aUtUVlpCRWlMZ1RITWVlWnhRNDltWTFiU0VjM2ZUOWZIVTR6THRGbVhFamVVVldtRzNjT0dubDNsc281VTNxdVp1Ui9ZSTEvbGZnZnlwVEJPT29qaFAyQVFIaG1KeFBPcWhkN3g0bWx6MzZCZHdLd0VQWWs4SWo4Z0sxMnBPdTAzTWFmRzJleXRmZ3R3c1dRNll2bWMvVXZ2VGl2NUpKS29sNFBoRHFMbUMiLCJtYWMiOiIzNmY0NWM5MDNiOGFmZTU4NzQ3ODlmZWFkOTViMjg1NjQ2OTNkMGNiOGMyOWJlMDcyYjZiMTA2MTU5ZDE0ZGM0IiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 00:21:21 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6ImNxOEtUOFV3YmZmc3ZHRVB5Rml5QUE9PSIsInZhbHVlIjoiWFQ1dGFMTmFNUzV4emk3SFhUenAwaEVubjIzcGFXZThVZ0lNcVRpVkZmVHgxNXBWVHNLNmVvQW95RHhwWkMwZk5CVGphbXJaMlFTanVoMmVNOTZIeTdBdGdxS3RqU0NodmhPNTdjSUxmVkRrRldScS80RXZ0MUJXV3U1Qit4d2ZLa3F6cGJHQmNXRzdkSGIvUUFCaktKQWFlSjhjbFhQRGFiVFhtL0cxQ2wzUGp4NDdkUUZHTjBwRE1UOFg2ZkJ6MnpCYTdOT3JoRGpDUnZZOEU5Y3gyUVNuZ3VpSG56aFdKZFp2ZnVrVmdST2NCUS9ndytQYlBETFMvSEQyVmtIMmVhTVh3cDBaQk04amlGWmhTcEFNWlBidjA0dmxSanlCSG1zcVZuTTZWWVlVZFhhUE1RbVR3M2w3K0trVWVTRjd3RE9zUk9Qb2hWSHhyT3lBV1NlZFlLY2xtTXlBeUUyeGcrYTVMOXNoQmtGV1F3Rm5SaEQzazJxQndyK0h1dzNNQzVkYUQ5OUsrZ0RnWmpIZnBUbGJKQ1BtM1BGamt1aCtuOXc5V0RVb1BQN09VcW5tQU5lZDNjOUUybUNGaC91eVMydHowK2MrREFrVzZIais3WTZjczFDTEFONVhJUnZQNDNKWXp4aEdDV3lyaFI2V1pqYjhzQjdQWDk4ZzdoN0oiLCJtYWMiOiI3MDUzOTUyMmFhOTBjYWVkYmNmNWFkNzdhMDNkYjgxY2NiODcwZTRiMjA0ODgwZjY1YjYwYTUzZGVjMDEwYzczIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 00:21:21 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1860176435\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-44281489 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">iD3eOztlFQTk9Wu93brk2PH5kNLllwdWSLzbHLjf</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"30 characters\">http://localhost/hrm-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>16</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-44281489\", {\"maxDepth\":0})</script>\n"}}