{"__meta": {"id": "X3f1c682e51fe2b4cecb2dd7a7eea61a1", "datetime": "2025-06-07 22:19:17", "utime": **********.257202, "method": "POST", "uri": "/chats/favorites", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749334755.823913, "end": **********.257244, "duration": 1.43333101272583, "duration_str": "1.43s", "measures": [{"label": "Booting", "start": 1749334755.823913, "relative_start": 0, "end": **********.085683, "relative_end": **********.085683, "duration": 1.2617700099945068, "duration_str": "1.26s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.085707, "relative_start": 1.261793851852417, "end": **********.257249, "relative_end": 5.0067901611328125e-06, "duration": 0.17154216766357422, "duration_str": "172ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45039632, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/favorites", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getFavorites", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "favorites", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=454\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:454-477</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.0074399999999999996, "accumulated_duration_str": "7.44ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.178591, "duration": 0.0050999999999999995, "duration_str": "5.1ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 68.548}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.212214, "duration": 0.0011899999999999999, "duration_str": "1.19ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 68.548, "width_percent": 15.995}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (15) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.231851, "duration": 0.00115, "duration_str": "1.15ms", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 84.543, "width_percent": 15.457}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "cN6pwMHluqMWM9hLaRtOtRasnQF8pfN3ghxjsOfa", "_previous": "array:1 [\n  \"url\" => \"http://localhost/financial/productservice\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/favorites", "status_code": "<pre class=sf-dump id=sf-dump-827616322 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-827616322\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1793594165 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1793594165\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-409968573 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">cN6pwMHluqMWM9hLaRtOtRasnQF8pfN3ghxjsOfa</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-409968573\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1293693422 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">http://localhost/financial/productservice</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1993 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=a091af98-270d-406e-ab7e-33a6e610d6e2414686; _clck=nups8l%7C2%7Cfwk%7C0%7C1960; _clsk=ij6lzq%7C1749334722506%7C7%7C1%7Ck.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IjB3U094UEY1YVFxZ2duZjBvMzJrUHc9PSIsInZhbHVlIjoiYmYrUzIzQVIyM2NvcEtrdDRpQW9sL2RTMFJueHdER1hFNzlLbEsyaHpQMUVIQjVZZlY0L3A4N2hhcHlWSU8xSUR0d0NaZ1YwWHZvVVZ2ZnJvQmJKMFdwM0E4ZXEzQ1FtdGdsSitTM3QxN3VUeUdva0pJYUNwa0prQ3RsWnp6eE1KSWRPYjNtOWxLaUxzTVZFN3N5bTNvU2VGQnlXazFRYlRRay9QVVpPUzZpQ1ZkeHZLN2N1YzRGdEpUQlgrMVhqbUc2UnpyNDJ1YzcwbUV2RjJDam1acituVVhkbi80MUFSc3ZIL1hoYTd4dVhGczJLWG1Oemc3TWdXK3dlbnY1ZDB2V05rd0FxeTlsZVZNQlZEVW9xVXg5ME9yY1VBU3Z0OENNei9JQThqdXdDUnVEaDFENktjb2VLL2ZTYmNUQ0R4RkQ3NGw5ZSttTE9oVCtLK0lnS2tYVGdzaytPdytZSThnL0VhY0lQWjMzdXZ1Y0t2WUpIUk1YRFlIWWxybGc2NDhsN0cyTWF3aU1yZThEWTlzSC94cVhKR1pCMENpTGpuRnpLaVBIc2pud0FsVEl4bmFZeDdDLzA1T0VpZ1N4dlQ2RVJPMUFuQ0poSDVwOGZBcUFEN3NzUTdIN2xuelo3bm1hc1RLYTM1NDhKSVU2SEhDNy9wSnNnU01UaHNpM2siLCJtYWMiOiI2MmRmYTE0ZWM1NmVjZjI1ODdkMTRhOTU4OTVmZTE4ODIwMjUzNjVmYjVkZDJhZGVmMjk1NGRkYzQxMGQ2MTIzIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6IlBYQzB0bkMwbVZWRFFZTGc5U053eHc9PSIsInZhbHVlIjoiKzBLOVN4cnNDWmpsUmh5akFhQUhkNk9xUFc3ayswV0s4VGV4MG8zUFF4ZHcxbkg5aG8rcGxmNTdleFZzdXVnVXZLaHBqbU90N1o0WExjQTRwb1NDSzg2WFNiNDNvLzlnT0hjQzk2WHFqdndtWUFUekF5a09EN0dLZTRwTW9WN3dEakJ1b09JVFJLaCt4eGZKVWw1MkMrV2JSQjIwOHRabWZWU1NvWXhKNEd4cC8reUNnRDRVY2lBM2Z2V281OVJpRHpRaXRzd2lNZUxOem1teUIyeFBqOS83QkszYkxVb3lsV3FMbFBhTldiSTNJY3RZYXZCOTBUdGZZY29MVC9yMGtSenpHWnZvYTRFaFhMV1MrTzZUSnY5L011elBpMGdoa0VXb0tQUTlBOWRNcWo3NzRwbE5sMyt0d0h2Wk9KRU13L1c3VE9kTU5QT3dkRFVTYno5ZTdzdXFLMnc5TzZEUVlaelREZmJ2MXkrRUxtMEY1S0F4Z2paUmxSYXhIZzM0MXM4c2xLR3NXakhFQVBhSmtYbmtZKzJ0NEZxSjMwMWJhVDRzelRieEJTMmI1VCtuTkxYMndyZDFDKzVlUUpOQzU5bE5xSFd4OXIvbllCU1ByZUMyTWhwbTJjVlhqaE5wTU9uZGxXcFo5bkxHS3R1RjhFSE9CVFNmOFN1cUFGcUIiLCJtYWMiOiI0YThiNTkxYzhjNGRlYTZhZmYzY2EwMzA4MDEzZGM0YzU3ZWI3ZDYwNjZmNGNjZTM0ZjhkZTkwZWEzMzQ4ZmIzIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1293693422\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-725523094 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">cN6pwMHluqMWM9hLaRtOtRasnQF8pfN3ghxjsOfa</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Eo35AMmp3Bkf2zsyHLroae0N6MdUih7xJ0ojLwSF</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-725523094\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-104052796 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 07 Jun 2025 22:19:17 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Ik5uSit5VXN5V1VlY0lLemo2NnFLQnc9PSIsInZhbHVlIjoiK3cwMy9EMHBsVkdDTnhZbkx6R3cwSGp6MVkraXZaSVZKT1o1bGVNeElzT1pPRTFEWndRakJlTEpCbTE5RlFmN0RpMmZjNXM4V3NXODNySVpsOU9JdE50TGNneHNJa0hnVWdWRjVkYjhMTlNVTWdQWEdrT3dMdjhVd0ZDYVkrcmJXN2duZ0kwYTV2Q3ZBcUgvajBvWEdCaUE5T3JEMWMxYzcyUmpFYlNjZ2pjV0FWRmtPYUhoWWxkNU04SWNDZ0FMVVRVMDlZdWkxUWtZeUJLOHdXdU5yaS9acXQ3UzNvNEUybTBvaUtmOGFQT2M3cjZraVZ3YW52NEM0NzFKZGNnSTFhOGVUaERRai83dDRKOWt0dW1LYk16N1pHVGNiQjdpVHFuSEw5UkZlKzBJWmNhUlphR2ZVc3JJKzJ2WTJBU0VBL1hmWlo3V2wwNXBrVW9xMzh4RGxJNG5GWmNoT3JPbVdZTVhCMlF0U2sweUtDSDNIU0VXS01QSWlTazcwcVg0YklpMWlvNVNKbWZxUjAxelMrNWVMTWpqc3k2VndycGY5R0c2S0VSazJNbGhNcytWdzdBV1dKRW52U1kyWVJmL0JDaWRNUEQ0cGk4RVhWOUxLTHM2WXlmTUN6azdJMi9OcGI5RlZ5THlDUmp2bFRhenlGT1ZZdzZLK1NMdUlwOFQiLCJtYWMiOiJmZTc3NTI2MGMzODQ2YmM5MzBiY2Q4NTliOTMyOWIzZjIwNzVmNTY5NzEyZWNmZjhhNmU0OWNlNGViYmUxNTJiIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 00:19:17 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6IjZSbG02Nll1RHVqalZPTTUwRGwvVEE9PSIsInZhbHVlIjoia3NuOThkRzZlTTF4WnJXUkNwSWpIQ2RWaW9UOGFOc3Nnc3FVR2tvbzZLR0hNYWpudVJFOCtMSmVZNjlqSXA4bWNQdU1WemZ1VnNkMncxSk5nVEJ4VUdLc1d6S3U4YnhIVzdNRWJjcmlKRUo3OFlqdHJqMDZnS3FzZ1FybjNpY3c2WTQ5c2RmeGhGOERSL1A1d1UrOXBQcUE4WGt3b0pIVjY4YmlKbVI5a1F0bUxneFZLR0ltdG9wc1lPQklYUHJmV0tiRUZ5UVJjdGVnb3VGaFR5ZlUrejV6VENyYTJBQXVVdVdaN2MrUmJrV2hoUTMwY015a1dQWW9qVlBsWmpzMGdiU2dnYlBlUmJGT255RjlPRkNLTGo5NEtibTJOWU9oZkFmMm9XdDB4aUhKY0JaRDAxeDBZelpBWGVvVkNLd1lRK3RsdGdsdWNxaldycS9GTjFoUFN5WDNEV1hCdE9LMjdQVlJxanRKaWhlSUxiR2lYckJlRUUwQ1l1L0t3RnlGckFWVUJjL0NJNGpGWHRQTklwbjJGb0tJVm0rU2phV3dPM1hlTnJWeXQzS2pFWVFjVlROei9UOTJqWHE2S1ZQY25sVENuaDJUVE16YVUrY2dtdDFCVXZ2MFIzeHlnSWdkbkhkOVRpaEIvOHdWeWtoL0NoQ0lMaWdGVGZSNHRQMjgiLCJtYWMiOiI5MzQzMGNhZmUwMTZmMTg2ZWNhMWQ4MTU0YWQ1MjY0OGViNzY5MGRiZWI4NWFkOTc2NDI3OTUxMmEzZWYwYTc2IiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 00:19:17 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Ik5uSit5VXN5V1VlY0lLemo2NnFLQnc9PSIsInZhbHVlIjoiK3cwMy9EMHBsVkdDTnhZbkx6R3cwSGp6MVkraXZaSVZKT1o1bGVNeElzT1pPRTFEWndRakJlTEpCbTE5RlFmN0RpMmZjNXM4V3NXODNySVpsOU9JdE50TGNneHNJa0hnVWdWRjVkYjhMTlNVTWdQWEdrT3dMdjhVd0ZDYVkrcmJXN2duZ0kwYTV2Q3ZBcUgvajBvWEdCaUE5T3JEMWMxYzcyUmpFYlNjZ2pjV0FWRmtPYUhoWWxkNU04SWNDZ0FMVVRVMDlZdWkxUWtZeUJLOHdXdU5yaS9acXQ3UzNvNEUybTBvaUtmOGFQT2M3cjZraVZ3YW52NEM0NzFKZGNnSTFhOGVUaERRai83dDRKOWt0dW1LYk16N1pHVGNiQjdpVHFuSEw5UkZlKzBJWmNhUlphR2ZVc3JJKzJ2WTJBU0VBL1hmWlo3V2wwNXBrVW9xMzh4RGxJNG5GWmNoT3JPbVdZTVhCMlF0U2sweUtDSDNIU0VXS01QSWlTazcwcVg0YklpMWlvNVNKbWZxUjAxelMrNWVMTWpqc3k2VndycGY5R0c2S0VSazJNbGhNcytWdzdBV1dKRW52U1kyWVJmL0JDaWRNUEQ0cGk4RVhWOUxLTHM2WXlmTUN6azdJMi9OcGI5RlZ5THlDUmp2bFRhenlGT1ZZdzZLK1NMdUlwOFQiLCJtYWMiOiJmZTc3NTI2MGMzODQ2YmM5MzBiY2Q4NTliOTMyOWIzZjIwNzVmNTY5NzEyZWNmZjhhNmU0OWNlNGViYmUxNTJiIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 00:19:17 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6IjZSbG02Nll1RHVqalZPTTUwRGwvVEE9PSIsInZhbHVlIjoia3NuOThkRzZlTTF4WnJXUkNwSWpIQ2RWaW9UOGFOc3Nnc3FVR2tvbzZLR0hNYWpudVJFOCtMSmVZNjlqSXA4bWNQdU1WemZ1VnNkMncxSk5nVEJ4VUdLc1d6S3U4YnhIVzdNRWJjcmlKRUo3OFlqdHJqMDZnS3FzZ1FybjNpY3c2WTQ5c2RmeGhGOERSL1A1d1UrOXBQcUE4WGt3b0pIVjY4YmlKbVI5a1F0bUxneFZLR0ltdG9wc1lPQklYUHJmV0tiRUZ5UVJjdGVnb3VGaFR5ZlUrejV6VENyYTJBQXVVdVdaN2MrUmJrV2hoUTMwY015a1dQWW9qVlBsWmpzMGdiU2dnYlBlUmJGT255RjlPRkNLTGo5NEtibTJOWU9oZkFmMm9XdDB4aUhKY0JaRDAxeDBZelpBWGVvVkNLd1lRK3RsdGdsdWNxaldycS9GTjFoUFN5WDNEV1hCdE9LMjdQVlJxanRKaWhlSUxiR2lYckJlRUUwQ1l1L0t3RnlGckFWVUJjL0NJNGpGWHRQTklwbjJGb0tJVm0rU2phV3dPM1hlTnJWeXQzS2pFWVFjVlROei9UOTJqWHE2S1ZQY25sVENuaDJUVE16YVUrY2dtdDFCVXZ2MFIzeHlnSWdkbkhkOVRpaEIvOHdWeWtoL0NoQ0lMaWdGVGZSNHRQMjgiLCJtYWMiOiI5MzQzMGNhZmUwMTZmMTg2ZWNhMWQ4MTU0YWQ1MjY0OGViNzY5MGRiZWI4NWFkOTc2NDI3OTUxMmEzZWYwYTc2IiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 00:19:17 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-104052796\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1346496119 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">cN6pwMHluqMWM9hLaRtOtRasnQF8pfN3ghxjsOfa</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"41 characters\">http://localhost/financial/productservice</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1346496119\", {\"maxDepth\":0})</script>\n"}}