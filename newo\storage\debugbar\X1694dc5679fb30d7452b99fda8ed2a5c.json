{"__meta": {"id": "X1694dc5679fb30d7452b99fda8ed2a5c", "datetime": "2025-06-07 22:19:09", "utime": **********.48227, "method": "PUT", "uri": "/financial/products/3", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749334748.057361, "end": **********.482303, "duration": 1.4249420166015625, "duration_str": "1.42s", "measures": [{"label": "Booting", "start": 1749334748.057361, "relative_start": 0, "end": **********.270164, "relative_end": **********.270164, "duration": 1.2128031253814697, "duration_str": "1.21s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.270185, "relative_start": 1.2128241062164307, "end": **********.482306, "relative_end": 3.0994415283203125e-06, "duration": 0.21212100982666016, "duration_str": "212ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 46879272, "peak_usage_str": "45MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "PUT financial/products/{id}", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\ProductServiceController@financialUpdate", "namespace": null, "prefix": "", "where": [], "as": "financial.products.update", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FProductServiceController.php&line=613\" onclick=\"\">app/Http/Controllers/ProductServiceController.php:613-646</a>"}, "queries": {"nb_statements": 5, "nb_failed_statements": 0, "accumulated_duration": 0.01003, "accumulated_duration_str": "10.03ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.355048, "duration": 0.004940000000000001, "duration_str": "4.94ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 49.252}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.385125, "duration": 0.00113, "duration_str": "1.13ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 49.252, "width_percent": 11.266}, {"sql": "select `permissions`.*, `model_has_permissions`.`model_id` as `pivot_model_id`, `model_has_permissions`.`permission_id` as `pivot_permission_id`, `model_has_permissions`.`model_type` as `pivot_model_type` from `permissions` inner join `model_has_permissions` on `permissions`.`id` = `model_has_permissions`.`permission_id` where `model_has_permissions`.`model_id` = 15 and `model_has_permissions`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["15", "App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 326}, {"index": 21, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 566}], "start": **********.4336412, "duration": 0.00152, "duration_str": "1.52ms", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:326", "source": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php:326", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=326", "ajax": false, "filename": "HasPermissions.php", "line": "326"}, "connection": "ty", "start_percent": 60.518, "width_percent": 15.155}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (15) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 312}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 26, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}], "start": **********.441359, "duration": 0.00108, "duration_str": "1.08ms", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 75.673, "width_percent": 10.768}, {"sql": "select * from `product_services` where `product_services`.`id` = '3' limit 1", "type": "query", "params": [], "bindings": ["3"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/ProductServiceController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\ProductServiceController.php", "line": 616}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.4583461, "duration": 0.00136, "duration_str": "1.36ms", "memory": 0, "memory_str": null, "filename": "ProductServiceController.php:616", "source": "app/Http/Controllers/ProductServiceController.php:616", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FProductServiceController.php&line=616", "ajax": false, "filename": "ProductServiceController.php", "line": "616"}, "connection": "ty", "start_percent": 86.441, "width_percent": 13.559}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}, "App\\Models\\ProductService": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FProductService.php&line=1", "ajax": false, "filename": "ProductService.php", "line": "?"}}}, "count": 3, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 1, "messages": [{"message": "[\n  ability => edit product & service,\n  result => true,\n  user => 15,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-662237385 data-indent-pad=\"  \"><span class=sf-dump-note>edit product & service</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"22 characters\">edit product &amp; service</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-662237385\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.455216, "xdebug_link": null}]}, "session": {"_token": "cN6pwMHluqMWM9hLaRtOtRasnQF8pfN3ghxjsOfa", "_previous": "array:1 [\n  \"url\" => \"http://localhost/financial/productservice\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15"}, "request": {"path_info": "/financial/products/3", "status_code": "<pre class=sf-dump id=sf-dump-621850077 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-621850077\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">cN6pwMHluqMWM9hLaRtOtRasnQF8pfN3ghxjsOfa</span>\"\n  \"<span class=sf-dump-key>field</span>\" => \"<span class=sf-dump-str title=\"20 characters\">sale_chartaccount_id</span>\"\n  \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"3 characters\">274</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">84</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">http://localhost/financial/productservice</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1993 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=a091af98-270d-406e-ab7e-33a6e610d6e2414686; _clck=nups8l%7C2%7Cfwk%7C0%7C1960; _clsk=ij6lzq%7C1749334722506%7C7%7C1%7Ck.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IjhHU3ZwLzJLUm9iRStpdE1IOFJDNmc9PSIsInZhbHVlIjoiV0dncHlKOTdIYkI4T213M1lLdHhvRTFYYVBobmh1ODlEYmo2eGFIYmM2WkdoVjFweXBzTHVXbVg4Zmd3b2JnK0h6eWlJWWRzdGRCVndPb0FESzU4Q3NDN0tXY0lDUEEvRUdsb3NiNjhqV1J2Ry9JTzZUVktxNlF3QW9Kb0laTHk3WUk3c0pBVElEVGt3WHdsd0N4ZkJLcjJ2VVRwdkxpMi9DVi9QUDZPM3dka0dNMFVQbVNDU0ZNOUNYOGxIZ2c2Vzl5OEllVVRWUmdIMXJ1OEFwNERmMHRZeklEdmt3ZjF2YTBwemlFelluODB5VUJlSU5tNXhEZGgvaDUrR0Q3UXgrV3FJOVBsL2x5cEVxMTJiUG15RlZzN2cvVyszcEQxV2VhWXJVRGFhYjBEMzREOVVGNWRLcis1NHk4cVpRRWk2eSs2eGxGRnVHTTExank4NTBjZ0RyMWdrRTgveVVBenVtc2hiQkpLQU9NTmpORFBTRkJuTTV0WHFPTmRUeVJQVjRxc08wWjYvRDEvc0lWM1pUTFk3VjlwWWMzbG1CUGRYL3BxU1lDcVNkN2JtbWhVVFgrK3Z1bVMzamlZK3d2ci96eUZjdXcwZUk2NUdGdEE2UytPcGJ4TXkzZzBCN2hRdkJOdW84eWlHWnIwTzkwSU9JTmNFMzh0UnFXSzFCTWUiLCJtYWMiOiI3NDJjNTVmMWJlMWM2ODA2YmRhYTc4MDdiNzBmM2ZmYmM0Y2E1MTBkNDJlZjM4NTQ2ZDYwZmIxZDIxYzBmNWY3IiwidGFnIjoiIn0%3D; test_session=eyJpdiI6IlBRUFVFWTBsQnU5aHV0Wk1DRkpFK2c9PSIsInZhbHVlIjoia0xSRk9EbmlDWVc1T08reEhpTktvNTBNRlpYcTc5ditKeUhCTmJ4RlRCa2lERVQ0MzFvVzhBb3U2VSsyWHd1YWJtL29rK1lRUEVxZCswWlBLZ2FTdlVlSFRCZGVDcmNRWDZvMmJNcDlZcDJNWlBaNXh0VXdMeElGakREcDVzYy92cHExZFB3djR2L3JKQ1lJR3MwN1hQNCtqS0thUTRaektwT3gySFF3ajVoWXNMd084cU5ESjlpSHV5d2NSZHU5TmQzb3JzQm1sbDlZUWZCS2x1MmdUaTZPSjZKV1U4dHpITStSMWNuZy8xbENKenZBNDFWYzJiZ0JkM2Z4TDBzNGJjZXo5UEdGb2g1S1pia3EwY1AyajRzNUlsRlJ3c1ZaS285K3pWeUFsTlZ3TWtxUkJXNUtXSm4xamxsWGUwVlBZYmVrbUhTeFZ5djIrbk42ZTVDSHNWK243TTM0K01uaWpuSWRKUURBQTBqM2xiWGMyd1FsdzQ0OFNlQmlCdUpRb2c2VEMxWWJuMkJCZWdGNDh0Nk5MQ1dsQUxPb2J6cWdiU0xWbVFSZ25ncEMyTk1MQTVGUjNSanc1a3l1WHNlS20waktlaWlXVVg4R2VNa2duOGlRU2lGWm5WQm5ONkRmdTZDMUkxYkorS0tpc0s5Q1JPYnJUUFJGVDg5djJHYXEiLCJtYWMiOiJhOTJjMmFkYTk3ZWVjOGFhMmUxYTk0ODBmYjg5YTQ5OTdhNTgzMmRkNjQ3ZWVlYWQ1ODU1N2RkMDdmMjExNjcyIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-213704201 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">cN6pwMHluqMWM9hLaRtOtRasnQF8pfN3ghxjsOfa</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Eo35AMmp3Bkf2zsyHLroae0N6MdUih7xJ0ojLwSF</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-213704201\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-435376118 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 07 Jun 2025 22:19:09 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6ImVIRHVhYzQ5RUJCOWNxWFVvSjlraWc9PSIsInZhbHVlIjoiUHpBWGhCWHNiWHFpakRoSlhhbWgrNVBiN1Rad3EzS1JOd3JGTlk0RHBIWk5Ta2NNYml1R29FSFA5U3I4Zmxza3RBaXlPM2syUjNmNVlzQTZDeWdTRG42NGFsRk9kYk9waGVReTJicDByNXRFUDJOTUV2aXF1UzZHcitWbUEzWkY5MTUzWUdiK1FycThNMkxWcTdQTjJKNmlqUG5OdVY1Z3ZWRFVZK3pzRHNDTG1MbTR6WW1EVmc2SnJJYjNMMlZVMHBOQmFoNVR3TUp5VXZiNFFDWkZzWittaWNMSlVmZkV3RnowMkwzeWFoOHMvUVdXZ3JPTlBreCtGTnprMVUyOUhuU0lKTFlqUnBNUnFmc00zNE13R0JuS1NGUVBLRG5QZEpKK2JNZ3Boai9CNVFXc2ZlYkw0YnN4RVpxeTIxeU13ZHVGcW9KTEFRZUtaSFV4L1pxczY1VlVGU2lGajBqdFJQNTNQY3dGS3g3bWt0WmorT0FSRnkyUVR6aW9yV0Z6dU9nMEx0MkpFUlNSVkU1UzVhaGx4MG1ZRE9OQ0s0aGlaNW1EcjRiS3grOFR6Rk9La0JDcWk4cTh0UEdhZ0tGcHVLMW5XZk5nTTZNL013Tk9kSGN1SXhkVHN0c0VCYUVxZHd5dHZxVld3MW1WS202S0lJSmNnOWJhVi9WK0F6Y2giLCJtYWMiOiI0OGJhNDRlYjcxOWFjNWUyODA0YmU3Y2M2NzZiOTQ5MzRiOTFkZGY0MGY1YTQ4MjVjNzk1NWQ3ZmFjZjNlNzFmIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 00:19:09 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6IjRucFZGbFdvalZTS3d2VmFmUmZ6L1E9PSIsInZhbHVlIjoicVdTZXA5ai8vNFd2OXZ5bVhJWUd0ZG4wR3NRelp1cmJ3ZnArVFppbE9PVFdkQlFZV1QwQWp6d0ZJWSthTHpLQUFTWTdaaDBQLzROV2UrN0FaU1lCTWMrN1pwYnpGb2o0R0U2bFZPWWZsZmx3Um1VY05CKzZ4N2hQVlFkNTRyK09OZlJSb0E5Nm5EZkNWUG04M2Y3dERSRzIvSE5IRWUvb0dpcFFGZERTMkFHYmxWdnlvN0xoSE5Oa29sY2VlLzdFUkxqaE9Xa3VtUmpVcXlKSnRsL2llUi9UaCtkcVFSSUU5NXNTRWppMysvZEZldHdoeEdhWFBoendDcStKVndxUTZSZm5GVlBQUm1mZU9sUjd3d1IxdzJpNkUvdEdlWStQYVRWRHJRUHRHelJOQm1hU2NsY0JoRjkxQjN6dmJYeGNiSVFHOVg4NEJzZjEvRWtNWXhRYTlUbFQ2cmdnMk96RjVYeEVXbXdZeHM5RDhiVXpPVWVaaGVqK0ZYL3FJQ0lhYmUrR2IwUjI1c3VkNnR6UFM4b0pYVnBUSUlhbDNpVGR3TW9WSGtZR3BWUDlUekwzcWJOSjNNREw4SXhKNFFIMEtYWnhUK0E0U3JSb2l1WFo4N1dLOW9scVB0eTNXMTVLRDgzRTZ2VUZSMDRFMWlrSnZlalNya3FMT0ZNeHZSNCsiLCJtYWMiOiIyNjgzZTM3OWVlNTc4NzJkNjUxYjQyMjQ4MTg3OWVhZTUyZGQ5YTY3MDRlY2EzMzgxMDY5MTg4MDE1ZjQyZmMwIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 00:19:09 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6ImVIRHVhYzQ5RUJCOWNxWFVvSjlraWc9PSIsInZhbHVlIjoiUHpBWGhCWHNiWHFpakRoSlhhbWgrNVBiN1Rad3EzS1JOd3JGTlk0RHBIWk5Ta2NNYml1R29FSFA5U3I4Zmxza3RBaXlPM2syUjNmNVlzQTZDeWdTRG42NGFsRk9kYk9waGVReTJicDByNXRFUDJOTUV2aXF1UzZHcitWbUEzWkY5MTUzWUdiK1FycThNMkxWcTdQTjJKNmlqUG5OdVY1Z3ZWRFVZK3pzRHNDTG1MbTR6WW1EVmc2SnJJYjNMMlZVMHBOQmFoNVR3TUp5VXZiNFFDWkZzWittaWNMSlVmZkV3RnowMkwzeWFoOHMvUVdXZ3JPTlBreCtGTnprMVUyOUhuU0lKTFlqUnBNUnFmc00zNE13R0JuS1NGUVBLRG5QZEpKK2JNZ3Boai9CNVFXc2ZlYkw0YnN4RVpxeTIxeU13ZHVGcW9KTEFRZUtaSFV4L1pxczY1VlVGU2lGajBqdFJQNTNQY3dGS3g3bWt0WmorT0FSRnkyUVR6aW9yV0Z6dU9nMEx0MkpFUlNSVkU1UzVhaGx4MG1ZRE9OQ0s0aGlaNW1EcjRiS3grOFR6Rk9La0JDcWk4cTh0UEdhZ0tGcHVLMW5XZk5nTTZNL013Tk9kSGN1SXhkVHN0c0VCYUVxZHd5dHZxVld3MW1WS202S0lJSmNnOWJhVi9WK0F6Y2giLCJtYWMiOiI0OGJhNDRlYjcxOWFjNWUyODA0YmU3Y2M2NzZiOTQ5MzRiOTFkZGY0MGY1YTQ4MjVjNzk1NWQ3ZmFjZjNlNzFmIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 00:19:09 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6IjRucFZGbFdvalZTS3d2VmFmUmZ6L1E9PSIsInZhbHVlIjoicVdTZXA5ai8vNFd2OXZ5bVhJWUd0ZG4wR3NRelp1cmJ3ZnArVFppbE9PVFdkQlFZV1QwQWp6d0ZJWSthTHpLQUFTWTdaaDBQLzROV2UrN0FaU1lCTWMrN1pwYnpGb2o0R0U2bFZPWWZsZmx3Um1VY05CKzZ4N2hQVlFkNTRyK09OZlJSb0E5Nm5EZkNWUG04M2Y3dERSRzIvSE5IRWUvb0dpcFFGZERTMkFHYmxWdnlvN0xoSE5Oa29sY2VlLzdFUkxqaE9Xa3VtUmpVcXlKSnRsL2llUi9UaCtkcVFSSUU5NXNTRWppMysvZEZldHdoeEdhWFBoendDcStKVndxUTZSZm5GVlBQUm1mZU9sUjd3d1IxdzJpNkUvdEdlWStQYVRWRHJRUHRHelJOQm1hU2NsY0JoRjkxQjN6dmJYeGNiSVFHOVg4NEJzZjEvRWtNWXhRYTlUbFQ2cmdnMk96RjVYeEVXbXdZeHM5RDhiVXpPVWVaaGVqK0ZYL3FJQ0lhYmUrR2IwUjI1c3VkNnR6UFM4b0pYVnBUSUlhbDNpVGR3TW9WSGtZR3BWUDlUekwzcWJOSjNNREw4SXhKNFFIMEtYWnhUK0E0U3JSb2l1WFo4N1dLOW9scVB0eTNXMTVLRDgzRTZ2VUZSMDRFMWlrSnZlalNya3FMT0ZNeHZSNCsiLCJtYWMiOiIyNjgzZTM3OWVlNTc4NzJkNjUxYjQyMjQ4MTg3OWVhZTUyZGQ5YTY3MDRlY2EzMzgxMDY5MTg4MDE1ZjQyZmMwIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 00:19:09 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-435376118\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-733923532 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">cN6pwMHluqMWM9hLaRtOtRasnQF8pfN3ghxjsOfa</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"41 characters\">http://localhost/financial/productservice</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-733923532\", {\"maxDepth\":0})</script>\n"}}