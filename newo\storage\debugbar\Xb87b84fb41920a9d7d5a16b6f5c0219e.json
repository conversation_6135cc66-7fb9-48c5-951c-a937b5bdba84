{"__meta": {"id": "Xb87b84fb41920a9d7d5a16b6f5c0219e", "datetime": "2025-06-07 22:21:24", "utime": **********.703957, "method": "GET", "uri": "/add-to-cart/3/pos", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749334883.292767, "end": **********.70399, "duration": 1.4112229347229004, "duration_str": "1.41s", "measures": [{"label": "Booting", "start": 1749334883.292767, "relative_start": 0, "end": **********.415904, "relative_end": **********.415904, "duration": 1.1231369972229004, "duration_str": "1.12s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.415927, "relative_start": 1.1231598854064941, "end": **********.703994, "relative_end": 4.0531158447265625e-06, "duration": 0.288067102432251, "duration_str": "288ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 53610712, "peak_usage_str": "51MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET add-to-cart/{id}/{session}", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\ProductServiceController@addToCart", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FProductServiceController.php&line=1236\" onclick=\"\">app/Http/Controllers/ProductServiceController.php:1236-1493</a>"}, "queries": {"nb_statements": 6, "nb_failed_statements": 0, "accumulated_duration": 0.012960000000000001, "accumulated_duration_str": "12.96ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 16 limit 1", "type": "query", "params": [], "bindings": ["16"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.537309, "duration": 0.00552, "duration_str": "5.52ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 42.593}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.568875, "duration": 0.00121, "duration_str": "1.21ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 42.593, "width_percent": 9.336}, {"sql": "select `permissions`.*, `model_has_permissions`.`model_id` as `pivot_model_id`, `model_has_permissions`.`permission_id` as `pivot_permission_id`, `model_has_permissions`.`model_type` as `pivot_model_type` from `permissions` inner join `model_has_permissions` on `permissions`.`id` = `model_has_permissions`.`permission_id` where `model_has_permissions`.`model_id` = 16 and `model_has_permissions`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["16", "App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 326}, {"index": 21, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 566}], "start": **********.617561, "duration": 0.00158, "duration_str": "1.58ms", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:326", "source": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php:326", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=326", "ajax": false, "filename": "HasPermissions.php", "line": "326"}, "connection": "ty", "start_percent": 51.929, "width_percent": 12.191}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (16) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 312}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 26, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}], "start": **********.6250138, "duration": 0.00164, "duration_str": "1.64ms", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 64.12, "width_percent": 12.654}, {"sql": "select * from `product_services` where `product_services`.`id` = '3' limit 1", "type": "query", "params": [], "bindings": ["3"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/ProductServiceController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\ProductServiceController.php", "line": 1240}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.64094, "duration": 0.00131, "duration_str": "1.31ms", "memory": 0, "memory_str": null, "filename": "ProductServiceController.php:1240", "source": "app/Http/Controllers/ProductServiceController.php:1240", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FProductServiceController.php&line=1240", "ajax": false, "filename": "ProductServiceController.php", "line": "1240"}, "connection": "ty", "start_percent": 76.775, "width_percent": 10.108}, {"sql": "select sum(`quantity`) as aggregate from `warehouse_products` where `product_id` = 3 and exists (select * from `warehouses` where `warehouse_products`.`warehouse_id` = `warehouses`.`id` and `created_by` = 15)", "type": "query", "params": [], "bindings": ["3", "15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/ProductService.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\ProductService.php", "line": 155}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/ProductServiceController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\ProductServiceController.php", "line": 1244}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.654961, "duration": 0.0017, "duration_str": "1.7ms", "memory": 0, "memory_str": null, "filename": "ProductService.php:155", "source": "app/Models/ProductService.php:155", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FProductService.php&line=155", "ajax": false, "filename": "ProductService.php", "line": "155"}, "connection": "ty", "start_percent": 86.883, "width_percent": 13.117}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}, "App\\Models\\ProductService": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FProductService.php&line=1", "ajax": false, "filename": "ProductService.php", "line": "?"}}}, "count": 3, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 1, "messages": [{"message": "[\n  ability => manage product & service,\n  result => true,\n  user => 16,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-2081419397 data-indent-pad=\"  \"><span class=sf-dump-note>manage product & service</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"24 characters\">manage product &amp; service</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>16</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2081419397\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.638266, "xdebug_link": null}]}, "session": {"_token": "iD3eOztlFQTk9Wu93brk2PH5kNLllwdWSLzbHLjf", "_previous": "array:1 [\n  \"url\" => \"http://localhost/hrm-dashboard\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "16", "pos": "array:1 [\n  3 => array:9 [\n    \"name\" => \"حمد\"\n    \"quantity\" => 1\n    \"price\" => \"10.00\"\n    \"id\" => \"3\"\n    \"tax\" => 0\n    \"subtotal\" => 10.0\n    \"originalquantity\" => 19\n    \"product_tax\" => \"-\"\n    \"product_tax_id\" => 0\n  ]\n]"}, "request": {"path_info": "/add-to-cart/3/pos", "status_code": "<pre class=sf-dump id=sf-dump-1885660345 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1885660345\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-1205172216 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1205172216\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-47579412 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-47579412\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">iD3eOztlFQTk9Wu93brk2PH5kNLllwdWSLzbHLjf</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1938 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=17ceeen%7C2%7Cfwk%7C0%7C1960; _clsk=1yftgw7%7C1749334872942%7C5%7C1%7Ck.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IndvUy9pQkdYR1dmZ0psWDVpSUkwMXc9PSIsInZhbHVlIjoiYmxITUgzVFBxNndhUGpWeHQzUVV6Z0d3Qmp4b0ZISXZIYmR1a2FRUzZpS1NJQzZYWHpJRlF4WlRlK0tCeUVWQkUrOE9ycGtHL0FGY2hSMjZRQ0lUYjlJZkwyZUdUNFZqUXVzaVlmVks5cjFvTUJqOThHQWd0Zjh6bG1PNjlsQWdKWVJUM1FrdmJVK2U4ZXhMbWl2M2d4Q1k2UFFaZDYwSXh6WWNLVVZZSnVXdHdkeHdpSCtuZFRKdmVtdE43L0lVeWQybGV5Z05KN3BRZEdQaFFKMDhyZGxnby9SN0tuWkgrcEZYc3hjR0d6elJ5cWFGcVc1ZTBHTWpZWEl6d1puKzFrV21JSTRaOXl3MThoR2lLYzI0cTVmSnExOXdGZ2tjc3pXVE1KYzQ1Z3FLVEgzTXdsdEs0TnVEUnNSV2hLMmEzdzRxM0I0UWxFcFpvUXA2alB2RUh0aUtUVlpCRWlMZ1RITWVlWnhRNDltWTFiU0VjM2ZUOWZIVTR6THRGbVhFamVVVldtRzNjT0dubDNsc281VTNxdVp1Ui9ZSTEvbGZnZnlwVEJPT29qaFAyQVFIaG1KeFBPcWhkN3g0bWx6MzZCZHdLd0VQWWs4SWo4Z0sxMnBPdTAzTWFmRzJleXRmZ3R3c1dRNll2bWMvVXZ2VGl2NUpKS29sNFBoRHFMbUMiLCJtYWMiOiIzNmY0NWM5MDNiOGFmZTU4NzQ3ODlmZWFkOTViMjg1NjQ2OTNkMGNiOGMyOWJlMDcyYjZiMTA2MTU5ZDE0ZGM0IiwidGFnIjoiIn0%3D; test_session=eyJpdiI6ImNxOEtUOFV3YmZmc3ZHRVB5Rml5QUE9PSIsInZhbHVlIjoiWFQ1dGFMTmFNUzV4emk3SFhUenAwaEVubjIzcGFXZThVZ0lNcVRpVkZmVHgxNXBWVHNLNmVvQW95RHhwWkMwZk5CVGphbXJaMlFTanVoMmVNOTZIeTdBdGdxS3RqU0NodmhPNTdjSUxmVkRrRldScS80RXZ0MUJXV3U1Qit4d2ZLa3F6cGJHQmNXRzdkSGIvUUFCaktKQWFlSjhjbFhQRGFiVFhtL0cxQ2wzUGp4NDdkUUZHTjBwRE1UOFg2ZkJ6MnpCYTdOT3JoRGpDUnZZOEU5Y3gyUVNuZ3VpSG56aFdKZFp2ZnVrVmdST2NCUS9ndytQYlBETFMvSEQyVmtIMmVhTVh3cDBaQk04amlGWmhTcEFNWlBidjA0dmxSanlCSG1zcVZuTTZWWVlVZFhhUE1RbVR3M2w3K0trVWVTRjd3RE9zUk9Qb2hWSHhyT3lBV1NlZFlLY2xtTXlBeUUyeGcrYTVMOXNoQmtGV1F3Rm5SaEQzazJxQndyK0h1dzNNQzVkYUQ5OUsrZ0RnWmpIZnBUbGJKQ1BtM1BGamt1aCtuOXc5V0RVb1BQN09VcW5tQU5lZDNjOUUybUNGaC91eVMydHowK2MrREFrVzZIais3WTZjczFDTEFONVhJUnZQNDNKWXp4aEdDV3lyaFI2V1pqYjhzQjdQWDk4ZzdoN0oiLCJtYWMiOiI3MDUzOTUyMmFhOTBjYWVkYmNmNWFkNzdhMDNkYjgxY2NiODcwZTRiMjA0ODgwZjY1YjYwYTUzZGVjMDEwYzczIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1774652747 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">iD3eOztlFQTk9Wu93brk2PH5kNLllwdWSLzbHLjf</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">atMfj5qpj5gKx4OQYPP5PbQzRbuF7iB8X4zv4aOY</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1774652747\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1584671086 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 07 Jun 2025 22:21:24 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Ii9Uc2h5blphcU9kcnBEQ1I0aXAyNmc9PSIsInZhbHVlIjoiMStyU2ZNL2pMc2hURUIrWmRoMFUyd0IxRGdlVEpDWFFURTZIVlJXRUpHakRDdjc0djdrdXFXdjAxd0JQcEhVMHNuN3ZIQ0xRbndWNXJkWnpJSXZuMTlseS9Wa3ZUSkxWK05MWnUyZC9kcm8zbmdFK1ZFdFdUZkRsY1NYTDBQaURPWGJNQ0IxZUF1aFZSZlVnOGplMEtXVXAzTkpSMGpra0lpVS83UUVGUy8zNkMyL0Y3alN5a1JEWjU0dGs4V3JqakxXYWxadmZTV1lIWDFocUFRRmhtczE1S3h5UXBmZHVWSWpVeHBnN1R0Rk9PR0ZUWUpwRld1RUI3cmQ3cy9wT2lwYitPQk8yTmJDbnE5T0ZpR0FrK3dNblN2WVN4VVlRMXk0Q3lWQ0w3VUU5cU9PZGlJcC9HV3F3bktxZGY3UnBZZmc2akNoUnpuTE0ySjg2KzVQdEFIc05TLzBxVHhzRi9sTFZZM1Y2eHpwVER4cXhrOTVLaVNRN1RmK3NIQk9QUHVUU1JOZXJGSnpHN1ptN3pOb1E0aHZqWVV4cGZNYWw3b09QSnVZa2U1RHlNZ3RNYlltRXpiRDVzSWNQVzI0R0xxOGRKL2xJUEpqZUhMU0xIWXI2N0dkTjRndUVSMXZkVWlQeW5xbWJNK0ZDYlo5SXVySElMYmtNeTdSS1NjQTUiLCJtYWMiOiIwNTNkNGNlMzhmM2NjNzQ1ZDEyMzc5OTI2YmMwY2ZjYjIxN2EzOTQ2NWNiY2ZhMjk2NjdkNzk5ODE4Yzk2NzliIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 00:21:24 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6Ikt3cWgxVG4yZjdvQmh1WmNwci9zQ1E9PSIsInZhbHVlIjoiakl4cTNVdTdCQTYyUDdOTHN2QVpncDZDbitQb2srRUkxbG1OMUVWQ0tTNURGTUZKcFRvalhVdXFiL0JMdi9JN2ZvckExUVFTQXBlOUllTVFFMVN3VzlseHVBejBMeURQYjBjTTdQZ0JBdHNPcmlFVm1uRGtSOXg5aFhnVGlwQkZaeWhQb3VRaGdxZGhFaTRzR3BGSnE4blZxemdyVEwwalVRaHQyZFBOWjREY1g0ZTBSRGlrUGlYdUVmeUpxVXJ3ejY0THJQZFpvQitvbVRXY0daMDJ3WW0xRHRITlVoaHRHanNyVkRweEJ6eDdXL3R1enRaakFpTkwrMjlaNnBsM2xXZjBHaER0OUZxLzhRYzY5U2tiZ3lDcm8zSEpFWCtIdGVrZzRaOHBvMzd4azJtalRLVk9ibHhxTy9PRTBQcnIzdnErZSt2VXNqMTFsWThrYTEwT3BnUVdYNWpzUEpGWE8rcXNxWVpHaUl3bzgrWjZLa3AvbFJsV0xnZlkzNUZIZW1SN1NGS2VhVUhYa3VtM3JIRmlnNzVYTUJSU0RkUHQxVy9lbnplSlJ3S2pseUN2SUlHTkdvYUk3VzdqZUQrYTFuTUlHMm1yejFXak9MbUszYmJOVGZmL2xCaER1bjR1Mkl5N0ltVld0bkhvSkppeXl3VmcwdVpUQVl0L2k4TjIiLCJtYWMiOiIwOGE2YTM0NmIzYjc2YTM0YjA5Nzg3OWI2YmQzMzQwY2I0NDRjZGU5N2NkMjYyZTMzMmNlOTkyMjdhMWRkNWVmIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 00:21:24 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Ii9Uc2h5blphcU9kcnBEQ1I0aXAyNmc9PSIsInZhbHVlIjoiMStyU2ZNL2pMc2hURUIrWmRoMFUyd0IxRGdlVEpDWFFURTZIVlJXRUpHakRDdjc0djdrdXFXdjAxd0JQcEhVMHNuN3ZIQ0xRbndWNXJkWnpJSXZuMTlseS9Wa3ZUSkxWK05MWnUyZC9kcm8zbmdFK1ZFdFdUZkRsY1NYTDBQaURPWGJNQ0IxZUF1aFZSZlVnOGplMEtXVXAzTkpSMGpra0lpVS83UUVGUy8zNkMyL0Y3alN5a1JEWjU0dGs4V3JqakxXYWxadmZTV1lIWDFocUFRRmhtczE1S3h5UXBmZHVWSWpVeHBnN1R0Rk9PR0ZUWUpwRld1RUI3cmQ3cy9wT2lwYitPQk8yTmJDbnE5T0ZpR0FrK3dNblN2WVN4VVlRMXk0Q3lWQ0w3VUU5cU9PZGlJcC9HV3F3bktxZGY3UnBZZmc2akNoUnpuTE0ySjg2KzVQdEFIc05TLzBxVHhzRi9sTFZZM1Y2eHpwVER4cXhrOTVLaVNRN1RmK3NIQk9QUHVUU1JOZXJGSnpHN1ptN3pOb1E0aHZqWVV4cGZNYWw3b09QSnVZa2U1RHlNZ3RNYlltRXpiRDVzSWNQVzI0R0xxOGRKL2xJUEpqZUhMU0xIWXI2N0dkTjRndUVSMXZkVWlQeW5xbWJNK0ZDYlo5SXVySElMYmtNeTdSS1NjQTUiLCJtYWMiOiIwNTNkNGNlMzhmM2NjNzQ1ZDEyMzc5OTI2YmMwY2ZjYjIxN2EzOTQ2NWNiY2ZhMjk2NjdkNzk5ODE4Yzk2NzliIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 00:21:24 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6Ikt3cWgxVG4yZjdvQmh1WmNwci9zQ1E9PSIsInZhbHVlIjoiakl4cTNVdTdCQTYyUDdOTHN2QVpncDZDbitQb2srRUkxbG1OMUVWQ0tTNURGTUZKcFRvalhVdXFiL0JMdi9JN2ZvckExUVFTQXBlOUllTVFFMVN3VzlseHVBejBMeURQYjBjTTdQZ0JBdHNPcmlFVm1uRGtSOXg5aFhnVGlwQkZaeWhQb3VRaGdxZGhFaTRzR3BGSnE4blZxemdyVEwwalVRaHQyZFBOWjREY1g0ZTBSRGlrUGlYdUVmeUpxVXJ3ejY0THJQZFpvQitvbVRXY0daMDJ3WW0xRHRITlVoaHRHanNyVkRweEJ6eDdXL3R1enRaakFpTkwrMjlaNnBsM2xXZjBHaER0OUZxLzhRYzY5U2tiZ3lDcm8zSEpFWCtIdGVrZzRaOHBvMzd4azJtalRLVk9ibHhxTy9PRTBQcnIzdnErZSt2VXNqMTFsWThrYTEwT3BnUVdYNWpzUEpGWE8rcXNxWVpHaUl3bzgrWjZLa3AvbFJsV0xnZlkzNUZIZW1SN1NGS2VhVUhYa3VtM3JIRmlnNzVYTUJSU0RkUHQxVy9lbnplSlJ3S2pseUN2SUlHTkdvYUk3VzdqZUQrYTFuTUlHMm1yejFXak9MbUszYmJOVGZmL2xCaER1bjR1Mkl5N0ltVld0bkhvSkppeXl3VmcwdVpUQVl0L2k4TjIiLCJtYWMiOiIwOGE2YTM0NmIzYjc2YTM0YjA5Nzg3OWI2YmQzMzQwY2I0NDRjZGU5N2NkMjYyZTMzMmNlOTkyMjdhMWRkNWVmIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 00:21:24 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1584671086\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1891268873 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">iD3eOztlFQTk9Wu93brk2PH5kNLllwdWSLzbHLjf</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"30 characters\">http://localhost/hrm-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>16</span>\n  \"<span class=sf-dump-key>pos</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-key>3</span> => <span class=sf-dump-note>array:9</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"3 characters\">&#1581;&#1605;&#1583;</span>\"\n      \"<span class=sf-dump-key>quantity</span>\" => <span class=sf-dump-num>1</span>\n      \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"5 characters\">10.00</span>\"\n      \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str>3</span>\"\n      \"<span class=sf-dump-key>tax</span>\" => <span class=sf-dump-num>0</span>\n      \"<span class=sf-dump-key>subtotal</span>\" => <span class=sf-dump-num>10.0</span>\n      \"<span class=sf-dump-key>originalquantity</span>\" => <span class=sf-dump-num>19</span>\n      \"<span class=sf-dump-key>product_tax</span>\" => \"<span class=sf-dump-str>-</span>\"\n      \"<span class=sf-dump-key>product_tax_id</span>\" => <span class=sf-dump-num>0</span>\n    </samp>]\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1891268873\", {\"maxDepth\":0})</script>\n"}}