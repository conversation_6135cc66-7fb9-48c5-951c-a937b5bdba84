{"__meta": {"id": "X7696c738bd28c1194c60f44464463ff2", "datetime": "2025-06-07 22:21:19", "utime": **********.291721, "method": "GET", "uri": "/product-categories", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749334877.866187, "end": **********.291754, "duration": 1.4255669116973877, "duration_str": "1.43s", "measures": [{"label": "Booting", "start": 1749334877.866187, "relative_start": 0, "end": **********.023169, "relative_end": **********.023169, "duration": 1.1569819450378418, "duration_str": "1.16s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.023192, "relative_start": 1.1570048332214355, "end": **********.291757, "relative_end": 3.0994415283203125e-06, "duration": 0.26856517791748047, "duration_str": "269ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 48114896, "peak_usage_str": "46MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET product-categories", "middleware": "web, verified, auth, XSS, category.access", "controller": "App\\Http\\Controllers\\ProductServiceCategoryController@getProductCategories", "namespace": null, "prefix": "", "where": [], "as": "product.categories", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FProductServiceCategoryController.php&line=203\" onclick=\"\">app/Http/Controllers/ProductServiceCategoryController.php:203-229</a>"}, "queries": {"nb_statements": 6, "nb_failed_statements": 0, "accumulated_duration": 0.01121, "accumulated_duration_str": "11.21ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 16 limit 1", "type": "query", "params": [], "bindings": ["16"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.1453671, "duration": 0.00471, "duration_str": "4.71ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 42.016}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.181642, "duration": 0.00117, "duration_str": "1.17ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 42.016, "width_percent": 10.437}, {"sql": "select `permissions`.*, `model_has_permissions`.`model_id` as `pivot_model_id`, `model_has_permissions`.`permission_id` as `pivot_permission_id`, `model_has_permissions`.`model_type` as `pivot_model_type` from `permissions` inner join `model_has_permissions` on `permissions`.`id` = `model_has_permissions`.`permission_id` where `model_has_permissions`.`model_id` = 16 and `model_has_permissions`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["16", "App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 326}, {"index": 21, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 566}], "start": **********.234036, "duration": 0.00121, "duration_str": "1.21ms", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:326", "source": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php:326", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=326", "ajax": false, "filename": "HasPermissions.php", "line": "326"}, "connection": "ty", "start_percent": 52.453, "width_percent": 10.794}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (16) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 312}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 26, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}], "start": **********.2412138, "duration": 0.00114, "duration_str": "1.14ms", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 63.247, "width_percent": 10.169}, {"sql": "select `product_service_categories`.*, COUNT(pu.category_id) product_services from `product_service_categories` left join `product_services` as `pu` on `product_service_categories`.`id` = `pu`.`category_id` where `product_service_categories`.`created_by` = 15 and `product_service_categories`.`type` = 'product & service' group by `product_service_categories`.`id` order by `product_service_categories`.`id` desc", "type": "query", "params": [], "bindings": ["15", "product &amp; service"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/ProductServiceCategory.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\ProductServiceCategory.php", "line": 116}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/ProductServiceCategoryController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\ProductServiceCategoryController.php", "line": 206}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.2564108, "duration": 0.00197, "duration_str": "1.97ms", "memory": 0, "memory_str": null, "filename": "ProductServiceCategory.php:116", "source": "app/Models/ProductServiceCategory.php:116", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FProductServiceCategory.php&line=116", "ajax": false, "filename": "ProductServiceCategory.php", "line": "116"}, "connection": "ty", "start_percent": 73.417, "width_percent": 17.574}, {"sql": "select count(*) as aggregate from `product_services` left join `product_service_categories` as `c` on `c`.`id` = `product_services`.`category_id` where `product_services`.`type` = 'product' and `product_services`.`created_by` = 15", "type": "query", "params": [], "bindings": ["product", "15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/ProductServiceCategoryController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\ProductServiceCategoryController.php", "line": 209}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.267025, "duration": 0.00101, "duration_str": "1.01ms", "memory": 0, "memory_str": null, "filename": "ProductServiceCategoryController.php:209", "source": "app/Http/Controllers/ProductServiceCategoryController.php:209", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FProductServiceCategoryController.php&line=209", "ajax": false, "filename": "ProductServiceCategoryController.php", "line": "209"}, "connection": "ty", "start_percent": 90.99, "width_percent": 9.01}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}, "App\\Models\\ProductServiceCategory": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FProductServiceCategory.php&line=1", "ajax": false, "filename": "ProductServiceCategory.php", "line": "?"}}}, "count": 3, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 1, "messages": [{"message": "[ability => create purchase, result => true, user => 16, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1437551161 data-indent-pad=\"  \"><span class=sf-dump-note>create purchase</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"15 characters\">create purchase</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>16</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1437551161\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.2532, "xdebug_link": null}]}, "session": {"_token": "iD3eOztlFQTk9Wu93brk2PH5kNLllwdWSLzbHLjf", "_previous": "array:1 [\n  \"url\" => \"http://localhost/hrm-dashboard\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "16"}, "request": {"path_info": "/product-categories", "status_code": "<pre class=sf-dump id=sf-dump-31224242 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-31224242\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-256536996 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-256536996\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-89759766 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-89759766\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1915420186 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">iD3eOztlFQTk9Wu93brk2PH5kNLllwdWSLzbHLjf</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1938 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=17ceeen%7C2%7Cfwk%7C0%7C1960; _clsk=1yftgw7%7C1749334872942%7C5%7C1%7Ck.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IlNBcDMyV2plU2dyZDBxNnpLVUt0TXc9PSIsInZhbHVlIjoid0gxT01ycFlJa0UvZGFiQ0toWC9uVFRWTjd1NDNnaDdDcEgwRVg2UmxycGJWdThXdDB1L2VuOFVaUnBPTGJCNFZYaUFVRVZaMklGTnlxYk9iRDhpeDMwT3JyMFhZcGErV00xdlhXK1EvRFBCR3ZYUE1URTVCd3BMUnNXNVRMV3RqdjZmMjhoWTBYWWo5ZGVLeDRldDBSc1ljamcrYjFTWjQ0ZzUyRXBhRGpuUzBSUUt2bEVpS3FlWHlyOFo5cTJaUHR1NXU1TisxcUNmVGVQUkFzcytQOTU4M0U4Z0ZCbHZ2VjduZVNYOCtiTlhQc0FwcFdSUTBVb0VxK2xYc0VsY3M2OFNVTDAwTjE4NjJyYTd2TW96OWozeERMcVdxMVdTcE9Nd0NqWU5ublpwdGV6WldQTE1xVDJHOXFkVnlFTVlMR0J3QkZJMHg4Y0VxY1c5cVozZE9VTWpzZms3c2Q3YU9mSzF0VmYxbHVPQXNISzl3ZTROeEo5N3ExYlBLc0dId084MDl1TU5Jb2UrZm9ZczNQRE1EZ1dIM0RoNm50TFR5cXE4K2Vtd2k3UWxjK1BpZFdQTG9NbXJPVEVvVVYxU2I0UGcyOHhCMDgyWnNDQ1lOZ0xwT1lCcGgzQmg5YlZaNy9ZdWMwN2c1Z1hwb3BQOURRQktjNjRmb0pqVG5LM3MiLCJtYWMiOiIxODllODUwZjQ4NTk2Y2EyYmNjOTEyYjNmZDdhOTEyNjk0NjRhYWE5NGVjMTk3NjM5NDY3MmFjNmFhNjZmMWZjIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6Ikw2NDhsREF3ZC9pekl6ay9DRWRLYUE9PSIsInZhbHVlIjoiVjRGZVoyQWQxV3Z0UWZMZFAyZkZtbDZNckY0TjNpL1JIczJDZXJjRFhrd0x6cHBPY0Z6NmFKWXhDNG9MeVU4Tm1SQlgrVGcwWDVjeWhTZGVFYnplOEpTUmVHYWs0SkUxSHVPSStRTnhEWVN5VU5yL2haaFBvZVNYYXdpVW5vQTkrYXdrU2xvOW1JbkhSQjh3c0ZSZE1OWENhME54VThrbmw2L3ZsYTU2UkRtTlp0RFJWcmYveHpjbjNUMmNKS3plWkcxeS8vYlRleUNlZnV4aWdiak1IM2VQNVdwOExzd0Y0T2Q4SXQvYzUvQWorSGI1OWExOGdSL3pLSjJNeUVXelppSkhkNnEwN0MxUUtSWnNNU0R1N05QS0JmR05RaHR5NVJ5cFROWFExUitnbmduNEtFdFBndXpDbHAzT1hubDNLZUVUQXo2UUE1anUzZDdXT1VtR3E5d0RpTlJleEdlejhLemtZdFY1VWJlV2xkVjkySitEMFBCQTFLanc0YjlQZEpvQk1vSyt5aEM3WUpvVTZOVmdMK25lVUVIa3d1OTgzcSsvUzNaTTdzODRTSnpxTHpXSXM5eHJ5MlB6RUVIMEhhU2NCeXZ3RFUvQTY2ZTVUVndZcTJ2d0ZtbUwyTnFRTWhsU1FJZ1Q1aVB6bHlTNlcrbHFPTWJDZ1NqSEFWRkQiLCJtYWMiOiJjMmQ4ZDUzZGM4NDJlZGZhYWM5MjczY2UwOThmNDQ5YTczMDBiMjJjMTI2NTM5MDljNTNkZDA1YTIzYzNhODdhIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1915420186\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-895460722 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">iD3eOztlFQTk9Wu93brk2PH5kNLllwdWSLzbHLjf</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">atMfj5qpj5gKx4OQYPP5PbQzRbuF7iB8X4zv4aOY</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-895460722\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-334713725 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 07 Jun 2025 22:21:19 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Ik9pQkVkMUJZMlhqNE92K1Z0QWJrTkE9PSIsInZhbHVlIjoiR1lieFI2ZDRwb0hpZ3doRWZkOHdlSVBkOXNBZUJ3UUViTTJleTZqb0RWaUlwVUtSd3Jkby95YVl4bEZUYms1bDBOM0xWYWVaQ2U0SHlHL0dnRjZCRHpSWnlkY2NURDlGdjRIZUh6aFhORWJlVDBpNHovSVZUQmNLb2NwU3J5WFVtenRacUQwY0ZzTG5zY2R4bUdDSUdQZ3lyUnNFZGMvL2hqYjNscGRzblRKS3gvTzdhakdnK1NBaFJzN2lPTzRvdExVcldDbThDUThTeWs5VDlkank1NEZSb0IwaFFlZmNMOHlTRlFkQ21aSDhvWkxoZHhQRHloWnBoaGR3b2FDRFNmMzVDSytqeDQvV08raWd3VWkyUEFHSitpaGJjU2VFbTlPMmw5aXR2cjgyTDhzMmZxWFdsVFNDV1pxYllVNnZPN29jaG53eXdySy84Sm9jN0FzS1ZPcWFBZzJtZitPQTR6TE1LTjBwdkU4cFhXV3JZUmp5cEphU0c5cHQxNGdVQkJkYkhHSEJUTnQrWU1XSG42UWQ1SjQzMnhQNkRBWXhkZFhsVlYzd3Vpc0dZZldQSklEYzNRWlRHaFdjV0lyZmhCcFpxZnVyNGNndkJWQnBZWlNDVkpzc2N3bVl3aEdmMnhWSVZialZpOVVLY3g0VW12Z28rSjhtSVYrUnVYdVciLCJtYWMiOiIyZTRjM2RhMDFmZjU3ZGIzZWRkMjQwOWEyNTI4NDAxZTBmN2I4NGU2MGNhMTQ0MzU2NTMzYTlmZjA0MTkzYmQ2IiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 00:21:19 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6Im5uZE50bTFZb1phcFZwaFBTRDB5M0E9PSIsInZhbHVlIjoiaC9IRjN2K2xtMkV6NkgzSUhFTE5EcFdGMXBBT1lSWjRQQ084OFhvUU5RUGdLdEFUUlpUYUFyVDAvT1Q2OGdMY05mOWFxeXRKUTdNdnFITDRlUGdmOU0vam4zdkdIck8xdVdOVW5EOXREY0RnR2kzajVONUd6Rm1JOExyQ2w4bkJqQkRPUHV0c2F5eHlncmdhVnVLbGxMK0xQbWd0RTlXNG9wdEhpYmt5OC9INnQvMGgvVXQ3MTRDOFR4OFFSVWYrWVRsMjZsakREekZkbHJpVDhSbjdCV2h1YSswVnRzUHozOHFGc0JZcmx1TlV1QzBaNlBoZnVaT29WRFVmcWhLUzRPVDlvdDc0T0ZQQlZtbTgzS21QZHpTcFJ1dlVyeWlMcEJCVGxxYnlrOTEwWHlQbXdQKy9lazlNMU9NaFJmQURBTVVITm9jUG5vMVE2dW1OZHFMd3pJMWw2UlFvU2o5MGhEM0xnZ1dRZ0hMamJCbEwzK3hsUFNoM3pmczBaYTRPZTBOemthMys0d084VmlwMWRieHlWM2FzTWcyWHF3VHYxWVVYcTd4NXNic2JWcVl1VnRGS3NoUWJ0TjFVdUZQeFRuYTF1VVc1RklEMXROREVmWjV3bnhMcFFGc05JbDVIMWNMV1dvLytMUzBuYmZYdUx5QTFOVGVBU0U1ayt3aU4iLCJtYWMiOiJiMzNkYzc4YTAwMGIzYTAzYWU4YTAzOGU4MWVjZWU2NzYzZmFhZjNkNWVhN2ZiMTA1N2Q5OTk2ZmM5MjVjNDI2IiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 00:21:19 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Ik9pQkVkMUJZMlhqNE92K1Z0QWJrTkE9PSIsInZhbHVlIjoiR1lieFI2ZDRwb0hpZ3doRWZkOHdlSVBkOXNBZUJ3UUViTTJleTZqb0RWaUlwVUtSd3Jkby95YVl4bEZUYms1bDBOM0xWYWVaQ2U0SHlHL0dnRjZCRHpSWnlkY2NURDlGdjRIZUh6aFhORWJlVDBpNHovSVZUQmNLb2NwU3J5WFVtenRacUQwY0ZzTG5zY2R4bUdDSUdQZ3lyUnNFZGMvL2hqYjNscGRzblRKS3gvTzdhakdnK1NBaFJzN2lPTzRvdExVcldDbThDUThTeWs5VDlkank1NEZSb0IwaFFlZmNMOHlTRlFkQ21aSDhvWkxoZHhQRHloWnBoaGR3b2FDRFNmMzVDSytqeDQvV08raWd3VWkyUEFHSitpaGJjU2VFbTlPMmw5aXR2cjgyTDhzMmZxWFdsVFNDV1pxYllVNnZPN29jaG53eXdySy84Sm9jN0FzS1ZPcWFBZzJtZitPQTR6TE1LTjBwdkU4cFhXV3JZUmp5cEphU0c5cHQxNGdVQkJkYkhHSEJUTnQrWU1XSG42UWQ1SjQzMnhQNkRBWXhkZFhsVlYzd3Vpc0dZZldQSklEYzNRWlRHaFdjV0lyZmhCcFpxZnVyNGNndkJWQnBZWlNDVkpzc2N3bVl3aEdmMnhWSVZialZpOVVLY3g0VW12Z28rSjhtSVYrUnVYdVciLCJtYWMiOiIyZTRjM2RhMDFmZjU3ZGIzZWRkMjQwOWEyNTI4NDAxZTBmN2I4NGU2MGNhMTQ0MzU2NTMzYTlmZjA0MTkzYmQ2IiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 00:21:19 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6Im5uZE50bTFZb1phcFZwaFBTRDB5M0E9PSIsInZhbHVlIjoiaC9IRjN2K2xtMkV6NkgzSUhFTE5EcFdGMXBBT1lSWjRQQ084OFhvUU5RUGdLdEFUUlpUYUFyVDAvT1Q2OGdMY05mOWFxeXRKUTdNdnFITDRlUGdmOU0vam4zdkdIck8xdVdOVW5EOXREY0RnR2kzajVONUd6Rm1JOExyQ2w4bkJqQkRPUHV0c2F5eHlncmdhVnVLbGxMK0xQbWd0RTlXNG9wdEhpYmt5OC9INnQvMGgvVXQ3MTRDOFR4OFFSVWYrWVRsMjZsakREekZkbHJpVDhSbjdCV2h1YSswVnRzUHozOHFGc0JZcmx1TlV1QzBaNlBoZnVaT29WRFVmcWhLUzRPVDlvdDc0T0ZQQlZtbTgzS21QZHpTcFJ1dlVyeWlMcEJCVGxxYnlrOTEwWHlQbXdQKy9lazlNMU9NaFJmQURBTVVITm9jUG5vMVE2dW1OZHFMd3pJMWw2UlFvU2o5MGhEM0xnZ1dRZ0hMamJCbEwzK3hsUFNoM3pmczBaYTRPZTBOemthMys0d084VmlwMWRieHlWM2FzTWcyWHF3VHYxWVVYcTd4NXNic2JWcVl1VnRGS3NoUWJ0TjFVdUZQeFRuYTF1VVc1RklEMXROREVmWjV3bnhMcFFGc05JbDVIMWNMV1dvLytMUzBuYmZYdUx5QTFOVGVBU0U1ayt3aU4iLCJtYWMiOiJiMzNkYzc4YTAwMGIzYTAzYWU4YTAzOGU4MWVjZWU2NzYzZmFhZjNkNWVhN2ZiMTA1N2Q5OTk2ZmM5MjVjNDI2IiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 00:21:19 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-334713725\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-17442790 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">iD3eOztlFQTk9Wu93brk2PH5kNLllwdWSLzbHLjf</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"30 characters\">http://localhost/hrm-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>16</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-17442790\", {\"maxDepth\":0})</script>\n"}}