{"__meta": {"id": "X271ff8743281a7b13e78ba5a02903d07", "datetime": "2025-06-07 22:18:21", "utime": **********.986664, "method": "GET", "uri": "/users/15/login-with-company", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749334700.532175, "end": **********.986704, "duration": 1.454529047012329, "duration_str": "1.45s", "measures": [{"label": "Booting", "start": 1749334700.532175, "relative_start": 0, "end": **********.837227, "relative_end": **********.837227, "duration": 1.3050520420074463, "duration_str": "1.31s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.83725, "relative_start": 1.30507493019104, "end": **********.986708, "relative_end": 3.814697265625e-06, "duration": 0.1494579315185547, "duration_str": "149ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 43786456, "peak_usage_str": "42MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET users/{id}/login-with-company", "middleware": "web, auth", "controller": "App\\Http\\Controllers\\UserController@LoginWithCompany", "namespace": null, "prefix": "", "where": [], "as": "login.with.company", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FUserController.php&line=660\" onclick=\"\">app/Http/Controllers/UserController.php:660-667</a>"}, "queries": {"nb_statements": 2, "nb_failed_statements": 0, "accumulated_duration": 0.00658, "accumulated_duration_str": "6.58ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.932495, "duration": 0.00539, "duration_str": "5.39ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 81.915}, {"sql": "select * from `users` where `users`.`id` = '15' limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/UserController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\UserController.php", "line": 662}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.952907, "duration": 0.0011899999999999999, "duration_str": "1.19ms", "memory": 0, "memory_str": null, "filename": "UserController.php:662", "source": "app/Http/Controllers/UserController.php:662", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FUserController.php&line=662", "ajax": false, "filename": "UserController.php", "line": "662"}, "connection": "ty", "start_percent": 81.915, "width_percent": 18.085}]}, "models": {"data": {"App\\Models\\User": {"value": 2, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "cN6pwMHluqMWM9hLaRtOtRasnQF8pfN3ghxjsOfa", "_previous": "array:1 [\n  \"url\" => \"http://localhost/users/15/login-with-company\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/users/15/login-with-company", "status_code": "<pre class=sf-dump id=sf-dump-1395508764 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1395508764\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1608206141 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1608206141\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1924105558 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1924105558\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/users</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1993 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=a091af98-270d-406e-ab7e-33a6e610d6e2414686; _clck=nups8l%7C2%7Cfwk%7C0%7C1960; _clsk=ij6lzq%7C1749334697734%7C4%7C1%7Ck.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6InovcmdBOURtVmtra1E3SytuekJ5Umc9PSIsInZhbHVlIjoieUlkNFBZbzgxM1NISTRGT2pjWGJxc09VMXJBVjhXOVhtNUpXSFBVWS9oNk92eDFiT3Y2SW1iUndpNjhKVUtHM1RjMHNPblZac0J3V2dhdVJsWTlvTExCNHZBdmNzcTJIdjgvbm1ia3BGMnBGWEpxeThTNk1ETzFqMk9lMlZxOURPcGdJRWFpNHpNdXNzTCtmazU5M0tMWWZUclIzcTdScDFzZDh1MzNsd2VIZTBjck1KVTJEYVdkYTNWWlI2b1NrNGhHSTE2akFxOHZXUWh4bnBNdEVnNy90QjhodE9yd2xIaElzWE1HU01rUlkxUkFmN1NUVi81Rjc4NEovZlJPTU14QzNjR0o2bXJ4TmZvTlljNndyVC9Vdy9haXBNSFM4L0laaEZCQVBvVU4rOTViSnVkTnhWZ1pDUnR2YXp0anoxUXlQNkhhYWFVRmw2dTVnMU0rZnRscUpuVEUvSks0V2ptY1dQUVN4ZGhaYURTK01wYmQ5QjNhN1BlMit2dm1oUVdwOVVCOFFuS3o1WS9oZUpTNkxpQXFEMmxHcGVCY28xVFFiSHpPcVdGdmhYZDRXUzJLR01qa2hwa3FCSzFnR0FWN3VUdTNMN2QvWHdJUGwzeURON3JyTUlGbkhvZHlRd1Z5M2x1cFlrTys4bEhDckxGQ2lKNjd3MHppM3lBYVEiLCJtYWMiOiJhZTEzYzk2NGEzZDI0M2M2NmYzMGU3YzhlMGIyYTU4ZTI3NDEzZjNmZThkZTgyNmNlY2MwYjYzMzg0Mzk1NzE4IiwidGFnIjoiIn0%3D; test_session=eyJpdiI6IlVJb0YvMDlLSnZadXdWVEJFazBhMXc9PSIsInZhbHVlIjoidk5leTNnT2xBd0tUdjlBRHdwL1loTzVIUjlhdlllaEpLRUx2K2xZU2tQSTBWZSsrYm14VGFqYURsek16NmxneGp2ckRDOTYxVjNmbDlRTzAyd09MdHdFTk4vbktzVUwvZ1V4b2lUMWJYalJKeGpMTU9UeEE1a2wvK3FZQVdvM0hvN3dlMTZDbTJMOGJnUThxWlVqU1B0d25MR2swUjQ0YXhkMWdPdjFRVjhLdnoybGpmK2xlamlkMC9Oakl6N1A0YTh5cWZZUVBVRjVMdzFpQlpaS1NUS212V2w0ZDBFdWJOOVJKbUx5MnF6RDdNWjdUaEdCbThoc3ViRjR5M01CUHhkR29QOE1sSXNWbzFpd010Y2dZM3JTaHRwWWFyQStwTngwVCs1V01HMWxZSVVuak12UGNnRm5PUnNucUVqL3hTQ1pTbEJhMCt6Z3F6OVlhUjFWeEZXbUU5aFBkTmtzU0I4M1lxcllHWE1YVlM2SklUMmVMRVRyTWxnUTJlMCtYWFF6YTVsNVpUdFBRTDhNWXFEQlpWdGRKMCtNRmxRSFZyZGlhRnE5blpWWitPMmtJMjNhdWhMTHE1dkcvcmo3VnBnT2JzeXFhRXF6V09EL2JBOXA0SkovMnFSSUhDK2t1Ukg0U3lac245TlFJSWdrMFNNdTZqNzNsWGZmN1daYlIiLCJtYWMiOiI5NzU2NGZiMGZjMmI5YzgxMjBmMjg0YjMwMTU2ZTUyMTkyNGRhNDdiZjc4ZmZmOTlhMjgzMTY4ZTlmZWM1ZDk3IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1974391939 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">cN6pwMHluqMWM9hLaRtOtRasnQF8pfN3ghxjsOfa</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">PmFRgUqysTWH0qB2ROlhITKoLIETZGW2KykVXtOx</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1974391939\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-56720714 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 07 Jun 2025 22:18:21 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IjNLMnRPa1pwK1l4WTA1ejRhUlh3c1E9PSIsInZhbHVlIjoiNDRLckdpUVhCWDQ1Mnllams2cmZMc1pMQlRKMjVsQzNtVExvOEkyWnhuWXk3eXVlelJ4SGJxZ1dWQ0d0QjBFWGlEck04THJvS3hoTjVzYWNSMjJPeTJSOEl0VFp1L0U1bGNhWEQrVHRNdVVQRG5GQ2pGVmtkc1o3NmZTbzNwb3g4UmRuTEN1enNXeEp6OFJ2VkpOZldiZnJCaFZBZHVablRkS1F2QU9NT0QrbStVZjFPYWxnV3poUmU2RjRHT3o1V2lrTHlSaDMvS1Y0SVV2SkZNVmxITFVzeW16bExOT2V1MVRGNEVVVU9sQlJ0UklzOEV6dTdZeUdzN1IxMGhaNURCclJoeElPaXc0MWNCSFZWaVBpUkdLdm5DL0k5SDdEdE9ITnVRVHBlN3lCNGl6UzdSVFZ0TGxKdXZkOGJJTDNEQXJ3QThFN2FVOEgvQ3RRZ0lIY2x5bVJ3djAxQkQ1NGozTzJncTZNNmlTRDUyOEZVemVXU0EwaWZXV29xSndpT1RWN29mamJxL2dHeENTV2w1ZHR0dXBEelN3QUpMcC9VOWRPNHZIU0l0RDJzWTU0bXBWZURJM0lFcUlxK3M0Mkg0VGt4RmlSSk5lQVN0MTg0bER4U1RFR2FmVEdjNWhUQmt1d3VqYXR4NjJkU0tuVTRyUVhwbjg4UE5qZjJWMUwiLCJtYWMiOiJiMGI1MzFlYjVjZmYwMTViMjZkM2UyYTRmZTZmMTk5YTMyYjM5NTg5NDY4NGE5YmYwYTY1NzFiMzdkNjZlMGM1IiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 00:18:21 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6InptYXlaTXN6bXFVRjliRWNwd3Q3SkE9PSIsInZhbHVlIjoiWHpmTUdMM080S0I0UUNYaldFUEVZVFJzWXJiTzZVMHN5ZlJ0eWh4Yk8zNEsvc0hIRHVCSjJDOUtNUWh3Z05JY0xjbFVzd3QyN0lPZzN0U3R3Q2ROdHlzVjNXT1h3ODFoOGp6RWMvditMa2laOFpTNkM2NjVHVFVkd2NOaGNFcUxGT3M3cGY0NjlmZy8zSm9QVmd2cnZUMDZ2bWhCM1NSK1dWMkFIRG1Bcm8rV29vdkpaRmNVZkkzMkJQWHZYWmorL0trZ2MxL21LdWNrbGVYWXFmQmI2N3E0K1MxcjhtWFIvUkc1bUZPRDVKY0Z3bmJJWDVTVUh6MXNOaUxpcTFKYlhZSVFmcmE4YkZCcFp4bUhTcmNQVnE3MXNGV2NKbHd2ZU9ScW10R1EzM0loSjRES3ZZVnQ4S0lnQ3dzeFd0djE0YktvSFBvazU1R3FGZE8xTzRwdGgxL2lQbmpKT0pFekJEYlBIWUR0KzVIcE41VHlQc01GbWsvWW1kbVEyQlAwZTRqS0hDUEVFUU9nWUFud25URWE4bjN1RkJnVy9Pd3lnU25kVmV0SmgzL25JYkdKdlpZSkFOdThhd1hTeWJETVQrbUxnS2pKTGdJa1ROQTdtQkFhd201ZCt2MUFHaGtYclpnMjRwT1dIZXpoK1FuWERlRElXR3dUQ2FRNUNDZ0wiLCJtYWMiOiIyZDBmODNhYWExNWZmZDMyYTNjNGEyMmMyZTJhYjg4YjY1MzU2ZTRjMWY0YjYwY2RhMzY1YWIyZjZhNjA0ZWViIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 00:18:21 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IjNLMnRPa1pwK1l4WTA1ejRhUlh3c1E9PSIsInZhbHVlIjoiNDRLckdpUVhCWDQ1Mnllams2cmZMc1pMQlRKMjVsQzNtVExvOEkyWnhuWXk3eXVlelJ4SGJxZ1dWQ0d0QjBFWGlEck04THJvS3hoTjVzYWNSMjJPeTJSOEl0VFp1L0U1bGNhWEQrVHRNdVVQRG5GQ2pGVmtkc1o3NmZTbzNwb3g4UmRuTEN1enNXeEp6OFJ2VkpOZldiZnJCaFZBZHVablRkS1F2QU9NT0QrbStVZjFPYWxnV3poUmU2RjRHT3o1V2lrTHlSaDMvS1Y0SVV2SkZNVmxITFVzeW16bExOT2V1MVRGNEVVVU9sQlJ0UklzOEV6dTdZeUdzN1IxMGhaNURCclJoeElPaXc0MWNCSFZWaVBpUkdLdm5DL0k5SDdEdE9ITnVRVHBlN3lCNGl6UzdSVFZ0TGxKdXZkOGJJTDNEQXJ3QThFN2FVOEgvQ3RRZ0lIY2x5bVJ3djAxQkQ1NGozTzJncTZNNmlTRDUyOEZVemVXU0EwaWZXV29xSndpT1RWN29mamJxL2dHeENTV2w1ZHR0dXBEelN3QUpMcC9VOWRPNHZIU0l0RDJzWTU0bXBWZURJM0lFcUlxK3M0Mkg0VGt4RmlSSk5lQVN0MTg0bER4U1RFR2FmVEdjNWhUQmt1d3VqYXR4NjJkU0tuVTRyUVhwbjg4UE5qZjJWMUwiLCJtYWMiOiJiMGI1MzFlYjVjZmYwMTViMjZkM2UyYTRmZTZmMTk5YTMyYjM5NTg5NDY4NGE5YmYwYTY1NzFiMzdkNjZlMGM1IiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 00:18:21 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6InptYXlaTXN6bXFVRjliRWNwd3Q3SkE9PSIsInZhbHVlIjoiWHpmTUdMM080S0I0UUNYaldFUEVZVFJzWXJiTzZVMHN5ZlJ0eWh4Yk8zNEsvc0hIRHVCSjJDOUtNUWh3Z05JY0xjbFVzd3QyN0lPZzN0U3R3Q2ROdHlzVjNXT1h3ODFoOGp6RWMvditMa2laOFpTNkM2NjVHVFVkd2NOaGNFcUxGT3M3cGY0NjlmZy8zSm9QVmd2cnZUMDZ2bWhCM1NSK1dWMkFIRG1Bcm8rV29vdkpaRmNVZkkzMkJQWHZYWmorL0trZ2MxL21LdWNrbGVYWXFmQmI2N3E0K1MxcjhtWFIvUkc1bUZPRDVKY0Z3bmJJWDVTVUh6MXNOaUxpcTFKYlhZSVFmcmE4YkZCcFp4bUhTcmNQVnE3MXNGV2NKbHd2ZU9ScW10R1EzM0loSjRES3ZZVnQ4S0lnQ3dzeFd0djE0YktvSFBvazU1R3FGZE8xTzRwdGgxL2lQbmpKT0pFekJEYlBIWUR0KzVIcE41VHlQc01GbWsvWW1kbVEyQlAwZTRqS0hDUEVFUU9nWUFud25URWE4bjN1RkJnVy9Pd3lnU25kVmV0SmgzL25JYkdKdlpZSkFOdThhd1hTeWJETVQrbUxnS2pKTGdJa1ROQTdtQkFhd201ZCt2MUFHaGtYclpnMjRwT1dIZXpoK1FuWERlRElXR3dUQ2FRNUNDZ0wiLCJtYWMiOiIyZDBmODNhYWExNWZmZDMyYTNjNGEyMmMyZTJhYjg4YjY1MzU2ZTRjMWY0YjYwY2RhMzY1YWIyZjZhNjA0ZWViIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 00:18:21 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-56720714\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1926486621 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">cN6pwMHluqMWM9hLaRtOtRasnQF8pfN3ghxjsOfa</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"44 characters\">http://localhost/users/15/login-with-company</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1926486621\", {\"maxDepth\":0})</script>\n"}}