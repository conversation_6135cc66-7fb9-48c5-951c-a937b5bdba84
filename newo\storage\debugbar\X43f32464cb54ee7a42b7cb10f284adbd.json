{"__meta": {"id": "X43f32464cb54ee7a42b7cb10f284adbd", "datetime": "2025-06-07 22:17:23", "utime": 1749334643.437939, "method": "GET", "uri": "/login", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749334641.125282, "end": 1749334643.437978, "duration": 2.3126959800720215, "duration_str": "2.31s", "measures": [{"label": "Booting", "start": 1749334641.125282, "relative_start": 0, "end": **********.77852, "relative_end": **********.77852, "duration": 1.65323805809021, "duration_str": "1.65s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.778569, "relative_start": 1.6532869338989258, "end": 1749334643.437982, "relative_end": 4.0531158447265625e-06, "duration": 0.6594130992889404, "duration_str": "659ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 50200216, "peak_usage_str": "48MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 4, "templates": [{"name": "1x auth.login", "param_count": null, "params": [], "start": 1749334643.026668, "type": "blade", "hash": "bladeC:\\laragon\\www\\to\\newo\\resources\\views/auth/login.blade.phpauth.login", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fresources%2Fviews%2Fauth%2Flogin.blade.php&line=1", "ajax": false, "filename": "login.blade.php", "line": "?"}, "render_count": 1, "name_original": "auth.login"}, {"name": "1x layouts.auth", "param_count": null, "params": [], "start": 1749334643.07171, "type": "blade", "hash": "bladeC:\\laragon\\www\\to\\newo\\resources\\views/layouts/auth.blade.phplayouts.auth", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fresources%2Fviews%2Flayouts%2Fauth.blade.php&line=1", "ajax": false, "filename": "auth.blade.php", "line": "?"}, "render_count": 1, "name_original": "layouts.auth"}, {"name": "1x landingpage::layouts.buttons", "param_count": null, "params": [], "start": 1749334643.385639, "type": "blade", "hash": "bladeC:\\laragon\\www\\to\\newo\\Modules/LandingPage\\Resources/views/layouts/buttons.blade.phplandingpage::layouts.buttons", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2FModules%2FLandingPage%2FResources%2Fviews%2Flayouts%2Fbuttons.blade.php&line=1", "ajax": false, "filename": "buttons.blade.php", "line": "?"}, "render_count": 1, "name_original": "landingpage::layouts.buttons"}, {"name": "1x layouts.cookie_consent", "param_count": null, "params": [], "start": 1749334643.405889, "type": "blade", "hash": "bladeC:\\laragon\\www\\to\\newo\\resources\\views/layouts/cookie_consent.blade.phplayouts.cookie_consent", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fresources%2Fviews%2Flayouts%2Fcookie_consent.blade.php&line=1", "ajax": false, "filename": "cookie_consent.blade.php", "line": "?"}, "render_count": 1, "name_original": "layouts.cookie_consent"}]}, "route": {"uri": "GET login/{lang?}", "middleware": "web, guest", "controller": "App\\Http\\Controllers\\Auth\\AuthenticatedSessionController@showLoginForm", "namespace": null, "prefix": "", "where": [], "as": "login", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FAuth%2FAuthenticatedSessionController.php&line=344\" onclick=\"\">app/Http/Controllers/Auth/AuthenticatedSessionController.php:344-359</a>"}, "queries": {"nb_statements": 9, "nb_failed_statements": 0, "accumulated_duration": 0.26848, "accumulated_duration_str": "268ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 46}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 78}, {"index": 15, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 555}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/Auth/AuthenticatedSessionController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\Auth\\AuthenticatedSessionController.php", "line": 348}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.908989, "duration": 0.0068, "duration_str": "6.8ms", "memory": 0, "memory_str": null, "filename": "Utility.php:46", "source": "app/Models/Utility.php:46", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUtility.php&line=46", "ajax": false, "filename": "Utility.php", "line": "46"}, "connection": "ty", "start_percent": 0, "width_percent": 2.533}, {"sql": "select table_name as `name`, (data_length + index_length) as `size`, table_comment as `comment`, engine as `engine`, table_collation as `collation` from information_schema.tables where table_schema = 'ty' and table_type in ('BASE TABLE', 'SYSTEM VERSIONED') order by table_name", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 537}, {"index": 14, "namespace": null, "name": "app/Http/Controllers/Auth/AuthenticatedSessionController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\Auth\\AuthenticatedSessionController.php", "line": 351}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.928989, "duration": 0.02588, "duration_str": "25.88ms", "memory": 0, "memory_str": null, "filename": "Utility.php:537", "source": "app/Models/Utility.php:537", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUtility.php&line=537", "ajax": false, "filename": "Utility.php", "line": "537"}, "connection": "ty", "start_percent": 2.533, "width_percent": 9.639}, {"sql": "select `full_name`, `code` from `languages`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 17, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 543}, {"index": 18, "namespace": null, "name": "app/Http/Controllers/Auth/AuthenticatedSessionController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\Auth\\AuthenticatedSessionController.php", "line": 351}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.969192, "duration": 0.00945, "duration_str": "9.45ms", "memory": 0, "memory_str": null, "filename": "Utility.php:543", "source": "app/Models/Utility.php:543", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUtility.php&line=543", "ajax": false, "filename": "Utility.php", "line": "543"}, "connection": "ty", "start_percent": 12.172, "width_percent": 3.52}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 4716}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 4650}, {"index": 15, "namespace": "view", "name": "auth.login", "file": "C:\\laragon\\www\\to\\newo\\resources\\views/auth/login.blade.php", "line": 4}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": 1749334643.044338, "duration": 0.00177, "duration_str": "1.77ms", "memory": 0, "memory_str": null, "filename": "Utility.php:4716", "source": "app/Models/Utility.php:4716", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUtility.php&line=4716", "ajax": false, "filename": "Utility.php", "line": "4716"}, "connection": "ty", "start_percent": 15.692, "width_percent": 0.659}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 4716}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 4650}, {"index": 15, "namespace": "view", "name": "layouts.auth", "file": "C:\\laragon\\www\\to\\newo\\resources\\views/layouts/auth.blade.php", "line": 10}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": 1749334643.083883, "duration": 0.00141, "duration_str": "1.41ms", "memory": 0, "memory_str": null, "filename": "Utility.php:4716", "source": "app/Models/Utility.php:4716", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUtility.php&line=4716", "ajax": false, "filename": "Utility.php", "line": "4716"}, "connection": "ty", "start_percent": 16.351, "width_percent": 0.525}, {"sql": "select * from `users` where `type` = 'super admin' limit 1", "type": "query", "params": [], "bindings": ["super admin"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 4081}, {"index": 17, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 4123}, {"index": 18, "namespace": "view", "name": "layouts.auth", "file": "C:\\laragon\\www\\to\\newo\\resources\\views/layouts/auth.blade.php", "line": 22}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": 1749334643.131067, "duration": 0.21406, "duration_str": "214ms", "memory": 0, "memory_str": null, "filename": "Utility.php:4081", "source": "app/Models/Utility.php:4081", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUtility.php&line=4081", "ajax": false, "filename": "Utility.php", "line": "4081"}, "connection": "ty", "start_percent": 16.876, "width_percent": 79.73}, {"sql": "select `value`, `name` from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 4082}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 4123}, {"index": 15, "namespace": "view", "name": "layouts.auth", "file": "C:\\laragon\\www\\to\\newo\\resources\\views/layouts/auth.blade.php", "line": 22}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": 1749334643.357391, "duration": 0.00172, "duration_str": "1.72ms", "memory": 0, "memory_str": null, "filename": "Utility.php:4082", "source": "app/Models/Utility.php:4082", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUtility.php&line=4082", "ajax": false, "filename": "Utility.php", "line": "4082"}, "connection": "ty", "start_percent": 96.607, "width_percent": 0.641}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 4716}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 4650}, {"index": 15, "namespace": "view", "name": "layouts.auth", "file": "C:\\laragon\\www\\to\\newo\\resources\\views/layouts/auth.blade.php", "line": 39}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": 1749334643.3685172, "duration": 0.0015, "duration_str": "1.5ms", "memory": 0, "memory_str": null, "filename": "Utility.php:4716", "source": "app/Models/Utility.php:4716", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUtility.php&line=4716", "ajax": false, "filename": "Utility.php", "line": "4716"}, "connection": "ty", "start_percent": 97.247, "width_percent": 0.559}, {"sql": "select * from `landing_page_settings`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 18, "namespace": null, "name": "Modules/LandingPage/Entities/LandingPageSetting.php", "file": "C:\\laragon\\www\\to\\newo\\Modules\\LandingPage\\Entities\\LandingPageSetting.php", "line": 27}, {"index": 19, "namespace": "view", "name": "landingpage::layouts.buttons", "file": "C:\\laragon\\www\\to\\newo\\Modules/LandingPage\\Resources/views/layouts/buttons.blade.php", "line": 2}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 73}], "start": 1749334643.392626, "duration": 0.005889999999999999, "duration_str": "5.89ms", "memory": 0, "memory_str": null, "filename": "LandingPageSetting.php:27", "source": "Modules/LandingPage/Entities/LandingPageSetting.php:27", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2FModules%2FLandingPage%2FEntities%2FLandingPageSetting.php&line=27", "ajax": false, "filename": "LandingPageSetting.php", "line": "27"}, "connection": "ty", "start_percent": 97.806, "width_percent": 2.194}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "aeg571Q3ThqeNMfiIAc14ULZrTrG6DjnVuualNlJ", "_previous": "array:1 [\n  \"url\" => \"http://localhost/login\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/login", "status_code": "<pre class=sf-dump id=sf-dump-346837111 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-346837111\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-506435156 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-506435156\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-235691585 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-235691585\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1742531629 data-indent-pad=\"  \"><span class=sf-dump-note>array:15</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">none</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1993 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=a091af98-270d-406e-ab7e-33a6e610d6e2414686; _clck=nups8l%7C2%7Cfwk%7C0%7C1960; _clsk=8djjbr%7C1749332394281%7C1%7C1%7Ck.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6Im9rYXY3UEJxSEUveHNWck9LVk5CZmc9PSIsInZhbHVlIjoiZjVsZDQxWDBodHlKTTJPRCtUaEI2VGk5TFRxZ21iR1lWMHI4VlFUblkvMlJ6RWFQUUVRQXZYNCswbkJ2UVV0Q0R2RjNoYWV6eDl6d1RUckQxWlR6U0dIQXpsdzBQRXJ6QjlOVDAzSmEvZGJRKzMvNlUwekVSSHpkTTNReTBlMDkvOGtPdkVOeE9IY0QvbFQ3RzFSOTc0d3BrdDZwTGY2R2hWM1crVTFvc0Z2N01nTFZsOHBwVkw3MmVVZkg5MGk3bEQxOHNlRVV5NkxKSmttZEw1VW91Y2p0aFlwdWVlcnh5MUhkTko1VkhQL2hDRkxwMlRjRnZBSEw1N090UXc2L0VXKzg4L1J6dDBzN01yRm5aV2hXUW1BL2kvZmlwUXpSWmVtYnRHMnRMVDQxOS9McTlsZEt1a0dXS2U5a21QYVRLUU1Wd1RFZXRBV3Z1Wktrc0Q5by9HcFdlR1ArYjk0bmxTZGlXbE5SOURGZnJuamQrTkFNUnZhZ3owMDU5TkdKazF3a1NpbnFXVXNtTkVIT3lUcHYybFJaaGNBR0F3TGpOYjlFUXpUbC9sOURPdzhvb3Vubm5Vd1FlOWJYWExtUHVwZGJYZDJlNjdvWTBvY29TWTZzd2FrS0xYZTlPRmJYMkRWdmpaZUlKcllpOElFNWxZZGVMb3dvOEx6S3d6ZDYiLCJtYWMiOiI0MGJhMWU2OWQ0OWU1ODYwOWUyMGQzOTllNjFmMzdjNTU0YmJmNGI1YjJhMDBlMjk5Y2MzZDhiOTgwNDUzYjFmIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6IjRwZG1nUi9OT0krM2lqRVZCbVJZbXc9PSIsInZhbHVlIjoickw0V21jcmhIU2RiVDVkcnE1TFE1dVVxRm5DYXA4M2dITnNnSXZlajIrK2psaVFQTWIvQVlGNFpkeXBXcE9teWtDa3BhckVEcTdLZGVwSzN6S3B0emJ1Z0s0YUhLR0g2U2ZtUDNuQkxSMS9VNWtPZ1RqOE1LUEllcVZXajUwSGs1QjFhS080T1h4M2ttNWluenVkaEthVVU2WEZDaGxGSjdxUE5CTnRvQkhDUFpuWE5yWUdHaXhNOEdrTWJvUFlBVE1MQ0VudUYrRldTSXFIejZjLzNwQmRFMHNSUmVYWXVvVWdydnM1d2ZibUcvWVVDSzlBNG5OanVQT2xQV0h4S01FTzFaVnBTK2kvZyt0ZW0zK2pXbVNOSGEwUlJZbVBzWSt5d0JqT3p6UHAxSUJCU0I0S0VZdFdTY0dWemxYQkhvVW5iQWhqakIrL0E4czRJZzlCRmxPemFuNzNHMVVaVzNRZE9nTlRvOFpFRldlOFFiNnhGaE0wcnozREZiTkxtWlc4dlRyY1lyZVNvQWlvaGVyaGJvVktrcVFYT3VkUHBkU1czRUo1Qk9XM1hOaDNWb0Nhbnp1SDg5VEcwMXY1clpEQ2FhQzl2Q0VLdFVvS3NadjUwYnVJM1U0YXViMWNWZE5TZG16SzlhcW0xUFBlZkQ2ZUVYSXFXcFhST2dVbVoiLCJtYWMiOiJiYTYxZGZkZjdlYTFhNzY2ZTZiNmYzMzQ0NGZlZTE0NDY3ODc1YTRlNzM3NjE3ZjA3YWM3NWUyZTg5NjM2MGVmIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1742531629\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1859997622 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">aeg571Q3ThqeNMfiIAc14ULZrTrG6DjnVuualNlJ</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">JGbRg3rX9CZC3OYjDIftJuBr6aHnx3C6fHbYY9Rw</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1859997622\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-80152972 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 07 Jun 2025 22:17:23 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IkJCREYvMmJDWXdqU1pxd3hHRGY3T3c9PSIsInZhbHVlIjoiek5nMHNYUW5DQzU1RUc1MHl2dnlIQ2ZDbzVIaElaVTM5Z3VaUDduYTJvZW5hWTFSck1YNWNyL3dlN1ZWTW1qVjRyNEZ3R3lFczV6YktNK2t4Um9RSU1BeVI5UHFHU3hER3FtMFlXejdrZzExN3dPV3BzRVNlSi9qRk5NckYrOVJ6bE9Md3ZPSHdIQnltbmcvVXlCMmFyZXovSmJHSStFMkpseTFpSC9xRGo3Sm8vK3hrSHR3L0JpNUNBVGs0S3BDaHJHZW5JSmpLY2FIVyt5L2QrOXRjY0RLYlpBOXlpYkZWVERaaUVwR2VJVTdQeXpTT1hReXFWSDduTmFNK2dNcVZZOVVhaVcvK0hPU3VMdXc0Vk1jSUZ6aXd0WFJVSHY5U3cxeWlMR0tveThZZXV4c1FIUTBxU1dvUjZ3OUpRMDRWRWFQWTRReE5DYnhEVm5Qb2NtYXppUEg1WDl1TmpzZnJPcmZsc3A2ZWFTdFJwbG9aRlBSWC85dU0rOUt6MXRZNGxSa0hKZkZaK3ZraG5GWXZNbmZvc3ZKYzV0V01mMkZMVk9FQm04dGtGbnV5cWRqRXRCektQMkdTWVloOElDT3dzQ3dOdGZ4Qy8zbzBGTGh2bTRrM1JCc0cvVWNyNEYwS2h5MCtSV2UycXdSRnJpMCtYd1kzeUhwNGxwNWovT3IiLCJtYWMiOiIwMGNiNDdmOWFhNWNjNDg2ZmM4Yjg1ZDlhMTk5YjU2ZmYwYjQ5MWU3NDA3ZjYxMmE0MjQwN2JiNjYwYmQ5ZTMzIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 00:17:23 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6InRNYVh1U0xHUVNBWkFMdmx0Y3ljV2c9PSIsInZhbHVlIjoiZ3NKc0txcll2OW1RUTFSNjIwWVhpbzBMWXoxQmZ5ZUxlOS90a3dSMWoyQlhHT2trVnI4NzlocDRnYWx5OGRFeWx4dGhMYzJUTG9xSFZnRmw2WVE4YzlER0NwM3ROd1lmbEtLTzdyNklMNjd2bzNnUUNid3pibVlhSGxBNWF5YXRFVzVOQWxDV1VFbW1mOVdVWUFjYW9BaFcwU2FENTJHNzAvcGFraU1KQ1JOZCs2Nk9ZYTM4RitVQUgzY2hBc05UMVJMNEpjREtHaFNtV2txQVZ3dEpHTGVwTGFnaDhsNnRwdVNQejJ3eUVpMlEyWGg5N1QrdmpJR0o2cTNCaEtWdG5jUU9aeXF2RlpVM1Jka25QU3JDcnZJZ0I2aG5OMzhRQWR1ZmU0OFBOYklZNlI5d2pmSEc2NDQzay9HREx3YzVnUEh6dGIzajBPc0FIY1Q5TzhoK2ZLNEc5WjZWUXRFbTkrSGFBWUZsN1J3d0hheStVQlJ5bDdNMnJmbVRSV1F0bXNRR3ExRTdXNTU0T3NScE93N3MrRDIrVGVoMDNQUWxkdTI0WWdDWnpFdklKL2RuMjFTSGRickhZaXRkeXhOR2dXVjJsTVRlWndlRjBUellmUUJsRzVzL1ExU1ZTNTRBUmkwTTBtN1Z5bnFaUTVpVFVDZGg2QVpRNEo5UDY2S28iLCJtYWMiOiJmNjE0ZDNmZTZmZmIyOTFkMWZmZmRkZThjNmVlYzY0OTVlYWFjNDdlNzIzYzhlODhhYzJjMWVlNzc2ZGE0MTM2IiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 00:17:23 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IkJCREYvMmJDWXdqU1pxd3hHRGY3T3c9PSIsInZhbHVlIjoiek5nMHNYUW5DQzU1RUc1MHl2dnlIQ2ZDbzVIaElaVTM5Z3VaUDduYTJvZW5hWTFSck1YNWNyL3dlN1ZWTW1qVjRyNEZ3R3lFczV6YktNK2t4Um9RSU1BeVI5UHFHU3hER3FtMFlXejdrZzExN3dPV3BzRVNlSi9qRk5NckYrOVJ6bE9Md3ZPSHdIQnltbmcvVXlCMmFyZXovSmJHSStFMkpseTFpSC9xRGo3Sm8vK3hrSHR3L0JpNUNBVGs0S3BDaHJHZW5JSmpLY2FIVyt5L2QrOXRjY0RLYlpBOXlpYkZWVERaaUVwR2VJVTdQeXpTT1hReXFWSDduTmFNK2dNcVZZOVVhaVcvK0hPU3VMdXc0Vk1jSUZ6aXd0WFJVSHY5U3cxeWlMR0tveThZZXV4c1FIUTBxU1dvUjZ3OUpRMDRWRWFQWTRReE5DYnhEVm5Qb2NtYXppUEg1WDl1TmpzZnJPcmZsc3A2ZWFTdFJwbG9aRlBSWC85dU0rOUt6MXRZNGxSa0hKZkZaK3ZraG5GWXZNbmZvc3ZKYzV0V01mMkZMVk9FQm04dGtGbnV5cWRqRXRCektQMkdTWVloOElDT3dzQ3dOdGZ4Qy8zbzBGTGh2bTRrM1JCc0cvVWNyNEYwS2h5MCtSV2UycXdSRnJpMCtYd1kzeUhwNGxwNWovT3IiLCJtYWMiOiIwMGNiNDdmOWFhNWNjNDg2ZmM4Yjg1ZDlhMTk5YjU2ZmYwYjQ5MWU3NDA3ZjYxMmE0MjQwN2JiNjYwYmQ5ZTMzIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 00:17:23 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6InRNYVh1U0xHUVNBWkFMdmx0Y3ljV2c9PSIsInZhbHVlIjoiZ3NKc0txcll2OW1RUTFSNjIwWVhpbzBMWXoxQmZ5ZUxlOS90a3dSMWoyQlhHT2trVnI4NzlocDRnYWx5OGRFeWx4dGhMYzJUTG9xSFZnRmw2WVE4YzlER0NwM3ROd1lmbEtLTzdyNklMNjd2bzNnUUNid3pibVlhSGxBNWF5YXRFVzVOQWxDV1VFbW1mOVdVWUFjYW9BaFcwU2FENTJHNzAvcGFraU1KQ1JOZCs2Nk9ZYTM4RitVQUgzY2hBc05UMVJMNEpjREtHaFNtV2txQVZ3dEpHTGVwTGFnaDhsNnRwdVNQejJ3eUVpMlEyWGg5N1QrdmpJR0o2cTNCaEtWdG5jUU9aeXF2RlpVM1Jka25QU3JDcnZJZ0I2aG5OMzhRQWR1ZmU0OFBOYklZNlI5d2pmSEc2NDQzay9HREx3YzVnUEh6dGIzajBPc0FIY1Q5TzhoK2ZLNEc5WjZWUXRFbTkrSGFBWUZsN1J3d0hheStVQlJ5bDdNMnJmbVRSV1F0bXNRR3ExRTdXNTU0T3NScE93N3MrRDIrVGVoMDNQUWxkdTI0WWdDWnpFdklKL2RuMjFTSGRickhZaXRkeXhOR2dXVjJsTVRlWndlRjBUellmUUJsRzVzL1ExU1ZTNTRBUmkwTTBtN1Z5bnFaUTVpVFVDZGg2QVpRNEo5UDY2S28iLCJtYWMiOiJmNjE0ZDNmZTZmZmIyOTFkMWZmZmRkZThjNmVlYzY0OTVlYWFjNDdlNzIzYzhlODhhYzJjMWVlNzc2ZGE0MTM2IiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 00:17:23 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-80152972\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1765321782 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">aeg571Q3ThqeNMfiIAc14ULZrTrG6DjnVuualNlJ</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/login</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1765321782\", {\"maxDepth\":0})</script>\n"}}