{"__meta": {"id": "Xc4550771961e977bec5a07fe464f3a1f", "datetime": "2025-06-07 22:38:21", "utime": **********.168558, "method": "GET", "uri": "/add-to-cart/3/pos", "ip": "127.0.0.1"}, "php": {"version": "8.3.16", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749335900.345442, "end": **********.168589, "duration": 0.8231470584869385, "duration_str": "823ms", "measures": [{"label": "Booting", "start": 1749335900.345442, "relative_start": 0, "end": 1749335900.983003, "relative_end": 1749335900.983003, "duration": 0.6375608444213867, "duration_str": "638ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": 1749335900.983023, "relative_start": 0.6375808715820312, "end": **********.168593, "relative_end": 3.814697265625e-06, "duration": 0.18557000160217285, "duration_str": "186ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 53602200, "peak_usage_str": "51MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET add-to-cart/{id}/{session}", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\ProductServiceController@addToCart", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FProductServiceController.php&line=1236\" onclick=\"\">app/Http/Controllers/ProductServiceController.php:1236-1493</a>"}, "queries": {"nb_statements": 6, "nb_failed_statements": 0, "accumulated_duration": 0.00875, "accumulated_duration_str": "8.75ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 16 limit 1", "type": "query", "params": [], "bindings": ["16"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.053483, "duration": 0.00455, "duration_str": "4.55ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 52}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.077194, "duration": 0.00085, "duration_str": "850μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 52, "width_percent": 9.714}, {"sql": "select `permissions`.*, `model_has_permissions`.`model_id` as `pivot_model_id`, `model_has_permissions`.`permission_id` as `pivot_permission_id`, `model_has_permissions`.`model_type` as `pivot_model_type` from `permissions` inner join `model_has_permissions` on `permissions`.`id` = `model_has_permissions`.`permission_id` where `model_has_permissions`.`model_id` = 16 and `model_has_permissions`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["16", "App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 326}, {"index": 21, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 566}], "start": **********.111097, "duration": 0.0009699999999999999, "duration_str": "970μs", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:326", "source": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php:326", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=326", "ajax": false, "filename": "HasPermissions.php", "line": "326"}, "connection": "ty", "start_percent": 61.714, "width_percent": 11.086}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (16) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 312}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 26, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}], "start": **********.1165538, "duration": 0.00079, "duration_str": "790μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 72.8, "width_percent": 9.029}, {"sql": "select * from `product_services` where `product_services`.`id` = '3' limit 1", "type": "query", "params": [], "bindings": ["3"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/ProductServiceController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\ProductServiceController.php", "line": 1240}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.128694, "duration": 0.0008, "duration_str": "800μs", "memory": 0, "memory_str": null, "filename": "ProductServiceController.php:1240", "source": "app/Http/Controllers/ProductServiceController.php:1240", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FProductServiceController.php&line=1240", "ajax": false, "filename": "ProductServiceController.php", "line": "1240"}, "connection": "ty", "start_percent": 81.829, "width_percent": 9.143}, {"sql": "select sum(`quantity`) as aggregate from `warehouse_products` where `product_id` = 3 and exists (select * from `warehouses` where `warehouse_products`.`warehouse_id` = `warehouses`.`id` and `created_by` = 15)", "type": "query", "params": [], "bindings": ["3", "15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/ProductService.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\ProductService.php", "line": 155}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/ProductServiceController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\ProductServiceController.php", "line": 1244}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.1385748, "duration": 0.00079, "duration_str": "790μs", "memory": 0, "memory_str": null, "filename": "ProductService.php:155", "source": "app/Models/ProductService.php:155", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FProductService.php&line=155", "ajax": false, "filename": "ProductService.php", "line": "155"}, "connection": "ty", "start_percent": 90.971, "width_percent": 9.029}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}, "App\\Models\\ProductService": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FProductService.php&line=1", "ajax": false, "filename": "ProductService.php", "line": "?"}}}, "count": 3, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 1, "messages": [{"message": "[\n  ability => manage product & service,\n  result => true,\n  user => 16,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1275576075 data-indent-pad=\"  \"><span class=sf-dump-note>manage product & service</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"24 characters\">manage product &amp; service</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>16</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1275576075\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.126766, "xdebug_link": null}]}, "session": {"_token": "iD3eOztlFQTk9Wu93brk2PH5kNLllwdWSLzbHLjf", "_previous": "array:1 [\n  \"url\" => \"http://localhost:8000/pos\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "16", "pos": "array:1 [\n  3 => array:9 [\n    \"name\" => \"حمد\"\n    \"quantity\" => 1\n    \"price\" => \"10.00\"\n    \"id\" => \"3\"\n    \"tax\" => 0\n    \"subtotal\" => 10.0\n    \"originalquantity\" => 18\n    \"product_tax\" => \"-\"\n    \"product_tax_id\" => 0\n  ]\n]"}, "request": {"path_info": "/add-to-cart/3/pos", "status_code": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-1402604438 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1402604438\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1228453630 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1228453630\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1286195321 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">iD3eOztlFQTk9Wu93brk2PH5kNLllwdWSLzbHLjf</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"25 characters\">http://localhost:8000/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1938 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=17ceeen%7C2%7Cfwk%7C0%7C1960; _clsk=1yftgw7%7C1749335881083%7C6%7C1%7Ck.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6ImZRVDFBbjA1TDMwTEZNbGMrK2dXTEE9PSIsInZhbHVlIjoicGQ3ZUxOaEVSNDRCZ3JPVTQybXNiQk96RzFKekNhcXluci9oU2hLb2xBT3RIclM2azBQc3YydzFKWmNrUm9WOWtJS202aFNmeVR4SDNWWHVqR0dYNzlVVHZpdjdHNkh2czNZOHVnQ1pqdEZBY1BjWHJlb0F4YklyckRYUzlkcE1GbzZBa3FiZ1l1azZySzMxa1JxZmVaS0lzalBWSkxrUEcxRmhnUHpmS3pGall4NVA0MWhZSk52OXlsQ3RaclV5bUNTWW5ZejFGVGN6MFFhWUE3Zms2RjF5L0J5ODZuM2cya0t5QXlKRW9KRXczUFN0aTI3QnZxOFhSR016eGpjUS9ybVJxcjNpL3FuamQrK1FkdmRzZVpzZGVBQStQMDA1aDRBSXBzWDFvOExmN2c3elB1eFhiZTJyOGhFMi8zME5WRVZsOEhCT3V0cTJ5VXhVNFNtZG14aVZ4Ly9ObTNGSVMzZkZFenpidGNoU2JwaVk2dDBMeHhsRkFJK1VCQWFGRU5GeVFGMkxuZSttR3FiS0pIckxaSnREWG1aRlMwV3J2cGNOamxPOEhyWGNYMEpyK1NMRVFZRFc2SzJJSCtwTG9rcEtIckJkZWFPR2owMFJQeVE3d1Fwd25Cb3dmNkQrdS8vMHBKMUpWUTBtWGJoUDNSVVA2YW51MGxuOE1JQSsiLCJtYWMiOiI4ZjY5NmM1NzZhNGZkNTk2M2NhODBmYTUxZWM1YmFmNzQ2NDMzOTc4NTBlMTQzNzAzNTc5YzdmM2MxYjdlOWI2IiwidGFnIjoiIn0%3D; test_session=eyJpdiI6IllySEUyaVJQVWwyWDlKUnc5dVBLdmc9PSIsInZhbHVlIjoidFJUM0JabzgxanpEbVZQNldLcFBnNXZyRGlFWUozUE9WOUJIeVppNWpxUVF6V1RrMkMxSmRaaFdnWU1SWHg0NlBidmFlYWdIcnhJeU94SklNNjR5ZGpWT0dQL0xuQzNObjUydTVob0g5QWZqKzFPM2hKNzI3aWwxNkVrSHN5djdxdldqYVdPUWNoSXlmODdXNjVjaEd1aVI4K1JCdkNIcnUxSFNrQTZ6c3F4dTRqMW85cWR6WEdJWFRrWDF6dXV0VHIwV0k1RE1NQ2RudmpUSkVmdmRlZkJ0Rnp2OFpoSWN3UmZvem9NV0o2bFhCUmNvRUt4THg3YnRQczhrWnRDYVpjamJGVWZuM015VFU0Q0ovNkd3TnM2dnBFS01LZDBPZlVvaGVoOWJYNzhRemc5Tm5QQkU5bFo5c210aDUyZUxwNStxS00vZHVabjE3RUJhQ0VkeFRQbkFYMCtsS0JlaDNKSDdqSWR4aElHUEpnUUFuOUFEQzZ4SXJKQWlkQWNRWTlUd0p1UFloR0UzM3RyZkE1YjYxY3ZGekUyK2JVa2hER0pKRXBDUzh1TGNRWW0zbkpyN1MyN1hvR2x5REt1T2JlSUpqeG0yZDNIZ3hFZUNuTlJldG0wV09MS1doMjdHVmFoVU5nQU9SZ1FjZzdsOWFaaHM0NEJFS2MzNGFqRnoiLCJtYWMiOiI2ODZjNmI3ZDI3NWVjYTYxMzk1N2RlOWM3NTMyMzY1MjAwYTA3NWE5ZjY3ODI4OTgxNTg0YTY2NDlmY2VmYTc1IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1286195321\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1557347822 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">iD3eOztlFQTk9Wu93brk2PH5kNLllwdWSLzbHLjf</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">atMfj5qpj5gKx4OQYPP5PbQzRbuF7iB8X4zv4aOY</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1557347822\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1610309175 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 07 Jun 2025 22:38:21 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IlhyNi85c0psaW9kRlpGVENFUkVkOVE9PSIsInZhbHVlIjoianpvQkNtOEJBeE5hNG9YOXluMUUwUTJwNVAyNnI3OHpnQWh3UjZiWG9EUGZmMmpLM2pCTWtOd0drODJFVGlQSGhPZi9CWTRSK2hhR2ZET0poR3BEZktuYXNqUTdQM1A3MmZHbkpyOGc5QWxvaTE0UnZ6K1RFYi9IRnVUd3RHc3NSd1kwRzVrRlF2U2FqTUw5K3d1cXBXTStDc3djRW9SMVVjUHZZQ1puWEFhdTBLSG4wMXFKMzJOTkp0U3orNWx5MjRieXE5Y0YvOEw2dFBScmJpTVBYcG9rMzhqRWhxN04wR29kNnM3dWFvWUduRE1PZm9FTCs2N25qelYxWGtjQ0hJQ1p2SGtEdEJkTVBBYjE2VXNYYkU5VytJRmpKRDBMRXBSeWZLK2xNSjFmTE41ZnJhQWdvc21KLzhoVWtFdHFwWk93QjJ1ZmU0YlFBYTRQUWh6cHJ4RS9Ld281WnppNm8wL0REaTN1d1pXU1JYKzl3QzVPb0FZYnFDdlQ4MVZxQUlVR0NxVit1WW0rUWhRdmQ0WFFHVVFPMnZuZi8vajBaUEt4VEpvc2xLUkwvVm9SdWlhb2d4WGZNdE1jdjhFa1ovRHZ5ejJ2RmRlUVREQXZmaDVTTVpqN3JtckVBMTBzYUd6TkdFMDA5dThrbWRzdExZNkVMOEVwNjBTM0RwSzkiLCJtYWMiOiI1Yzk4MTQwNWE2YWViMDNlODAwMzJhYTcwMmY1MDNmYmY3OGFjNzI4NDAxNTUyNmFhYTdmMTIzYjYxMmFmNmUwIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 00:38:21 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6IkxsNURiNm9lV3NmUDhOWGlYMGVEd0E9PSIsInZhbHVlIjoidzJoU2JJNFhnN24vL2xRb2dVNkFmRUliYW0valJwRHo0R2w0V2Z4bktBRlp3ckwyT1pSMkw0VHFQT3NWMkJwV29tTlhZNG9oblhONnlNYW9QQTF3VHFOR2ZjMkZGVm9obUxndGZnaHpIbEEwVG1JT1E2Sk5XenFpUGN2SVozT0QzMFRFOWh1SnBVcDVCd05tY1E3TGorTVpkRDJ4QkdkYVk2Nm16NUlvT3hOQ2p6aXFJVDNSb3gwdzMzRHpzMkNKaXhJYjVnbXAwcDYwY1lMYVBpSjJVNzFmZWMrSGtRWEM4azgxOHBXbi9La2FhTzFNcTg2RzRISzFWazZkK0pReE1OaW5LdGI4L08vUnNGOFlocVlrL3RKNWNEV3FROUE2cUNTQkRUSjVNaGVGYU1BT0xaY2p4Z216eVNFMWFyMmE4RU9BVGxJS3NUZjBsSW02MVI0MEJleHhyNktVSEc2dXI0dmkzZTVwb0p3Q2Z4THAwa3A1VGZmNHVOYitTQWd5a0RlbkFQa0dEQTFJay9NTEFYdk9pNmUzUTFhR2dwVDllQUFFZHptRWpMWEtiZVBwVnROYW5TK2V5eGxhTU9sUWRrU2lBcUhTMmtybE15TCtHWFBpQStqc3NaZk9UQXRUeTA2NEFvaGovL29sbkl2c2lMdkd4OTFaOWorT2IzWjMiLCJtYWMiOiI2ZmY1OTVlNDc0M2RjNGFhNjRjMzBjMGVmNGVlYWJkNjY4ZjAxMmFjNTgzZTRjODk3OThhYzM2YWVkN2VmOTVlIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 00:38:21 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IlhyNi85c0psaW9kRlpGVENFUkVkOVE9PSIsInZhbHVlIjoianpvQkNtOEJBeE5hNG9YOXluMUUwUTJwNVAyNnI3OHpnQWh3UjZiWG9EUGZmMmpLM2pCTWtOd0drODJFVGlQSGhPZi9CWTRSK2hhR2ZET0poR3BEZktuYXNqUTdQM1A3MmZHbkpyOGc5QWxvaTE0UnZ6K1RFYi9IRnVUd3RHc3NSd1kwRzVrRlF2U2FqTUw5K3d1cXBXTStDc3djRW9SMVVjUHZZQ1puWEFhdTBLSG4wMXFKMzJOTkp0U3orNWx5MjRieXE5Y0YvOEw2dFBScmJpTVBYcG9rMzhqRWhxN04wR29kNnM3dWFvWUduRE1PZm9FTCs2N25qelYxWGtjQ0hJQ1p2SGtEdEJkTVBBYjE2VXNYYkU5VytJRmpKRDBMRXBSeWZLK2xNSjFmTE41ZnJhQWdvc21KLzhoVWtFdHFwWk93QjJ1ZmU0YlFBYTRQUWh6cHJ4RS9Ld281WnppNm8wL0REaTN1d1pXU1JYKzl3QzVPb0FZYnFDdlQ4MVZxQUlVR0NxVit1WW0rUWhRdmQ0WFFHVVFPMnZuZi8vajBaUEt4VEpvc2xLUkwvVm9SdWlhb2d4WGZNdE1jdjhFa1ovRHZ5ejJ2RmRlUVREQXZmaDVTTVpqN3JtckVBMTBzYUd6TkdFMDA5dThrbWRzdExZNkVMOEVwNjBTM0RwSzkiLCJtYWMiOiI1Yzk4MTQwNWE2YWViMDNlODAwMzJhYTcwMmY1MDNmYmY3OGFjNzI4NDAxNTUyNmFhYTdmMTIzYjYxMmFmNmUwIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 00:38:21 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6IkxsNURiNm9lV3NmUDhOWGlYMGVEd0E9PSIsInZhbHVlIjoidzJoU2JJNFhnN24vL2xRb2dVNkFmRUliYW0valJwRHo0R2w0V2Z4bktBRlp3ckwyT1pSMkw0VHFQT3NWMkJwV29tTlhZNG9oblhONnlNYW9QQTF3VHFOR2ZjMkZGVm9obUxndGZnaHpIbEEwVG1JT1E2Sk5XenFpUGN2SVozT0QzMFRFOWh1SnBVcDVCd05tY1E3TGorTVpkRDJ4QkdkYVk2Nm16NUlvT3hOQ2p6aXFJVDNSb3gwdzMzRHpzMkNKaXhJYjVnbXAwcDYwY1lMYVBpSjJVNzFmZWMrSGtRWEM4azgxOHBXbi9La2FhTzFNcTg2RzRISzFWazZkK0pReE1OaW5LdGI4L08vUnNGOFlocVlrL3RKNWNEV3FROUE2cUNTQkRUSjVNaGVGYU1BT0xaY2p4Z216eVNFMWFyMmE4RU9BVGxJS3NUZjBsSW02MVI0MEJleHhyNktVSEc2dXI0dmkzZTVwb0p3Q2Z4THAwa3A1VGZmNHVOYitTQWd5a0RlbkFQa0dEQTFJay9NTEFYdk9pNmUzUTFhR2dwVDllQUFFZHptRWpMWEtiZVBwVnROYW5TK2V5eGxhTU9sUWRrU2lBcUhTMmtybE15TCtHWFBpQStqc3NaZk9UQXRUeTA2NEFvaGovL29sbkl2c2lMdkd4OTFaOWorT2IzWjMiLCJtYWMiOiI2ZmY1OTVlNDc0M2RjNGFhNjRjMzBjMGVmNGVlYWJkNjY4ZjAxMmFjNTgzZTRjODk3OThhYzM2YWVkN2VmOTVlIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 00:38:21 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1610309175\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-2041320662 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">iD3eOztlFQTk9Wu93brk2PH5kNLllwdWSLzbHLjf</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"25 characters\">http://localhost:8000/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>16</span>\n  \"<span class=sf-dump-key>pos</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-key>3</span> => <span class=sf-dump-note>array:9</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"3 characters\">&#1581;&#1605;&#1583;</span>\"\n      \"<span class=sf-dump-key>quantity</span>\" => <span class=sf-dump-num>1</span>\n      \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"5 characters\">10.00</span>\"\n      \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str>3</span>\"\n      \"<span class=sf-dump-key>tax</span>\" => <span class=sf-dump-num>0</span>\n      \"<span class=sf-dump-key>subtotal</span>\" => <span class=sf-dump-num>10.0</span>\n      \"<span class=sf-dump-key>originalquantity</span>\" => <span class=sf-dump-num>18</span>\n      \"<span class=sf-dump-key>product_tax</span>\" => \"<span class=sf-dump-str>-</span>\"\n      \"<span class=sf-dump-key>product_tax_id</span>\" => <span class=sf-dump-num>0</span>\n    </samp>]\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2041320662\", {\"maxDepth\":0})</script>\n"}}