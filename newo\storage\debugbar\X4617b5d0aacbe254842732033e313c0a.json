{"__meta": {"id": "X4617b5d0aacbe254842732033e313c0a", "datetime": "2025-06-07 22:38:12", "utime": **********.959003, "method": "GET", "uri": "/product-categories", "ip": "127.0.0.1"}, "php": {"version": "8.3.16", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749335891.933372, "end": **********.959042, "duration": 1.025670051574707, "duration_str": "1.03s", "measures": [{"label": "Booting", "start": 1749335891.933372, "relative_start": 0, "end": **********.764923, "relative_end": **********.764923, "duration": 0.8315510749816895, "duration_str": "832ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.764942, "relative_start": 0.8315699100494385, "end": **********.959046, "relative_end": 3.814697265625e-06, "duration": 0.19410395622253418, "duration_str": "194ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 48103152, "peak_usage_str": "46MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET product-categories", "middleware": "web, verified, auth, XSS, category.access", "controller": "App\\Http\\Controllers\\ProductServiceCategoryController@getProductCategories", "namespace": null, "prefix": "", "where": [], "as": "product.categories", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FProductServiceCategoryController.php&line=203\" onclick=\"\">app/Http/Controllers/ProductServiceCategoryController.php:203-229</a>"}, "queries": {"nb_statements": 6, "nb_failed_statements": 0, "accumulated_duration": 0.023300000000000005, "accumulated_duration_str": "23.3ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 16 limit 1", "type": "query", "params": [], "bindings": ["16"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.8462331, "duration": 0.01805, "duration_str": "18.05ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 77.468}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.8808968, "duration": 0.0011200000000000001, "duration_str": "1.12ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 77.468, "width_percent": 4.807}, {"sql": "select `permissions`.*, `model_has_permissions`.`model_id` as `pivot_model_id`, `model_has_permissions`.`permission_id` as `pivot_permission_id`, `model_has_permissions`.`model_type` as `pivot_model_type` from `permissions` inner join `model_has_permissions` on `permissions`.`id` = `model_has_permissions`.`permission_id` where `model_has_permissions`.`model_id` = 16 and `model_has_permissions`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["16", "App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 326}, {"index": 21, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 566}], "start": **********.9148262, "duration": 0.00098, "duration_str": "980μs", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:326", "source": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php:326", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=326", "ajax": false, "filename": "HasPermissions.php", "line": "326"}, "connection": "ty", "start_percent": 82.275, "width_percent": 4.206}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (16) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 312}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 26, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}], "start": **********.9205651, "duration": 0.00098, "duration_str": "980μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 86.481, "width_percent": 4.206}, {"sql": "select `product_service_categories`.*, COUNT(pu.category_id) product_services from `product_service_categories` left join `product_services` as `pu` on `product_service_categories`.`id` = `pu`.`category_id` where `product_service_categories`.`created_by` = 15 and `product_service_categories`.`type` = 'product & service' group by `product_service_categories`.`id` order by `product_service_categories`.`id` desc", "type": "query", "params": [], "bindings": ["15", "product &amp; service"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/ProductServiceCategory.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\ProductServiceCategory.php", "line": 116}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/ProductServiceCategoryController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\ProductServiceCategoryController.php", "line": 206}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.9347389, "duration": 0.00124, "duration_str": "1.24ms", "memory": 0, "memory_str": null, "filename": "ProductServiceCategory.php:116", "source": "app/Models/ProductServiceCategory.php:116", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FProductServiceCategory.php&line=116", "ajax": false, "filename": "ProductServiceCategory.php", "line": "116"}, "connection": "ty", "start_percent": 90.687, "width_percent": 5.322}, {"sql": "select count(*) as aggregate from `product_services` left join `product_service_categories` as `c` on `c`.`id` = `product_services`.`category_id` where `product_services`.`type` = 'product' and `product_services`.`created_by` = 15", "type": "query", "params": [], "bindings": ["product", "15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/ProductServiceCategoryController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\ProductServiceCategoryController.php", "line": 209}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.941876, "duration": 0.00093, "duration_str": "930μs", "memory": 0, "memory_str": null, "filename": "ProductServiceCategoryController.php:209", "source": "app/Http/Controllers/ProductServiceCategoryController.php:209", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FProductServiceCategoryController.php&line=209", "ajax": false, "filename": "ProductServiceCategoryController.php", "line": "209"}, "connection": "ty", "start_percent": 96.009, "width_percent": 3.991}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}, "App\\Models\\ProductServiceCategory": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FProductServiceCategory.php&line=1", "ajax": false, "filename": "ProductServiceCategory.php", "line": "?"}}}, "count": 3, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 1, "messages": [{"message": "[ability => create purchase, result => true, user => 16, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-248866728 data-indent-pad=\"  \"><span class=sf-dump-note>create purchase</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"15 characters\">create purchase</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>16</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-248866728\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.931859, "xdebug_link": null}]}, "session": {"_token": "iD3eOztlFQTk9Wu93brk2PH5kNLllwdWSLzbHLjf", "_previous": "array:1 [\n  \"url\" => \"http://localhost:8000/pos\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "16"}, "request": {"path_info": "/product-categories", "status_code": "<pre class=sf-dump id=sf-dump-1634201642 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1634201642\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-535426382 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-535426382\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-920912486 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-920912486\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-140951966 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">iD3eOztlFQTk9Wu93brk2PH5kNLllwdWSLzbHLjf</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"25 characters\">http://localhost:8000/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1938 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=17ceeen%7C2%7Cfwk%7C0%7C1960; _clsk=1yftgw7%7C1749335881083%7C6%7C1%7Ck.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6Ik1OUUVYcUJaWjdYdWJnZWlvMWNxSnc9PSIsInZhbHVlIjoiSGlUa3RzaHg0VDdVdXRwMHlTWnBvOGxab29vR2plYkthek5lWG1tRy92cjQvdEtHTCtiOVhWSlBndk0vOUhMdFQ0RHFvWXA2Q2xrUFNJRVIwR2FSYm05eFM4UUlVSTJZU1BvczNESVRQZDliNlhxcHZXTUtodEZGenYrNlpiSjBIeTJHYUp3OWpkbm0yZE5kaER4MkxaL2pvTkpuaW9uNkYwZ3k3TnJHd2VHZ0I1VDF4bGlvc2dqbUhTaHV2dTBhMWphT3FhZkM5VzNhQkhLd05SQUdlM2psVEtqVjVISDArNkt3MjVPdzl4NnBsTEd1MVI3TFZwc0NocUpXZVZWSFZpYW5XdXZKeGRkRlFZa0N0UXhQUC9IWGg0TFIzTW8vUWdmemRNQWFRMlhCTFhmdWROaVYrYlZ3cmtWbnowdk5BdnZaNnpycUVSYVNpekg1TmJkZlpxeTQyelhWcnFLVnVKek1rcVpZUEsrcXZJWWRDOEx1RjF6REhqNGx2RlJPQTZvRTNuQmR6YkhkanJrU055MWoyZWNsbDRNNHNyaTB4M1V0dENDNGJuOTYrTExiNllxN09sbnVmejUyZDVYdHlzOEtNVFArU0RyVGlNa29vK2dlSGovREdhSVE4L0VFcTJhUU1HZGZmRHJtbHl1WG9GdzhHZlJZTTd4NW9TVlEiLCJtYWMiOiIwZjc5MmIzZWE3N2MyY2RlNWQyYmNkZDI1OGY0NWI0M2M1NGE1MjYyOGY5ZDIzNzE1NTcxZTllOWQ3ODQ2MDQyIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6IjBaQ0VXeVJheUVYY0hFWFF5L0xQRGc9PSIsInZhbHVlIjoiY2wxUnFLY2E5SjkyQjY0RjBDYXg1UW44bFZmUWN0ZXRhTEtyaUxLd1k5YnJxOURJeWJLNC9ZQk9yMjFmTTBrRHd4Y1VaajVxaVUza2YxSTZxcVd0SVM3eTgwQU1MUlVPU0k2Q2t1Z0tYZzNVd3YrTGVTNVdTUGtEZ0Y5bHBhVVVlK1d4bzFUaVMxaldNaktaVmowcGF4cjJCVjhycnMxWXIvZFNjVGhTblNnS0lweXlOYjZtZ1pDcmh5TEZkN1JSTUJldnphemdrL2YvcUcrVXI0ZmZuc1dLUU5FNktSUTBNeEFPTzR6ekNibW1jSVNGdmlOL0s4N3FlZWhxc08xc1NIZnV5aS9hN3M1ek9IczVPVmxLUElia0JhdnZTZkg3eXZ5VGtwV1dkVmhDK1lPNTFuc21FOFh1RWlsUlgvbFlvaFQxYUJpMXA3L1dxVW12Um1NVU1VWStwSXBDNHZQMXNWWHBNSUI0WGlHUFV6S2JLUEdqOG1pak1zUmRjbmkyc0ZuU3lkaGQ5WFY3TDZ2UXdGc0N3bUczcGR4MlRmdmpYRWpHY2NpUzBXQ296aisrMzM3TTNKcU9tcmEvQU9Uc0MxTGRLK1pJcy80RUZkbFNEckdyb2FNdnVVOHVLVW9QMkk2TElva3ByZGRFbWFlVFRMSENMekxLMkk4bk8rUXUiLCJtYWMiOiI4YTQ1NTUxNWM4NTRhY2E2MDE0MGU3MDJkNjQ2MTg5MTYwMzg2YzAwYmI1YTBhZDdmMzk0NDAwMGJkNmZkMjFlIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-140951966\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1737812226 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">iD3eOztlFQTk9Wu93brk2PH5kNLllwdWSLzbHLjf</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">atMfj5qpj5gKx4OQYPP5PbQzRbuF7iB8X4zv4aOY</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1737812226\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-881495695 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 07 Jun 2025 22:38:12 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IjlaZEpxNnNXdUxlYnRaTFNRaE5KWkE9PSIsInZhbHVlIjoiRk1xeW50dUJnUGxTclJWOTNSajVzV1duaUgvZndraE9mNXVuZnJxY1NjM2pJR2VCWEZDa2pRbzBGbVd1NmlGOXdPbEVDQm04WTloN2JVcUZ0SWYrNXBYQ05tWHVBcGMzaFNwYUdValpmbkJyZ09xYUZaaDBPenE5T2dLL3U1ZXZUOEM1M3QxR0h3M2VWL2VNc015bTFhV2cwTUM0SjJJTFVwTXp1djFJeEJhRXRlSlBQeitlQzY5ZFkvYmltMHBjZVZnQnI0UGwyM2kyMlNXczRTTUVlaW1Ea21wcjJnR3duV055ZHpnSmdndjBNOUR3TExmc1hjOFFGUzBNaWR5bjM3TmJhUDdMYU5uUUJWZVRIeTZkKytXWjk1WmI5cXFyZS81a0swVmlGdnJrZjZRaXhjNHBEdzRIb1ZFazFDSmlCc01xSGYrMlQ3TkltUHZIa09qZkgvRjA0MnFuYjZFYWxqVE41cDJtMFptUCtTeHFCdnB4Wm00UzNqbG1pMzN0ZnAyYkJKWDZiQ0c2WFdoRGpGcVNsMEFOZk5mS1JiQmZueGFFOHNtdEh1dmVaRW03NFhpOUoxZUFubE92d3o2SnFLcFJMNCt1Wi9McHFCTDhqMkFmdlVhZGdpd091UmNBbTFjMjVaNHY2bkFKTmUyeHJTT053YXl4VUFweFhMWjEiLCJtYWMiOiIwYzUxYjIzNDYzYzYxMDBhNDZmZTRjMzdjMjFlZjIzYjM5MzU1YjhiNTkzNzk3NjA1MTk4MzQ4NmY3MjExNzFhIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 00:38:12 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6IjRLUnE0Vm9CSDJEZHl6b0lDMEVjQnc9PSIsInZhbHVlIjoiMWtMdFk3azVYOWxmb1djamFnbDRvQ1FxK1hJc3Foa2dxaWNuR1ExRjFHNFlYbmNxL3R4UGZmeVBQd05wNlNzY3E1MG1VV2NpdEIwcEwyWWlQMk95VHE5VzBrbUVSeXZxcEJFM0JYc29wdTBuK3Z3S09vRVBnVXNnNFRER3ZnRTBFcHZqRkdudkMwYWU4bmE4YlNYT0VJREExYkQzODNKOGswRFhKdmtybVZ0Y2RELzZGa0taRks3MjAwUjY1VmFOK1JDeGs0QXlBbXdrLzdHRjM1V2lxTkZKUlJ2S1owV1JSRGdBcHBsd1kwbjdhbjRVbjlTdGtkRjJ3Wms0Y3dyTU9acWJnL3lMci9vZHpjeld5ZFhBMnBNaHhpa1MxYzlwWkNTVmJVM0IxQmxmcDJ2dEN4bUlmR25HbVg2K25yUVBNQWZFc3NzWmtUbkFlOHdCRyszeW5MUHFxR0lJYWcxNWswQ01SeDFwNjMvN2xOMjQzdUI0bHlvQTNFQnM2Z1plb1hpQm8wcmloRFVwbWNGVk4xN1liR3NRRGZxakJOeGxjN2dHbWFjZjJRVVduUUNmL0ZLMkdtV0xIVjJrVW5qTjc2b0FxbWZvcHJ0T3p4NVJ5TjBUcGoyMmlmdXJGMld5OVpzajdsVWIrZiszU3d3TUVWeklZdDRFbFB2MjBiME4iLCJtYWMiOiI4M2QzNmMyMWRkNzJmZDlkMTM4N2NkZTJjYmY2YzUyM2IxZjFjZjEyYTcxZWE0MjIzN2I1NDA1NzdlODBlZDAxIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 00:38:12 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IjlaZEpxNnNXdUxlYnRaTFNRaE5KWkE9PSIsInZhbHVlIjoiRk1xeW50dUJnUGxTclJWOTNSajVzV1duaUgvZndraE9mNXVuZnJxY1NjM2pJR2VCWEZDa2pRbzBGbVd1NmlGOXdPbEVDQm04WTloN2JVcUZ0SWYrNXBYQ05tWHVBcGMzaFNwYUdValpmbkJyZ09xYUZaaDBPenE5T2dLL3U1ZXZUOEM1M3QxR0h3M2VWL2VNc015bTFhV2cwTUM0SjJJTFVwTXp1djFJeEJhRXRlSlBQeitlQzY5ZFkvYmltMHBjZVZnQnI0UGwyM2kyMlNXczRTTUVlaW1Ea21wcjJnR3duV055ZHpnSmdndjBNOUR3TExmc1hjOFFGUzBNaWR5bjM3TmJhUDdMYU5uUUJWZVRIeTZkKytXWjk1WmI5cXFyZS81a0swVmlGdnJrZjZRaXhjNHBEdzRIb1ZFazFDSmlCc01xSGYrMlQ3TkltUHZIa09qZkgvRjA0MnFuYjZFYWxqVE41cDJtMFptUCtTeHFCdnB4Wm00UzNqbG1pMzN0ZnAyYkJKWDZiQ0c2WFdoRGpGcVNsMEFOZk5mS1JiQmZueGFFOHNtdEh1dmVaRW03NFhpOUoxZUFubE92d3o2SnFLcFJMNCt1Wi9McHFCTDhqMkFmdlVhZGdpd091UmNBbTFjMjVaNHY2bkFKTmUyeHJTT053YXl4VUFweFhMWjEiLCJtYWMiOiIwYzUxYjIzNDYzYzYxMDBhNDZmZTRjMzdjMjFlZjIzYjM5MzU1YjhiNTkzNzk3NjA1MTk4MzQ4NmY3MjExNzFhIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 00:38:12 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6IjRLUnE0Vm9CSDJEZHl6b0lDMEVjQnc9PSIsInZhbHVlIjoiMWtMdFk3azVYOWxmb1djamFnbDRvQ1FxK1hJc3Foa2dxaWNuR1ExRjFHNFlYbmNxL3R4UGZmeVBQd05wNlNzY3E1MG1VV2NpdEIwcEwyWWlQMk95VHE5VzBrbUVSeXZxcEJFM0JYc29wdTBuK3Z3S09vRVBnVXNnNFRER3ZnRTBFcHZqRkdudkMwYWU4bmE4YlNYT0VJREExYkQzODNKOGswRFhKdmtybVZ0Y2RELzZGa0taRks3MjAwUjY1VmFOK1JDeGs0QXlBbXdrLzdHRjM1V2lxTkZKUlJ2S1owV1JSRGdBcHBsd1kwbjdhbjRVbjlTdGtkRjJ3Wms0Y3dyTU9acWJnL3lMci9vZHpjeld5ZFhBMnBNaHhpa1MxYzlwWkNTVmJVM0IxQmxmcDJ2dEN4bUlmR25HbVg2K25yUVBNQWZFc3NzWmtUbkFlOHdCRyszeW5MUHFxR0lJYWcxNWswQ01SeDFwNjMvN2xOMjQzdUI0bHlvQTNFQnM2Z1plb1hpQm8wcmloRFVwbWNGVk4xN1liR3NRRGZxakJOeGxjN2dHbWFjZjJRVVduUUNmL0ZLMkdtV0xIVjJrVW5qTjc2b0FxbWZvcHJ0T3p4NVJ5TjBUcGoyMmlmdXJGMld5OVpzajdsVWIrZiszU3d3TUVWeklZdDRFbFB2MjBiME4iLCJtYWMiOiI4M2QzNmMyMWRkNzJmZDlkMTM4N2NkZTJjYmY2YzUyM2IxZjFjZjEyYTcxZWE0MjIzN2I1NDA1NzdlODBlZDAxIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 00:38:12 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-881495695\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1689942925 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">iD3eOztlFQTk9Wu93brk2PH5kNLllwdWSLzbHLjf</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"25 characters\">http://localhost:8000/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>16</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1689942925\", {\"maxDepth\":0})</script>\n"}}