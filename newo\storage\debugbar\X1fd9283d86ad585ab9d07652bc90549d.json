{"__meta": {"id": "X1fd9283d86ad585ab9d07652bc90549d", "datetime": "2025-06-07 23:52:15", "utime": **********.976151, "method": "POST", "uri": "/chats/getContacts", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.372527, "end": **********.976175, "duration": 0.6036481857299805, "duration_str": "604ms", "measures": [{"label": "Booting", "start": **********.372527, "relative_start": 0, "end": **********.872453, "relative_end": **********.872453, "duration": 0.4999260902404785, "duration_str": "500ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.872469, "relative_start": 0.4999420642852783, "end": **********.976178, "relative_end": 2.86102294921875e-06, "duration": 0.10370898246765137, "duration_str": "104ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45051464, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/getContacts", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getContacts", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "contacts.get", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=333\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:333-390</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.029079999999999998, "accumulated_duration_str": "29.08ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.9127982, "duration": 0.026789999999999998, "duration_str": "26.79ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 92.125}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.952976, "duration": 0.0009599999999999999, "duration_str": "960μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 92.125, "width_percent": 3.301}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (15) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.96302, "duration": 0.00133, "duration_str": "1.33ms", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 95.426, "width_percent": 4.574}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "cN6pwMHluqMWM9hLaRtOtRasnQF8pfN3ghxjsOfa", "_previous": "array:1 [\n  \"url\" => \"http://localhost/financial/productservice\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/getContacts", "status_code": "<pre class=sf-dump id=sf-dump-907347772 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-907347772\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1528236876 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1528236876\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-55046798 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">cN6pwMHluqMWM9hLaRtOtRasnQF8pfN3ghxjsOfa</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-55046798\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1984549112 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">http://localhost/financial/productservice</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1995 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=a091af98-270d-406e-ab7e-33a6e610d6e2414686; _clck=nups8l%7C2%7Cfwk%7C0%7C1960; _clsk=1dgl8io%7C1749340332464%7C17%7C1%7Ck.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IlBVSEZZSkNYeldvOTMvZ3Z2eGNnSmc9PSIsInZhbHVlIjoiclRPa1lqYWhUd1pzc050dVJiYlBvQW54MTB0K1Q2QmpaWWFyYzNMNS8xa05hUUVLL0d6b2YwL0xOaWZiOExuU0NaVlU4cFJGWUFLY2wzYVowNXJMdjNKNCtZNWVSSWYxRUl0V2doc2x6dmRSZVEzU0t6Y2NZUUdidVdZMFJQbWFRTEpmdDVaVWo0L1ZwNW0zVHM1b2VHTXpIN244RnBXUGFBOXA5UWFZZWMxNk1rT2VhS210Vk02VnZEWXM5VDhKa1N2ZE5razM3RTJuRHV0cUlmdFlkZzZiTzRxZ3l2cnI3Qm9Jc2EyREJZaG9BU0tBbFhLemtBS25YY3lmc2pmbWMrZWJHTGdhdkQ1aWUxSUk4ZVFxOWJ1cXBVM05PbFZWVGovSzQxS1dkQmcvV0tvVWxMNGhLZFFpcUJHRHlkOWV3dUFyUzJjZ21BaFFacENmekRuYWFtTnVuV3VrS1RSeTdsRTZTQ3lxOEd5RjF6VkFKZ3ViVkx6Y2s1bzUzdEN6YkF1VFROcUdYalBLcVhwS2hlQ2tqRGJJWndieGF0ZDFzQ3NWKzMxYmdTS3Boc0U3VjJQSUwzbDloMHFhaXh0aVFXaFV0VDlSbDNpTytlYTJXK1JLbFBTZFhaV3NIOTl3c3RubEtkOEVZa2tqZzFWV0RIQmFUVmIweEhTMzBnMzgiLCJtYWMiOiJlM2ZjZjU4NzUwNmJiYjJmOWExOTczNGNlODkyN2U5MWRlZTA3NjA3OGUyMGQwMTRiYzIzZmVmNGE4OGE2NzYwIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6ImUwcER2NW5nZUgzaCtRL2NxdkVnTFE9PSIsInZhbHVlIjoibWp0UFczdklCOUZIMnFBRFl6Nm9IQ3JJZklzVFRHcFZReEhwc0xTQjREZU1zdFpuVXRra3J4UGZ2SW4zREhnTlJzNFZjUklmbkVXUCtPdVRsN0FZS2E2dnlTa0Ixak4yL2NPbG1RRzlEMjFBbnM2bkczSnk4WGJEVGdZaUo0SkZuQ0I1QW9UaFJpRXhWR2orS0VYeFNjeTVVV1RwS1YvM21VVW1SeGRVcXI5ZnhLZW9qMEhMZHU1VGtHNS95THhneDFKZ1VCT1VZVnRxRjBTVWZHZGdVNkh6YWFjby91eUFqZC9CdmREWHNTa2FrS282alJCZHQ5U2IvTHg3aEdJSU1Xd0NDa2hGTFgvc1hMb3U5SXEwR3JHN3dXZEZsd2RTUThtSytUclFiUVlSSGRZVWJ3ajVXTXd4YnhVTXdQaDZvczlYS0I5U3BveFJxYXRraGNJUjRTR3RZTGdjS25Xc2d3RU9yeVFBWHV5YWhoakw2ZngwOWJuNTJna3NBeUZmbURkbzI2NVYrM0NUcUxSbnp1QnhLNmdBWC9FK3dSQ3ZZaTA5VVBvaURaM1ZkZWUzdEYrTEdOU0tsKzhiZ3FXZjRHTjJnWW9RbzcxeG1mNFVnVVlOR2dSQlVKQ2ZwbFJrMUo4WWdYT3ltbnFDNU5TSlFUbW1paUFLV2l3ZW5jbU4iLCJtYWMiOiJiYWFhZTEzNjg5ZTM0OGUwZWM4MDE3M2IzYjBiNTM3Mjg0ZjU0OTM3Mjc4YTY4MmU3ZWNmY2NmMDMxNDM4OGJlIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1984549112\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1419108069 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">cN6pwMHluqMWM9hLaRtOtRasnQF8pfN3ghxjsOfa</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Eo35AMmp3Bkf2zsyHLroae0N6MdUih7xJ0ojLwSF</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1419108069\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1646158668 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 07 Jun 2025 23:52:15 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IlR1c0FveWJkb0JBTlRkRVdrNnhTMWc9PSIsInZhbHVlIjoibDhmdnhId3JCQ2MwaE5qUmoycC92WCtXK2N5T3o1YTFxZFd5TXBtQjhqeC9makV4M3FCV1FRTzFSVG9Kbnp5dmRjNU9zYVI3bUNGajA2SGtGcVZjN1d4R1YyT2FGNWFFd1RLSWF2K01MZzh0M3ZjMzgwR2l0TlZoRzRrNTA4VHNWVW83SWRBM0hEN3g0NnFsMnhrcXhYWnhhck9xNnlKU21KVzJaVjA4WUxNZkE3ZFBQVWI3UlYwOExTU2plTWVTU2w3Y1BKeUlsZnBSZ2FjTWsrMjB1T29Bem53SEFUUmhldjZFakp2UWpTZUw0TmFla0hLeitNS3BERDc3SzR1VVRDZkNLY3J6OFlnL2lGdlphbUIzQ3E0WS9YR2YwYTZ5RkVPcVZPTFhyajc4Y213WDNRdlN3NUxvOFI5NS9LNjM2L1dMWVZ2aFl5ekNwdDAwTWlnaWFMaS85U2I2eDlMNTNoSHVpYjdzTkhPbDRXb01NaXk4ajhqSjNPdlUrcTZwWUdnOGY1OWlxNWg3dldRblFFVWtQWldRYjQ0U0dnSERQSE5CUVhZa2dtMnFGajUxRHE2bDlUL2lPcldtNTkxVGY5SEJMZk5vZU5iODREYUNsNWZZcWdBVDZuVUllR0ljUDFJL2RMV3FoR1ovc2RldTZabzlSSi9meUdjR1d0T0QiLCJtYWMiOiJhNzkxODEzYTI3YzcyMmFmNzAzNjI1NmI4MjZjZDgyZjljOTUwYzhhNjI1YjRhNDgxZmUyMjc0ZDZkYjgyNmI3IiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 01:52:15 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6InZ5amNKanRVR2tlN0JpUm1Ld1dOdkE9PSIsInZhbHVlIjoieDVQcnRhUTB6RWdhL3dnaDdnZERXaFNCdkhhWmNNNW5DcER6NEt4N2VPYWNubFR0M0xUUVducFYzS1RFV0dCY0lxa2RxR0w4cXlGR2NKS0dnRmFHb3F4OFR6NDZOQ28vczNabWg0Nld6SC9YM2l0RjJXSmNTUlFldE4vWnNzSDJxMUplUUEwSDNxWXFMKzBhbExyek1lQXUrZTRZMzBrTGZaZ1IzR1BpWGxQanpqVDVvZGxWblBTSklJa1ZFQkRXZGtxNHBMR2xGVGhsUTAvTzJBZHF1cWpPaHdiTlM3Q2VNbEV2L01CUHNIUDc4MFhhU3J0STcwQzhVdjZaUG1uc0RMODdwdmd3aVBMUlRhZ09IaUlWNGFTV2J6bTJWaHY4TEhKRmlOMmtNdGF6dWltYURyYkdXSVlyTHlZTkZmNzlqdzUzdEpWYldVc3RTbC9xbXFIUGFwWXZxNWNnZHBVYTBtUlhWQmlTOHF6SzBUcitmMHVwMkIxSTFJM2RvOHBtcjZTemJlYTU1UkM5OXo0c2FpUGN4V3p2MVk2NVh1WDNLV3lnQ044UXZIR21lejFkOHZ5QjZWaDNQSTZ5aHNTRExYNmkrOFV4RE1rT2QrZVhQNnUyNkpKcTQ1L2N3endIbTMxU1N1K3NCcUVFOS90TnUzOVZTd3lDcm1nTS9OYXUiLCJtYWMiOiJmZjQ3MTk2ZDM0OTg2NzA3MjkxODUxYjhkMzE5M2E1MzAwZGQ1NWMwZDk1N2NhYWYxM2YyNzVjNzM4NzM1MWQ1IiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 01:52:15 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IlR1c0FveWJkb0JBTlRkRVdrNnhTMWc9PSIsInZhbHVlIjoibDhmdnhId3JCQ2MwaE5qUmoycC92WCtXK2N5T3o1YTFxZFd5TXBtQjhqeC9makV4M3FCV1FRTzFSVG9Kbnp5dmRjNU9zYVI3bUNGajA2SGtGcVZjN1d4R1YyT2FGNWFFd1RLSWF2K01MZzh0M3ZjMzgwR2l0TlZoRzRrNTA4VHNWVW83SWRBM0hEN3g0NnFsMnhrcXhYWnhhck9xNnlKU21KVzJaVjA4WUxNZkE3ZFBQVWI3UlYwOExTU2plTWVTU2w3Y1BKeUlsZnBSZ2FjTWsrMjB1T29Bem53SEFUUmhldjZFakp2UWpTZUw0TmFla0hLeitNS3BERDc3SzR1VVRDZkNLY3J6OFlnL2lGdlphbUIzQ3E0WS9YR2YwYTZ5RkVPcVZPTFhyajc4Y213WDNRdlN3NUxvOFI5NS9LNjM2L1dMWVZ2aFl5ekNwdDAwTWlnaWFMaS85U2I2eDlMNTNoSHVpYjdzTkhPbDRXb01NaXk4ajhqSjNPdlUrcTZwWUdnOGY1OWlxNWg3dldRblFFVWtQWldRYjQ0U0dnSERQSE5CUVhZa2dtMnFGajUxRHE2bDlUL2lPcldtNTkxVGY5SEJMZk5vZU5iODREYUNsNWZZcWdBVDZuVUllR0ljUDFJL2RMV3FoR1ovc2RldTZabzlSSi9meUdjR1d0T0QiLCJtYWMiOiJhNzkxODEzYTI3YzcyMmFmNzAzNjI1NmI4MjZjZDgyZjljOTUwYzhhNjI1YjRhNDgxZmUyMjc0ZDZkYjgyNmI3IiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 01:52:15 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6InZ5amNKanRVR2tlN0JpUm1Ld1dOdkE9PSIsInZhbHVlIjoieDVQcnRhUTB6RWdhL3dnaDdnZERXaFNCdkhhWmNNNW5DcER6NEt4N2VPYWNubFR0M0xUUVducFYzS1RFV0dCY0lxa2RxR0w4cXlGR2NKS0dnRmFHb3F4OFR6NDZOQ28vczNabWg0Nld6SC9YM2l0RjJXSmNTUlFldE4vWnNzSDJxMUplUUEwSDNxWXFMKzBhbExyek1lQXUrZTRZMzBrTGZaZ1IzR1BpWGxQanpqVDVvZGxWblBTSklJa1ZFQkRXZGtxNHBMR2xGVGhsUTAvTzJBZHF1cWpPaHdiTlM3Q2VNbEV2L01CUHNIUDc4MFhhU3J0STcwQzhVdjZaUG1uc0RMODdwdmd3aVBMUlRhZ09IaUlWNGFTV2J6bTJWaHY4TEhKRmlOMmtNdGF6dWltYURyYkdXSVlyTHlZTkZmNzlqdzUzdEpWYldVc3RTbC9xbXFIUGFwWXZxNWNnZHBVYTBtUlhWQmlTOHF6SzBUcitmMHVwMkIxSTFJM2RvOHBtcjZTemJlYTU1UkM5OXo0c2FpUGN4V3p2MVk2NVh1WDNLV3lnQ044UXZIR21lejFkOHZ5QjZWaDNQSTZ5aHNTRExYNmkrOFV4RE1rT2QrZVhQNnUyNkpKcTQ1L2N3endIbTMxU1N1K3NCcUVFOS90TnUzOVZTd3lDcm1nTS9OYXUiLCJtYWMiOiJmZjQ3MTk2ZDM0OTg2NzA3MjkxODUxYjhkMzE5M2E1MzAwZGQ1NWMwZDk1N2NhYWYxM2YyNzVjNzM4NzM1MWQ1IiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 01:52:15 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1646158668\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1218564739 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">cN6pwMHluqMWM9hLaRtOtRasnQF8pfN3ghxjsOfa</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"41 characters\">http://localhost/financial/productservice</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1218564739\", {\"maxDepth\":0})</script>\n"}}