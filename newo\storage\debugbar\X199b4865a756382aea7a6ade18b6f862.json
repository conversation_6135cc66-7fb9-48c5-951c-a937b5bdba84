{"__meta": {"id": "X199b4865a756382aea7a6ade18b6f862", "datetime": "2025-06-07 22:18:03", "utime": **********.162929, "method": "GET", "uri": "/account-dashboard", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.67392, "end": **********.162965, "duration": 1.****************, "duration_str": "1.49s", "measures": [{"label": "Booting", "start": **********.67392, "relative_start": 0, "end": **********.944683, "relative_end": **********.944683, "duration": 1.****************, "duration_str": "1.27s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.944705, "relative_start": 1.****************, "end": **********.16297, "relative_end": 5.0067901611328125e-06, "duration": 0.*****************, "duration_str": "218ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": ********, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET account-dashboard", "middleware": "web, verified, auth, XSS, revalidate", "controller": "App\\Http\\Controllers\\DashboardController@account_dashboard_index", "namespace": null, "prefix": "", "where": [], "as": "dashboard", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FDashboardController.php&line=81\" onclick=\"\">app/Http/Controllers/DashboardController.php:81-181</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.01647, "accumulated_duration_str": "16.47ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.05417, "duration": 0.00597, "duration_str": "5.97ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 36.248}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.09227, "duration": 0.0014299999999999998, "duration_str": "1.43ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 36.248, "width_percent": 8.682}, {"sql": "select * from `migrations`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/rachidlaasri/laravel-installer/src/Helpers/MigrationsHelper.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\rachidlaasri\\laravel-installer\\src\\Helpers\\MigrationsHelper.php", "line": 29}, {"index": 14, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\XSS.php", "line": 40}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 16, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.127309, "duration": 0.00907, "duration_str": "9.07ms", "memory": 0, "memory_str": null, "filename": "MigrationsHelper.php:29", "source": "vendor/rachidlaasri/laravel-installer/src/Helpers/MigrationsHelper.php:29", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Frachidlaasri%2Flaravel-installer%2Fsrc%2FHelpers%2FMigrationsHelper.php&line=29", "ajax": false, "filename": "MigrationsHelper.php", "line": "29"}, "connection": "ty", "start_percent": 44.93, "width_percent": 55.07}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "cN6pwMHluqMWM9hLaRtOtRasnQF8pfN3ghxjsOfa", "_previous": "array:1 [\n  \"url\" => \"http://localhost/account-dashboard\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "1", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/account-dashboard", "status_code": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:17</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">max-age=0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/login</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1993 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=a091af98-270d-406e-ab7e-33a6e610d6e2414686; _clck=nups8l%7C2%7Cfwk%7C0%7C1960; _clsk=ij6lzq%7C1749334667398%7C2%7C1%7Ck.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6Ik4xNXVvVHF2M2R5MXVJUTlMTWtqakE9PSIsInZhbHVlIjoidW5wcGIwVkdLOUVFUGh5YzRydkM1Qmx0Y1JlSHdSQ1h6dDI2cWpDSlNZNWVlQjdFNnV0OGpwSWZnWnh4WU1YVUl3NDdHNmI2dGl0bEpMS3A2MjVGTlc1aTRBQlBJMjNyQUpPbW5wRDF3RXk5cWc5MVJ1b0lEMC80clRXaHlBYjVaOE5ramV0Vit1bWJ5TExzSUlnaHFaK0lDTEIzREFSRTgwVFRJS3ptMDV5TmNxcVZxdXN3WTFxRGxjQjk0QUQxcnlFZ3V0MnhTd0xtZUNqeEdxMXh5ZHRvdHR1MldDWGRqYWFqVk9kdzEyOEpTK283WjJMeWEzZHZQcjBiT29kSXIraWVXaDJmYzBEcWZ3WjR3aVkrRE8vWWIzMWY5anN0YThJdGhCY2oyejE0ZDlzZHNhelVzSTBGaWN1WHBqVDVZNjh5RVI5WElBQ0NIclFxZDlxQm5BL1c4Y3hPMGRUUXlOOFphbnA1SEhGbExrZ3VCVUtjQXZmT28yRHBMaTlNRkNzQkpZL1FTelVHeWNHRENvbkxKQ0dHc2ZjN0hCZjJCYzJnNzlPcUhzNzgwN2JDS3VqR1hLWkQ0V0JsY2x3Y1NzSHVnamI0a3JLaTE0QndrTXV6WXgwWGRla1dBVG41b2UzV2tqLy96WWozd1ZiUE9QTnBGQUxmb3RaVldMNG4iLCJtYWMiOiI3YzJkNDM4ZmYyOTAwNDRiOTc5OTI5ZDRmYjNiYzAxZDIzYzU3M2I4NDJiMmEzZWJhYTVmZTNiYzE5NDdjZmYzIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6IjFvMFJBUkROWC92U0ppT2JHTmFaQ1E9PSIsInZhbHVlIjoibTlNWFc4UE42aUxaTjlDeEUxdDhKbFNlYW9LQkh5eXk0cVNwT2tGcmpSYVBsNEdyU1Q5WmFNWWRnTnQvRUV2Z3ZDY1B1cytMclRrcFYrb3hFMmdZNThjbXZLNDk3Y1NoZllwTldjKzczT0htYU9jak13cG5sRkZMZi9vMVdNMkk5eWNvUVFqbGhvb3lvL1VxTXV1S2xDQko1M2Z2QVcraEw1cDh5QjNYNnl0MVN5cmJxSzZWZFNGaTltYk4vWjBMbnZiMitFbkdwYkY0UytjTUZ2UEtIc3NaK210T3JKRjNmVEFjYVNBSVVQNlUxaWU1WTF0R3JCTEJpZXdIb0NPV0tkUVFxY0x2WnhmUGtKTG5PYUhrRmhFdU1WTWtPQ0ZlbUNzWlNQeW95emR6TlltQ1E2cHZuZUdVL3hsL0xPZzBiQmRuUTRZdW5uMjRZTy9RTUJTOTBQeWRmWEd5QnVsOTZkTjZBVmJPY0dBang2bDRkSHQzVXhuR2UvblJnRG5YOEhoVWlndEs1c3NGcG9DcXR0REhQNTgwd09wTXA3YnNabUVnc2JtTEVIbzlSb3krcXJqbHd1UW5ER2xuMExxaGszeXBKWGFqTDVMN0hRVDcyTjdYazVLMWp1U01wT0RheFk2a0NMRnVBMGNuTXdsUVRZdFBTb2NFVnJXNWFHanEiLCJtYWMiOiIwMzY1OGE4NmQ3MGJlOGRiYjEwNjVkNjhkMTVjODQzMDZmNjg4M2ZkNmE4NjJhMDc5ZjJlZDYyZmYyMjE5NmNiIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-655047366 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">cN6pwMHluqMWM9hLaRtOtRasnQF8pfN3ghxjsOfa</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">PmFRgUqysTWH0qB2ROlhITKoLIETZGW2KykVXtOx</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-655047366\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1812163992 data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 07 Jun 2025 22:18:03 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"26 characters\">http://localhost/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-methods</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">POST, GET, OPTIONS, PUT, DELETE</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-headers</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"66 characters\">Content-Type, Accept, Authorization, X-Requested-With, Application</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Im5Xa0NFZU5UMUdjd3ZWbmVKQldaOUE9PSIsInZhbHVlIjoiR3pxejFxQ1R0bjdqR2ZtdWgxL2FMY0s4NSs0RUxGOTFUb3VpdWxWbDdTZ2JoQUljS3RHMlNJU0Evblg4am5RNThDVXR6ZGFnQ25pQWduaEpHVHJ4VE11ZmUwTHcra0twajVMM2xZeWczcW05N0hlZFA2TE5FLzdaSnViRHZaVjNvUHZ4THFmQnlKdVd3NHRvMnVPaUdIeVhzU0FqUmgzQVBndG1wcExDeExyTUxhdkJNUHNzb3ljeFFZdFJ0UzlmM3IvOC90RnllUFRJMmVOK2tFOXZqWUxDQ2pzMk9JTVlzczhlZlk0ZEc4WmIzWm9DL0VqQklmTWl2WkEwYVR0aENsM1NkU21BcjBKY3JpV2prZU1sb2FXOWRlYnAwYm4ySWZ2bzRwMTBnQlNpNTNBelRXVUlDTEdwZDZnZFFnemdUMTJzSW9UcTlKZWRudnNoM2F2RStrdU5FQ3lxMGpjUUROWlNCdUtHakdRU1MwSjJTMURVLzIwMENPVDBXT1NTNHpkTlJkalB1aCtFOGdrdXRvTThPckxIZGZZaWlXUEdDc3AxQUk0V29kcDRRbDR4ZlUyWlpLVDNEOXFCYnA3b0NyZ1hBL0cvL3FlYUhlRDJCS09zcmpiTXFLWktVeHUwZHEvQmRQNEc0OGtZdit3MEVvWFc2MVlIQThwSXJ6NmkiLCJtYWMiOiJhNDNiYzFmYmI4ZGQ5NDkxNWY4MDkwZWYwOGZjZTZmYjI5YTdkNTg0ODcyZjkzMTVjNzRlYTJjOGU3ODdiNGJkIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 00:18:03 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6IlZ1aGloRUlpclhLNXZLOC9rcXdjYVE9PSIsInZhbHVlIjoiL0FvSzhibURCeDdaKzFMd0ZoN3luclZWbFZPSXZxSzE3RGlBN1ppUm5aYk1NNGdNSThwV3N6RC9qK0pZR0ZoSGR6OVZPQmt0aU1oaXpVblFxcEdSSEk4RklvNkpqOWthbWsrL3JNRkZNSklnZlFHalNacFFHMGlRamZHeFBTRkE2YSt4Q3hTaU9aUmo4K0J1YmFCeHVhN3g1dWlOcW10V1RkTFFZSmRIaDZmNWNOemlCWEVDMDl6UUZoZWtHU2Y4b3BucTFvYXBZTU1JTmpIT2tEam1vRnZHdDVwOUdxam5qQzh3ZE1sR2dTaTVaZGoxdldiRmExSGJaSll0WlBZSVZFQUloZWRReXNoMW1LUGtFalRIK1lIV3VHL0E0RWRUNnJRTlBncGkyY2RkOWRZUGRVbDRYR0t6VTdnR1dOMFJSNWRyOVBzZjAvVi8rSEdaWURHTUZvR3ZhcTZPOUdjZjMvb1QyN0ZSakJoMFI0VU5ub2FwTXdiSHF1b3piblJXSTlyajEvL0pVZThlYjh2WFlIaDY5T2Vjek5ESTlHV0VmVmlvL3lVcTJrUjBZL2U1eEx6MjVkWkdsdEdWV1ZDd1VPL01XNjRHTUFFNnJGSi8yTUJPeHgyR0tOZ3JyZUlUemtWWHd0U2ZNNGtPc1Iva21JeWxGMmJvSzlHN00rRUkiLCJtYWMiOiI3MDA4NDQwM2Y0NjVmZjhjNzk1ZTAxMzVkMmRjMjU3ZjhhNTg3YmZlM2ZhOTgyOTE2MjgyZGIwYjA4ZDYxNjQ4IiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 00:18:03 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Im5Xa0NFZU5UMUdjd3ZWbmVKQldaOUE9PSIsInZhbHVlIjoiR3pxejFxQ1R0bjdqR2ZtdWgxL2FMY0s4NSs0RUxGOTFUb3VpdWxWbDdTZ2JoQUljS3RHMlNJU0Evblg4am5RNThDVXR6ZGFnQ25pQWduaEpHVHJ4VE11ZmUwTHcra0twajVMM2xZeWczcW05N0hlZFA2TE5FLzdaSnViRHZaVjNvUHZ4THFmQnlKdVd3NHRvMnVPaUdIeVhzU0FqUmgzQVBndG1wcExDeExyTUxhdkJNUHNzb3ljeFFZdFJ0UzlmM3IvOC90RnllUFRJMmVOK2tFOXZqWUxDQ2pzMk9JTVlzczhlZlk0ZEc4WmIzWm9DL0VqQklmTWl2WkEwYVR0aENsM1NkU21BcjBKY3JpV2prZU1sb2FXOWRlYnAwYm4ySWZ2bzRwMTBnQlNpNTNBelRXVUlDTEdwZDZnZFFnemdUMTJzSW9UcTlKZWRudnNoM2F2RStrdU5FQ3lxMGpjUUROWlNCdUtHakdRU1MwSjJTMURVLzIwMENPVDBXT1NTNHpkTlJkalB1aCtFOGdrdXRvTThPckxIZGZZaWlXUEdDc3AxQUk0V29kcDRRbDR4ZlUyWlpLVDNEOXFCYnA3b0NyZ1hBL0cvL3FlYUhlRDJCS09zcmpiTXFLWktVeHUwZHEvQmRQNEc0OGtZdit3MEVvWFc2MVlIQThwSXJ6NmkiLCJtYWMiOiJhNDNiYzFmYmI4ZGQ5NDkxNWY4MDkwZWYwOGZjZTZmYjI5YTdkNTg0ODcyZjkzMTVjNzRlYTJjOGU3ODdiNGJkIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 00:18:03 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6IlZ1aGloRUlpclhLNXZLOC9rcXdjYVE9PSIsInZhbHVlIjoiL0FvSzhibURCeDdaKzFMd0ZoN3luclZWbFZPSXZxSzE3RGlBN1ppUm5aYk1NNGdNSThwV3N6RC9qK0pZR0ZoSGR6OVZPQmt0aU1oaXpVblFxcEdSSEk4RklvNkpqOWthbWsrL3JNRkZNSklnZlFHalNacFFHMGlRamZHeFBTRkE2YSt4Q3hTaU9aUmo4K0J1YmFCeHVhN3g1dWlOcW10V1RkTFFZSmRIaDZmNWNOemlCWEVDMDl6UUZoZWtHU2Y4b3BucTFvYXBZTU1JTmpIT2tEam1vRnZHdDVwOUdxam5qQzh3ZE1sR2dTaTVaZGoxdldiRmExSGJaSll0WlBZSVZFQUloZWRReXNoMW1LUGtFalRIK1lIV3VHL0E0RWRUNnJRTlBncGkyY2RkOWRZUGRVbDRYR0t6VTdnR1dOMFJSNWRyOVBzZjAvVi8rSEdaWURHTUZvR3ZhcTZPOUdjZjMvb1QyN0ZSakJoMFI0VU5ub2FwTXdiSHF1b3piblJXSTlyajEvL0pVZThlYjh2WFlIaDY5T2Vjek5ESTlHV0VmVmlvL3lVcTJrUjBZL2U1eEx6MjVkWkdsdEdWV1ZDd1VPL01XNjRHTUFFNnJGSi8yTUJPeHgyR0tOZ3JyZUlUemtWWHd0U2ZNNGtPc1Iva21JeWxGMmJvSzlHN00rRUkiLCJtYWMiOiI3MDA4NDQwM2Y0NjVmZjhjNzk1ZTAxMzVkMmRjMjU3ZjhhNTg3YmZlM2ZhOTgyOTE2MjgyZGIwYjA4ZDYxNjQ4IiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 00:18:03 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1812163992\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-19******** data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">cN6pwMHluqMWM9hLaRtOtRasnQF8pfN3ghxjsOfa</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-19********\", {\"maxDepth\":0})</script>\n"}}