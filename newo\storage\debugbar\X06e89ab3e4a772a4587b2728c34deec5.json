{"__meta": {"id": "X06e89ab3e4a772a4587b2728c34deec5", "datetime": "2025-06-07 23:52:15", "utime": **********.976527, "method": "POST", "uri": "/chats/favorites", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.37459, "end": **********.97656, "duration": 0.6019701957702637, "duration_str": "602ms", "measures": [{"label": "Booting", "start": **********.37459, "relative_start": 0, "end": **********.870849, "relative_end": **********.870849, "duration": 0.4962589740753174, "duration_str": "496ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.870871, "relative_start": 0.49628114700317383, "end": **********.976564, "relative_end": 3.814697265625e-06, "duration": 0.10569286346435547, "duration_str": "106ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45054800, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/favorites", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getFavorites", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "favorites", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=454\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:454-477</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.028470000000000002, "accumulated_duration_str": "28.47ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.9126952, "duration": 0.02696, "duration_str": "26.96ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 94.696}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.9531128, "duration": 0.00093, "duration_str": "930μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 94.696, "width_percent": 3.267}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (15) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.964304, "duration": 0.00058, "duration_str": "580μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 97.963, "width_percent": 2.037}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "cN6pwMHluqMWM9hLaRtOtRasnQF8pfN3ghxjsOfa", "_previous": "array:1 [\n  \"url\" => \"http://localhost/financial/productservice\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/favorites", "status_code": "<pre class=sf-dump id=sf-dump-376295603 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-376295603\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1961206588 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1961206588\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1755860503 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">cN6pwMHluqMWM9hLaRtOtRasnQF8pfN3ghxjsOfa</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1755860503\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1459323107 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">http://localhost/financial/productservice</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1995 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=a091af98-270d-406e-ab7e-33a6e610d6e2414686; _clck=nups8l%7C2%7Cfwk%7C0%7C1960; _clsk=1dgl8io%7C1749340332464%7C17%7C1%7Ck.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IlBVSEZZSkNYeldvOTMvZ3Z2eGNnSmc9PSIsInZhbHVlIjoiclRPa1lqYWhUd1pzc050dVJiYlBvQW54MTB0K1Q2QmpaWWFyYzNMNS8xa05hUUVLL0d6b2YwL0xOaWZiOExuU0NaVlU4cFJGWUFLY2wzYVowNXJMdjNKNCtZNWVSSWYxRUl0V2doc2x6dmRSZVEzU0t6Y2NZUUdidVdZMFJQbWFRTEpmdDVaVWo0L1ZwNW0zVHM1b2VHTXpIN244RnBXUGFBOXA5UWFZZWMxNk1rT2VhS210Vk02VnZEWXM5VDhKa1N2ZE5razM3RTJuRHV0cUlmdFlkZzZiTzRxZ3l2cnI3Qm9Jc2EyREJZaG9BU0tBbFhLemtBS25YY3lmc2pmbWMrZWJHTGdhdkQ1aWUxSUk4ZVFxOWJ1cXBVM05PbFZWVGovSzQxS1dkQmcvV0tvVWxMNGhLZFFpcUJHRHlkOWV3dUFyUzJjZ21BaFFacENmekRuYWFtTnVuV3VrS1RSeTdsRTZTQ3lxOEd5RjF6VkFKZ3ViVkx6Y2s1bzUzdEN6YkF1VFROcUdYalBLcVhwS2hlQ2tqRGJJWndieGF0ZDFzQ3NWKzMxYmdTS3Boc0U3VjJQSUwzbDloMHFhaXh0aVFXaFV0VDlSbDNpTytlYTJXK1JLbFBTZFhaV3NIOTl3c3RubEtkOEVZa2tqZzFWV0RIQmFUVmIweEhTMzBnMzgiLCJtYWMiOiJlM2ZjZjU4NzUwNmJiYjJmOWExOTczNGNlODkyN2U5MWRlZTA3NjA3OGUyMGQwMTRiYzIzZmVmNGE4OGE2NzYwIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6ImUwcER2NW5nZUgzaCtRL2NxdkVnTFE9PSIsInZhbHVlIjoibWp0UFczdklCOUZIMnFBRFl6Nm9IQ3JJZklzVFRHcFZReEhwc0xTQjREZU1zdFpuVXRra3J4UGZ2SW4zREhnTlJzNFZjUklmbkVXUCtPdVRsN0FZS2E2dnlTa0Ixak4yL2NPbG1RRzlEMjFBbnM2bkczSnk4WGJEVGdZaUo0SkZuQ0I1QW9UaFJpRXhWR2orS0VYeFNjeTVVV1RwS1YvM21VVW1SeGRVcXI5ZnhLZW9qMEhMZHU1VGtHNS95THhneDFKZ1VCT1VZVnRxRjBTVWZHZGdVNkh6YWFjby91eUFqZC9CdmREWHNTa2FrS282alJCZHQ5U2IvTHg3aEdJSU1Xd0NDa2hGTFgvc1hMb3U5SXEwR3JHN3dXZEZsd2RTUThtSytUclFiUVlSSGRZVWJ3ajVXTXd4YnhVTXdQaDZvczlYS0I5U3BveFJxYXRraGNJUjRTR3RZTGdjS25Xc2d3RU9yeVFBWHV5YWhoakw2ZngwOWJuNTJna3NBeUZmbURkbzI2NVYrM0NUcUxSbnp1QnhLNmdBWC9FK3dSQ3ZZaTA5VVBvaURaM1ZkZWUzdEYrTEdOU0tsKzhiZ3FXZjRHTjJnWW9RbzcxeG1mNFVnVVlOR2dSQlVKQ2ZwbFJrMUo4WWdYT3ltbnFDNU5TSlFUbW1paUFLV2l3ZW5jbU4iLCJtYWMiOiJiYWFhZTEzNjg5ZTM0OGUwZWM4MDE3M2IzYjBiNTM3Mjg0ZjU0OTM3Mjc4YTY4MmU3ZWNmY2NmMDMxNDM4OGJlIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1459323107\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-2078109515 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">cN6pwMHluqMWM9hLaRtOtRasnQF8pfN3ghxjsOfa</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Eo35AMmp3Bkf2zsyHLroae0N6MdUih7xJ0ojLwSF</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2078109515\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-211406712 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 07 Jun 2025 23:52:15 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IkNiSG1ENzlkaElSUzM1anQ0Qjk5RWc9PSIsInZhbHVlIjoiQStmYkRoWUZsRk03bGxsd2ZYUVRzOGplemRwbklJVkJ5ZXUra1p6Kzg3WnB1QVg5U2l4SFF0enZ2L1NUcW5jVDJHaUR5Z0ZLUlFoVko2UW1YbHk0LzFoUENMTGIyN2tzMGp4WEVsdjBkNnpwWmhBck05ZDhWUllST2NnVmpPWTl4N3NzNWkrSkNpc1B2L1k1bnpIcHdScTd0WjFCRUtmRFl1NTBVd0N5bWFwaU5SZTRQaUJVbUwwRmFHSG56THc3cjdiMnhsTS9aNkxYYStGYkZFSWdmT1QwRkVjTzg1dTBSOVowek1ibnRMbGtMRUdCUUl1MElIYmJUajMzSUZENUwzV08wNCtvZGc4N1I3UXZNdms2ZFV6SjBURUkxcU1uZlR1NHRzSlRkK1JITHpTNFZZWER5RStROXJBS094amVseVNXcXBhVVJTRjlHdS91eGZ2VzlrQlRxcXhTTmZLdHNEZExjd2d5R0IrYWQ0ZzBCNjZlNFAxT1ZMRFR0aHA5MUszczdLSzRtbGR2OEJoOHVKSUh1ZTZJNGRoZ1N3dzk4L1JtenFLaXJzd0JLdjF4V2J2aW52b1U4dTMxT3lDRkdNTCtpUkxUZTNtc04reDlFYytRTVBIdUMxeTJoRlBJVHZvL1F3RS85SkswOHZiRDhGa2RGQmU5SVRQaHJLbDkiLCJtYWMiOiJmYjQ2ZjFjZDcxMzliZmJmMmE3YzgxNmY0NGZhZWU1MTRhYjkzZWZiNDMwOTk5OTc5ZDUwMTU2OTU2NThkZWE0IiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 01:52:15 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6Im5YazhKNXVXOUEveC9uQnBIUVNkblE9PSIsInZhbHVlIjoiRTl6V2FRRUJRK2NCTUpZY2VUWklqR3BFaWpJWlRCVWdGbm5SOStDTWxRRXZCRVR3ZGhlVEpld0svazNOYnBVVkpUbjBKVzZVTURsMEVCcjFvRklzN0JCZFdNN0RCUGhtTlR6ZWJ5bzZzYnFhRkJFMDBESm85d0o2amZWelp0Q3BTVTZoZ29iTXI5T0d4MGFCUWh2ZjFCZUlsV3ByY3duM1pCTmtNWFdsaG45V05qZXpyellRR1g5OGlZeWJyN1EzbTdNL0FVcVIvT2JSaHp1eVdLNkx6YkpzM1NkSzVwZmRCaUdqYURNRzB2OHR0L3U5QUtUNmQvcVJDclJiT1B1OVJpUjZjODN4MDBGQSt3M2pNWjFBM29wOU5NSG00cVBkRnJKbVlyNytIOHdjd09PdVpIckkyQTJKUC9JZ0pVSmRGdVdsNTJOSU1vZyt1ZnRRRW0wQ29zRDZGM0JubGVEUlgrNVFDTWFUR3pCZzh3Y1hlK3RDdE9PRGNzL3o4ZHhWemFvMFAvWkc2ZC9EOE1tRmRvdHdiK1ViZ0FGeFFDTkRwNURaUmllbEJrSmFMekVGQkpQY3R6SnRNa0RTWkxwalFhVGR6VVQ3dCtydGNMSmtCdnN5L2RCRGR6MXJFQkxzSkZOQkNTWGJ6NnZpOEd3UStoN1JmR3dTdDUzeVpRMmgiLCJtYWMiOiJjZjZjOGFhMjA5ZWJmMjI5M2IzODEyMmEwOTJmZTE5MjA1ZjcwZjcyMDIzNWVmMmZlZDY3NGVlMTIwMGM4MDE5IiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 01:52:15 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IkNiSG1ENzlkaElSUzM1anQ0Qjk5RWc9PSIsInZhbHVlIjoiQStmYkRoWUZsRk03bGxsd2ZYUVRzOGplemRwbklJVkJ5ZXUra1p6Kzg3WnB1QVg5U2l4SFF0enZ2L1NUcW5jVDJHaUR5Z0ZLUlFoVko2UW1YbHk0LzFoUENMTGIyN2tzMGp4WEVsdjBkNnpwWmhBck05ZDhWUllST2NnVmpPWTl4N3NzNWkrSkNpc1B2L1k1bnpIcHdScTd0WjFCRUtmRFl1NTBVd0N5bWFwaU5SZTRQaUJVbUwwRmFHSG56THc3cjdiMnhsTS9aNkxYYStGYkZFSWdmT1QwRkVjTzg1dTBSOVowek1ibnRMbGtMRUdCUUl1MElIYmJUajMzSUZENUwzV08wNCtvZGc4N1I3UXZNdms2ZFV6SjBURUkxcU1uZlR1NHRzSlRkK1JITHpTNFZZWER5RStROXJBS094amVseVNXcXBhVVJTRjlHdS91eGZ2VzlrQlRxcXhTTmZLdHNEZExjd2d5R0IrYWQ0ZzBCNjZlNFAxT1ZMRFR0aHA5MUszczdLSzRtbGR2OEJoOHVKSUh1ZTZJNGRoZ1N3dzk4L1JtenFLaXJzd0JLdjF4V2J2aW52b1U4dTMxT3lDRkdNTCtpUkxUZTNtc04reDlFYytRTVBIdUMxeTJoRlBJVHZvL1F3RS85SkswOHZiRDhGa2RGQmU5SVRQaHJLbDkiLCJtYWMiOiJmYjQ2ZjFjZDcxMzliZmJmMmE3YzgxNmY0NGZhZWU1MTRhYjkzZWZiNDMwOTk5OTc5ZDUwMTU2OTU2NThkZWE0IiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 01:52:15 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6Im5YazhKNXVXOUEveC9uQnBIUVNkblE9PSIsInZhbHVlIjoiRTl6V2FRRUJRK2NCTUpZY2VUWklqR3BFaWpJWlRCVWdGbm5SOStDTWxRRXZCRVR3ZGhlVEpld0svazNOYnBVVkpUbjBKVzZVTURsMEVCcjFvRklzN0JCZFdNN0RCUGhtTlR6ZWJ5bzZzYnFhRkJFMDBESm85d0o2amZWelp0Q3BTVTZoZ29iTXI5T0d4MGFCUWh2ZjFCZUlsV3ByY3duM1pCTmtNWFdsaG45V05qZXpyellRR1g5OGlZeWJyN1EzbTdNL0FVcVIvT2JSaHp1eVdLNkx6YkpzM1NkSzVwZmRCaUdqYURNRzB2OHR0L3U5QUtUNmQvcVJDclJiT1B1OVJpUjZjODN4MDBGQSt3M2pNWjFBM29wOU5NSG00cVBkRnJKbVlyNytIOHdjd09PdVpIckkyQTJKUC9JZ0pVSmRGdVdsNTJOSU1vZyt1ZnRRRW0wQ29zRDZGM0JubGVEUlgrNVFDTWFUR3pCZzh3Y1hlK3RDdE9PRGNzL3o4ZHhWemFvMFAvWkc2ZC9EOE1tRmRvdHdiK1ViZ0FGeFFDTkRwNURaUmllbEJrSmFMekVGQkpQY3R6SnRNa0RTWkxwalFhVGR6VVQ3dCtydGNMSmtCdnN5L2RCRGR6MXJFQkxzSkZOQkNTWGJ6NnZpOEd3UStoN1JmR3dTdDUzeVpRMmgiLCJtYWMiOiJjZjZjOGFhMjA5ZWJmMjI5M2IzODEyMmEwOTJmZTE5MjA1ZjcwZjcyMDIzNWVmMmZlZDY3NGVlMTIwMGM4MDE5IiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 01:52:15 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-211406712\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-2014105563 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">cN6pwMHluqMWM9hLaRtOtRasnQF8pfN3ghxjsOfa</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"41 characters\">http://localhost/financial/productservice</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2014105563\", {\"maxDepth\":0})</script>\n"}}