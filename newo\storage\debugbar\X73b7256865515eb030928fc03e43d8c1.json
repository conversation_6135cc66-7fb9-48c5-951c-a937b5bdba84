{"__meta": {"id": "X73b7256865515eb030928fc03e43d8c1", "datetime": "2025-06-07 22:20:59", "utime": **********.367612, "method": "GET", "uri": "/login", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749334857.910381, "end": **********.367648, "duration": 1.4572668075561523, "duration_str": "1.46s", "measures": [{"label": "Booting", "start": 1749334857.910381, "relative_start": 0, "end": **********.103217, "relative_end": **********.103217, "duration": 1.192835807800293, "duration_str": "1.19s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.103241, "relative_start": 1.1928598880767822, "end": **********.367652, "relative_end": 4.0531158447265625e-06, "duration": 0.26441097259521484, "duration_str": "264ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45983352, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 4, "templates": [{"name": "1x auth.login", "param_count": null, "params": [], "start": **********.260633, "type": "blade", "hash": "bladeC:\\laragon\\www\\to\\newo\\resources\\views/auth/login.blade.phpauth.login", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fresources%2Fviews%2Fauth%2Flogin.blade.php&line=1", "ajax": false, "filename": "login.blade.php", "line": "?"}, "render_count": 1, "name_original": "auth.login"}, {"name": "1x layouts.auth", "param_count": null, "params": [], "start": **********.278031, "type": "blade", "hash": "bladeC:\\laragon\\www\\to\\newo\\resources\\views/layouts/auth.blade.phplayouts.auth", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fresources%2Fviews%2Flayouts%2Fauth.blade.php&line=1", "ajax": false, "filename": "auth.blade.php", "line": "?"}, "render_count": 1, "name_original": "layouts.auth"}, {"name": "1x landingpage::layouts.buttons", "param_count": null, "params": [], "start": **********.341168, "type": "blade", "hash": "bladeC:\\laragon\\www\\to\\newo\\Modules/LandingPage\\Resources/views/layouts/buttons.blade.phplandingpage::layouts.buttons", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2FModules%2FLandingPage%2FResources%2Fviews%2Flayouts%2Fbuttons.blade.php&line=1", "ajax": false, "filename": "buttons.blade.php", "line": "?"}, "render_count": 1, "name_original": "landingpage::layouts.buttons"}, {"name": "1x layouts.cookie_consent", "param_count": null, "params": [], "start": **********.3521, "type": "blade", "hash": "bladeC:\\laragon\\www\\to\\newo\\resources\\views/layouts/cookie_consent.blade.phplayouts.cookie_consent", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fresources%2Fviews%2Flayouts%2Fcookie_consent.blade.php&line=1", "ajax": false, "filename": "cookie_consent.blade.php", "line": "?"}, "render_count": 1, "name_original": "layouts.cookie_consent"}]}, "route": {"uri": "GET login/{lang?}", "middleware": "web, guest", "controller": "App\\Http\\Controllers\\Auth\\AuthenticatedSessionController@showLoginForm", "namespace": null, "prefix": "", "where": [], "as": "login", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FAuth%2FAuthenticatedSessionController.php&line=344\" onclick=\"\">app/Http/Controllers/Auth/AuthenticatedSessionController.php:344-359</a>"}, "queries": {"nb_statements": 9, "nb_failed_statements": 0, "accumulated_duration": 0.03470000000000001, "accumulated_duration_str": "34.7ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 46}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 78}, {"index": 15, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 555}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/Auth/AuthenticatedSessionController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\Auth\\AuthenticatedSessionController.php", "line": 348}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.186526, "duration": 0.01579, "duration_str": "15.79ms", "memory": 0, "memory_str": null, "filename": "Utility.php:46", "source": "app/Models/Utility.php:46", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUtility.php&line=46", "ajax": false, "filename": "Utility.php", "line": "46"}, "connection": "ty", "start_percent": 0, "width_percent": 45.504}, {"sql": "select table_name as `name`, (data_length + index_length) as `size`, table_comment as `comment`, engine as `engine`, table_collation as `collation` from information_schema.tables where table_schema = 'ty' and table_type in ('BASE TABLE', 'SYSTEM VERSIONED') order by table_name", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 537}, {"index": 14, "namespace": null, "name": "app/Http/Controllers/Auth/AuthenticatedSessionController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\Auth\\AuthenticatedSessionController.php", "line": 351}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.208972, "duration": 0.01009, "duration_str": "10.09ms", "memory": 0, "memory_str": null, "filename": "Utility.php:537", "source": "app/Models/Utility.php:537", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUtility.php&line=537", "ajax": false, "filename": "Utility.php", "line": "537"}, "connection": "ty", "start_percent": 45.504, "width_percent": 29.078}, {"sql": "select `full_name`, `code` from `languages`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 17, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 543}, {"index": 18, "namespace": null, "name": "app/Http/Controllers/Auth/AuthenticatedSessionController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\Auth\\AuthenticatedSessionController.php", "line": 351}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.2270398, "duration": 0.00079, "duration_str": "790μs", "memory": 0, "memory_str": null, "filename": "Utility.php:543", "source": "app/Models/Utility.php:543", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUtility.php&line=543", "ajax": false, "filename": "Utility.php", "line": "543"}, "connection": "ty", "start_percent": 74.582, "width_percent": 2.277}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 4716}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 4650}, {"index": 15, "namespace": "view", "name": "auth.login", "file": "C:\\laragon\\www\\to\\newo\\resources\\views/auth/login.blade.php", "line": 4}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.26235, "duration": 0.0015, "duration_str": "1.5ms", "memory": 0, "memory_str": null, "filename": "Utility.php:4716", "source": "app/Models/Utility.php:4716", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUtility.php&line=4716", "ajax": false, "filename": "Utility.php", "line": "4716"}, "connection": "ty", "start_percent": 76.859, "width_percent": 4.323}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 4716}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 4650}, {"index": 15, "namespace": "view", "name": "layouts.auth", "file": "C:\\laragon\\www\\to\\newo\\resources\\views/layouts/auth.blade.php", "line": 10}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.2803972, "duration": 0.00132, "duration_str": "1.32ms", "memory": 0, "memory_str": null, "filename": "Utility.php:4716", "source": "app/Models/Utility.php:4716", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUtility.php&line=4716", "ajax": false, "filename": "Utility.php", "line": "4716"}, "connection": "ty", "start_percent": 81.182, "width_percent": 3.804}, {"sql": "select * from `users` where `type` = 'super admin' limit 1", "type": "query", "params": [], "bindings": ["super admin"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 4081}, {"index": 17, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 4123}, {"index": 18, "namespace": "view", "name": "layouts.auth", "file": "C:\\laragon\\www\\to\\newo\\resources\\views/layouts/auth.blade.php", "line": 22}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.310423, "duration": 0.00159, "duration_str": "1.59ms", "memory": 0, "memory_str": null, "filename": "Utility.php:4081", "source": "app/Models/Utility.php:4081", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUtility.php&line=4081", "ajax": false, "filename": "Utility.php", "line": "4081"}, "connection": "ty", "start_percent": 84.986, "width_percent": 4.582}, {"sql": "select `value`, `name` from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 4082}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 4123}, {"index": 15, "namespace": "view", "name": "layouts.auth", "file": "C:\\laragon\\www\\to\\newo\\resources\\views/layouts/auth.blade.php", "line": 22}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.321006, "duration": 0.0010500000000000002, "duration_str": "1.05ms", "memory": 0, "memory_str": null, "filename": "Utility.php:4082", "source": "app/Models/Utility.php:4082", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUtility.php&line=4082", "ajax": false, "filename": "Utility.php", "line": "4082"}, "connection": "ty", "start_percent": 89.568, "width_percent": 3.026}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 4716}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 4650}, {"index": 15, "namespace": "view", "name": "layouts.auth", "file": "C:\\laragon\\www\\to\\newo\\resources\\views/layouts/auth.blade.php", "line": 39}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.329086, "duration": 0.00123, "duration_str": "1.23ms", "memory": 0, "memory_str": null, "filename": "Utility.php:4716", "source": "app/Models/Utility.php:4716", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUtility.php&line=4716", "ajax": false, "filename": "Utility.php", "line": "4716"}, "connection": "ty", "start_percent": 92.594, "width_percent": 3.545}, {"sql": "select * from `landing_page_settings`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 18, "namespace": null, "name": "Modules/LandingPage/Entities/LandingPageSetting.php", "file": "C:\\laragon\\www\\to\\newo\\Modules\\LandingPage\\Entities\\LandingPageSetting.php", "line": 27}, {"index": 19, "namespace": "view", "name": "landingpage::layouts.buttons", "file": "C:\\laragon\\www\\to\\newo\\Modules/LandingPage\\Resources/views/layouts/buttons.blade.php", "line": 2}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 73}], "start": **********.343641, "duration": 0.00134, "duration_str": "1.34ms", "memory": 0, "memory_str": null, "filename": "LandingPageSetting.php:27", "source": "Modules/LandingPage/Entities/LandingPageSetting.php:27", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2FModules%2FLandingPage%2FEntities%2FLandingPageSetting.php&line=27", "ajax": false, "filename": "LandingPageSetting.php", "line": "27"}, "connection": "ty", "start_percent": 96.138, "width_percent": 3.862}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "DCK491V1GUyngHueDf4bnOw7DOzApt305JPGLVcA", "_previous": "array:1 [\n  \"url\" => \"http://localhost/login\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/login", "status_code": "<pre class=sf-dump id=sf-dump-979611685 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-979611685\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-1086632545 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1086632545\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1648713576 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1648713576\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-896200249 data-indent-pad=\"  \"><span class=sf-dump-note>array:17</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">max-age=0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/login</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1938 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=17ceeen%7C2%7Cfwk%7C0%7C1960; XSRF-TOKEN=eyJpdiI6IlRYMUpMbExVSFdOMU1FMXZkdVhaUHc9PSIsInZhbHVlIjoieDZiUGZiY3BuVWd0aDNBT0lLaHUzUy80SjZhandnMTZSQ050QndiQ0NoTVJUcXcwRUMxUTFrZnkweEgrM3lhVDQ2NlgwUmVvbUpPRUZYZDJqdVNDbzRYeWQ3cWpIdk5DMTBRUVFVNDNnUEFLb200NzJ5aUlwYkcvVERuenVJS1Z1NW0vbXVFUDhhT1AwbDBHdTlFSG4zaHdXaVVaK2VWRFltVzJEdXdJeTd1OEZBa0hpbFZ2djlPaW9aTjVpZktoK2pCdXBmM3ZieGZ6amdhV2N3L1hiY1ZwOTRLMnFWZCtYa1g2N3hRbE5SZ2k2WGhYLzEwWVhpMkdUMEErWE1MT1gwTWpWeTZOVUxWMDE5N1p6eUFKS1NKZ1BTT2s0VzcwcExZWG1hb0pDdE5YWlB3UmZzeDBqS09QWU5XOXlKaThMLzFTdVBudUVmSDFGS3ZUYW5YSUFkN01xODFBdURzVTJNMzNaTjhkbndrZGNkeHFzazVBQm9xRTF1Y0lMME5YNnd4SXhIcVlwa2p1S0IwbG9Ub1c5TVZoRXY2cUQzQnV5Q21zMzZmblBHUCtKNTI3TnRYK09KcHFYOUEzSlJJZnd1MXJPNjhKZXk1ZjR3cHBZRklGaEpJTlcvT040NDUzNnFlT0tZbkFpdHVwMWJ2S2xsTzhzYzR4bklJOVhEV3MiLCJtYWMiOiI3NjRiZmI4Yzg1MTVlMjgzYjdlZWZjZTAzMGY1ODE3ZTdkZjNjZTVhMDM4NWMyNmQ5NTZmZTgwNWMzZmYzOTllIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6InlZQVJYYW1lcnVjNWYrUUlkazRkc0E9PSIsInZhbHVlIjoiYnZkSUkrSjZPK3NXZG1WTDN2UWl0OU5vQkhQYjByUnRYT1JNYVZUTzJDTUpXV2E1c3dobVNwUDJ2UlNtckJmWmczNGxKUjVaOUR4V0dqMkhnU0ZsT0J4MFhaTHFUODBXSEpkRWJxa0puUVdKWXl2WFRqcGE4TmFWRjlUeUJ1MzQxYmE2NC90Qk1Sb3ZvWWYrS1pZcU90Z2pyWTNGVGZHMlQ3TGVhZW1MZFZEZlY1bGwxNkhQTUo0VHg1VDk0WUZUU01RZm5NYXFFOTFKaEtXS1VNeFBLY0RRK3RteUJ0OWNnVTlSQlRnQ1RqZkFTOFlwdmRaanUweENnRDVITWdDZ3ZvY2pOVDJsS0ZNazdGdFgzOXRNckN0T2oxaGxUd0tEa0cybE9vSzZMWm4zbURKWnZZSnZtUnh1NlJhdDdhLzZlVlJWKzRIWFNXMkZLd1FhZGl5alMxM04xOC9SaGE1d1gzNktsNXJFaEJaM0ZoTTJaRzloNHh4S21TZ082TUJJUExmV0ppdHBGVVdTSmpjWTBqTExmNjlXSG5iU3NITzY3WFZlbGorRzZIREo5dlhoUzFIN3RKUUt2U3pLOEdwMHJ5bjJZeEl4RkozdzlNbG9GcTE5YUZFQktzd1VMVGd5T0tzakdHeEpDTWYyWHVlR3JqUnlJK1ZIejgweEtEOCsiLCJtYWMiOiIwNzQwYTFhZjMyZWE3M2Q1NDIxN2ZmYmIzZjE1ZjMzNzRmYWY5MGY5OWZmMjMyMDI0ODc2YTgzMDhjYzYwZTkwIiwidGFnIjoiIn0%3D; _clsk=1yftgw7%7C1749334846996%7C3%7C1%7Ck.clarity.ms%2Fcollect</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-896200249\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-845750515 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">DCK491V1GUyngHueDf4bnOw7DOzApt305JPGLVcA</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">9hFyIirUIgn7VMVjViW9Tfz3n0yci3AztutQe7vH</span>\"\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-845750515\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-13728153 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 07 Jun 2025 22:20:59 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6ImJCR3orVkdjekN1MVRiZUo3RGo4TUE9PSIsInZhbHVlIjoiODdSSmJ1bTQ0UC9ZM2F0djN6eUZyRTFYSmRvZEUvZTBaQnBOTmxoTUtoUko5cEt3KzN5dG9BVDB2SU1iMjNZN0hKQ0VIOHYzNjJYQUhya1JCdjVSVVdrM3NuN3hNRnVsck51TCtpWDNxcnpOYlhQUTdXZitsZUZCUk10RktHOFpONGl4R1BYcnJ6cU1LOFRTQ0d1SFB5NlA0R1E1SWFWbEd2Q2J6V01CcmU0Z3BuTHQ4c3l2U0ZsYWhPbjVPd0JBT2xyNk1GdVJvOVdKdktoWjA0Ynl6djhOZFA0cWRsWkt2bW5haCt5dXRPZDFhUXBXVVJsc3dzWHlCSk9ocGJreCt4aks2d2JsQW91c05FN0tPSUVCWWhESHBkVXArRHhKQyt6N2FkOWpVZzZRMkFkOHpJczg0STZnYkh3cU0zZWRFU1ErNVF0emJCMDhQSXVmT3RtS0FiWTRPNnk5dzYwbU1hemRZZGR6clBwQVRFcDc1bVBIeGlORk9PVFIxV28zaXFudzE0LzlKTlRvRFFnV1FZMGdtdmI2NTNHMTU4WS9nVkxFSnlhUHBPSEMwRUVWQk9RamFaUXJydWg3UHJHd1ByN2lFZlRVZlByWnFiU3BqVHBOTE1YeUlBZThDN3E5Nm5pa0tQSVlYcGljSHFQWGtQbXplVzhxMkJJUEpjOUIiLCJtYWMiOiJkZjQ3ODdhYjQ1YWVjMTg5NDdjOTI5MGY3OGFjMjExMzFjNzU1NGI0Zjc0YmViMWI1YjNmMzYzZmY5YjM3NTYzIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 00:20:59 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6ImxnVGFzT28vUFQvblhqZzBuMVdjNUE9PSIsInZhbHVlIjoid0s2WnZaRlN3V01sU01GZXZoeXZnSG1RWG1SSlMydjB1aTNOWkdiYmxBSnllOFNJclZzaEQrZUYyNzNBVHNTTUR3Y2VaWmpVVlZoTDJBdTVhaVBQb0JzZmhKL3laR0tjMm8valNpa0ZVWWkvZ3U2aiswR3NwejY0MnNUYjhmUGw4YWpCNURhL09acHdTeUYvaHBVcC8yZVlCUWpPTzFPSUdJVGtpbmxyb1ZDTVZ1TWk4UmF1S1U3c0l0ZEthWFFnODZYNEpTNVNSd1ZFMzV5cWNnb3VqZDNpZ29yRWFVbTNIMVFJQk0yMWZjWTFKQXBqU1cvL0ZUd0o0M0pXSndJdTdmTHd5UUlsTWpWNlFYdVhseTJoTzZmQ2ZnNTAzbC82Wk5zQklNSDFBNTEyaDNkaGE0MlpIMm1LbkN3NXFIcytmMFFQWDlFdW1qSWRNcWlpaFpnR0d5V0tFaElZYlQwMGdPd0ZZWklzcGloY0s5MERoRDJjd3NZeUFmMDk0ZGRxdld3dWVrZlVhV0Q1TVRVUHdBbnc5WFFxaWVrYzQ5Z01VV2UwaXZsY1k5YXFyeWtHSGV4ZGxabWhJNTBmWE8ydmc2UG5VMzhSdkszM1hjRnBjZWd6SVExTmVVRVpOWDZwcFVianc5VWlSSUVFMkNSR2Y4bmdEdHJabTZwQ0RRY1ciLCJtYWMiOiJlMmQyN2ZjM2JiMzM0YjYyMWY1ODVhZmQwOWViZjI3YWI3YmEwNjcwMTZiMjZlN2U5YzgwMWVkZjVmZTM0NzlhIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 00:20:59 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6ImJCR3orVkdjekN1MVRiZUo3RGo4TUE9PSIsInZhbHVlIjoiODdSSmJ1bTQ0UC9ZM2F0djN6eUZyRTFYSmRvZEUvZTBaQnBOTmxoTUtoUko5cEt3KzN5dG9BVDB2SU1iMjNZN0hKQ0VIOHYzNjJYQUhya1JCdjVSVVdrM3NuN3hNRnVsck51TCtpWDNxcnpOYlhQUTdXZitsZUZCUk10RktHOFpONGl4R1BYcnJ6cU1LOFRTQ0d1SFB5NlA0R1E1SWFWbEd2Q2J6V01CcmU0Z3BuTHQ4c3l2U0ZsYWhPbjVPd0JBT2xyNk1GdVJvOVdKdktoWjA0Ynl6djhOZFA0cWRsWkt2bW5haCt5dXRPZDFhUXBXVVJsc3dzWHlCSk9ocGJreCt4aks2d2JsQW91c05FN0tPSUVCWWhESHBkVXArRHhKQyt6N2FkOWpVZzZRMkFkOHpJczg0STZnYkh3cU0zZWRFU1ErNVF0emJCMDhQSXVmT3RtS0FiWTRPNnk5dzYwbU1hemRZZGR6clBwQVRFcDc1bVBIeGlORk9PVFIxV28zaXFudzE0LzlKTlRvRFFnV1FZMGdtdmI2NTNHMTU4WS9nVkxFSnlhUHBPSEMwRUVWQk9RamFaUXJydWg3UHJHd1ByN2lFZlRVZlByWnFiU3BqVHBOTE1YeUlBZThDN3E5Nm5pa0tQSVlYcGljSHFQWGtQbXplVzhxMkJJUEpjOUIiLCJtYWMiOiJkZjQ3ODdhYjQ1YWVjMTg5NDdjOTI5MGY3OGFjMjExMzFjNzU1NGI0Zjc0YmViMWI1YjNmMzYzZmY5YjM3NTYzIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 00:20:59 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6ImxnVGFzT28vUFQvblhqZzBuMVdjNUE9PSIsInZhbHVlIjoid0s2WnZaRlN3V01sU01GZXZoeXZnSG1RWG1SSlMydjB1aTNOWkdiYmxBSnllOFNJclZzaEQrZUYyNzNBVHNTTUR3Y2VaWmpVVlZoTDJBdTVhaVBQb0JzZmhKL3laR0tjMm8valNpa0ZVWWkvZ3U2aiswR3NwejY0MnNUYjhmUGw4YWpCNURhL09acHdTeUYvaHBVcC8yZVlCUWpPTzFPSUdJVGtpbmxyb1ZDTVZ1TWk4UmF1S1U3c0l0ZEthWFFnODZYNEpTNVNSd1ZFMzV5cWNnb3VqZDNpZ29yRWFVbTNIMVFJQk0yMWZjWTFKQXBqU1cvL0ZUd0o0M0pXSndJdTdmTHd5UUlsTWpWNlFYdVhseTJoTzZmQ2ZnNTAzbC82Wk5zQklNSDFBNTEyaDNkaGE0MlpIMm1LbkN3NXFIcytmMFFQWDlFdW1qSWRNcWlpaFpnR0d5V0tFaElZYlQwMGdPd0ZZWklzcGloY0s5MERoRDJjd3NZeUFmMDk0ZGRxdld3dWVrZlVhV0Q1TVRVUHdBbnc5WFFxaWVrYzQ5Z01VV2UwaXZsY1k5YXFyeWtHSGV4ZGxabWhJNTBmWE8ydmc2UG5VMzhSdkszM1hjRnBjZWd6SVExTmVVRVpOWDZwcFVianc5VWlSSUVFMkNSR2Y4bmdEdHJabTZwQ0RRY1ciLCJtYWMiOiJlMmQyN2ZjM2JiMzM0YjYyMWY1ODVhZmQwOWViZjI3YWI3YmEwNjcwMTZiMjZlN2U5YzgwMWVkZjVmZTM0NzlhIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 00:20:59 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-13728153\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1473539686 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">DCK491V1GUyngHueDf4bnOw7DOzApt305JPGLVcA</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/login</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1473539686\", {\"maxDepth\":0})</script>\n"}}