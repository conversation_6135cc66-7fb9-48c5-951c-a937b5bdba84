{"__meta": {"id": "Xf0009554cd3a8a318ce8064313566fc1", "datetime": "2025-06-07 22:36:30", "utime": **********.443117, "method": "GET", "uri": "/search-products?search=&cat_id=0&war_id=8&session_key=pos&type=sku", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749335789.660434, "end": **********.443149, "duration": 0.7827150821685791, "duration_str": "783ms", "measures": [{"label": "Booting", "start": 1749335789.660434, "relative_start": 0, "end": **********.26766, "relative_end": **********.26766, "duration": 0.6072258949279785, "duration_str": "607ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.267675, "relative_start": 0.6072409152984619, "end": **********.443153, "relative_end": 3.814697265625e-06, "duration": 0.1754779815673828, "duration_str": "175ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 53117552, "peak_usage_str": "51MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET search-products", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\ProductServiceController@searchProducts", "namespace": null, "prefix": "", "where": [], "as": "search.products", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FProductServiceController.php&line=1138\" onclick=\"\">app/Http/Controllers/ProductServiceController.php:1138-1234</a>"}, "queries": {"nb_statements": 8, "nb_failed_statements": 0, "accumulated_duration": 0.02087, "accumulated_duration_str": "20.87ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 16 limit 1", "type": "query", "params": [], "bindings": ["16"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.326691, "duration": 0.01304, "duration_str": "13.04ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 62.482}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.354016, "duration": 0.00098, "duration_str": "980μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 62.482, "width_percent": 4.696}, {"sql": "select `permissions`.*, `model_has_permissions`.`model_id` as `pivot_model_id`, `model_has_permissions`.`permission_id` as `pivot_permission_id`, `model_has_permissions`.`model_type` as `pivot_model_type` from `permissions` inner join `model_has_permissions` on `permissions`.`id` = `model_has_permissions`.`permission_id` where `model_has_permissions`.`model_id` = 16 and `model_has_permissions`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["16", "App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 326}, {"index": 21, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 566}], "start": **********.382931, "duration": 0.00107, "duration_str": "1.07ms", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:326", "source": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php:326", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=326", "ajax": false, "filename": "HasPermissions.php", "line": "326"}, "connection": "ty", "start_percent": 67.178, "width_percent": 5.127}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (16) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 312}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 26, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}], "start": **********.387666, "duration": 0.00146, "duration_str": "1.46ms", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 72.305, "width_percent": 6.996}, {"sql": "select * from `warehouse_products` where `warehouse_id` = '8'", "type": "query", "params": [], "bindings": ["8"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/ProductServiceController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\ProductServiceController.php", "line": 1150}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.398011, "duration": 0.00093, "duration_str": "930μs", "memory": 0, "memory_str": null, "filename": "ProductServiceController.php:1150", "source": "app/Http/Controllers/ProductServiceController.php:1150", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FProductServiceController.php&line=1150", "ajax": false, "filename": "ProductServiceController.php", "line": "1150"}, "connection": "ty", "start_percent": 79.3, "width_percent": 4.456}, {"sql": "select `product_services`.*, `c`.`name` as `categoryname` from `product_services` left join `product_service_categories` as `c` on `c`.`id` = `product_services`.`category_id` where `product_services`.`type` = 'product' and `product_services`.`created_by` = 15 and `product_services`.`id` in (3) order by `product_services`.`id` desc", "type": "query", "params": [], "bindings": ["product", "15", "3"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/ProductServiceController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\ProductServiceController.php", "line": 1166}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.4041212, "duration": 0.00189, "duration_str": "1.89ms", "memory": 0, "memory_str": null, "filename": "ProductServiceController.php:1166", "source": "app/Http/Controllers/ProductServiceController.php:1166", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FProductServiceController.php&line=1166", "ajax": false, "filename": "ProductServiceController.php", "line": "1166"}, "connection": "ty", "start_percent": 83.757, "width_percent": 9.056}, {"sql": "select * from `product_service_units` where `product_service_units`.`id` in (5)", "type": "query", "params": [], "bindings": ["5"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/ProductServiceController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\ProductServiceController.php", "line": 1166}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.413924, "duration": 0.0009, "duration_str": "900μs", "memory": 0, "memory_str": null, "filename": "ProductServiceController.php:1166", "source": "app/Http/Controllers/ProductServiceController.php:1166", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FProductServiceController.php&line=1166", "ajax": false, "filename": "ProductServiceController.php", "line": "1166"}, "connection": "ty", "start_percent": 92.813, "width_percent": 4.312}, {"sql": "select * from `warehouse_products` where `warehouse_id` = '8' and `product_id` = 3 limit 1", "type": "query", "params": [], "bindings": ["8", "3"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/ProductService.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\ProductService.php", "line": 267}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/ProductServiceController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\ProductServiceController.php", "line": 1173}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.4183168, "duration": 0.0006, "duration_str": "600μs", "memory": 0, "memory_str": null, "filename": "ProductService.php:267", "source": "app/Models/ProductService.php:267", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FProductService.php&line=267", "ajax": false, "filename": "ProductService.php", "line": "267"}, "connection": "ty", "start_percent": 97.125, "width_percent": 2.875}]}, "models": {"data": {"App\\Models\\WarehouseProduct": {"value": 2, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FWarehouseProduct.php&line=1", "ajax": false, "filename": "WarehouseProduct.php", "line": "?"}}, "App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}, "App\\Models\\ProductService": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FProductService.php&line=1", "ajax": false, "filename": "ProductService.php", "line": "?"}}, "App\\Models\\ProductServiceUnit": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FProductServiceUnit.php&line=1", "ajax": false, "filename": "ProductServiceUnit.php", "line": "?"}}}, "count": 6, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 1, "messages": [{"message": "[ability => manage pos, result => true, user => 16, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-837678506 data-indent-pad=\"  \"><span class=sf-dump-note>manage pos</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"10 characters\">manage pos</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>16</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-837678506\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.39657, "xdebug_link": null}]}, "session": {"_token": "iD3eOztlFQTk9Wu93brk2PH5kNLllwdWSLzbHLjf", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "16"}, "request": {"path_info": "/search-products", "status_code": "<pre class=sf-dump id=sf-dump-1006035950 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1006035950\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-963569986 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>search</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cat_id</span>\" => \"<span class=sf-dump-str>0</span>\"\n  \"<span class=sf-dump-key>war_id</span>\" => \"<span class=sf-dump-str>8</span>\"\n  \"<span class=sf-dump-key>session_key</span>\" => \"<span class=sf-dump-str title=\"3 characters\">pos</span>\"\n  \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"3 characters\">sku</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-963569986\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-152862538 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-152862538\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-918549504 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">iD3eOztlFQTk9Wu93brk2PH5kNLllwdWSLzbHLjf</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1938 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=17ceeen%7C2%7Cfwk%7C0%7C1960; _clsk=1yftgw7%7C1749334872942%7C5%7C1%7Ck.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IkJDMEVuSzhmSWVWRkJuSzNtS2I4cmc9PSIsInZhbHVlIjoiUEh4SS9WeVRlbFNxTU9Lc2lPQzBqdmV5YXJGaGc2TXdwWlVLUGZjVCtGQkZ6TlRhaU1BSW5GdC8rVnpsSzZPSTBuV2ZDQWt4VWZWZ3lLQ0dkNzV3QW5CNG5CNkxSR1hvWTJyN0sxcFBwbkNBSGFQZE8wT1JTUU1TL1lYV0VlWFBoU0F0aEhBdXhvS1VzRExnVnhsS3J6clJlNmUrbE1qdXpUU0k1Z2t0aXg4cCtmRFhuRnFRd2VnTncxQ3FDMVUzaC8xekVidDJmNlg0VEo2c01wNnZzV0FzVXBsVHJTUk9tanNEdll0SmVEcE9xY2EzMzRVZTk5VFJTSVE2ODU5ZDZYTndFR0MrQ01mL1JoS1JvVE5kUWZ5d2E5TEJaZ2E0bFc0Y0E5KzRiT0NEUkdVdDUrWlRaVGpxQlUzU3VsQ053V2p0Und5QXptclFjRCtzaXlsQkhkUUUvMzNQWk1EOE9yNklLUGlITzZhZy90bytpYUJvRENNTnliSjZFeG5HOFNWVERzc0d2TmFlMGZQQkExRHU3WHVFaStnWUtyWGdqMmtDM0pIMElyRmdtVytnRFNRdzdXcnBsZ1l1Vy9PMjB3OG9OcDV3cnVoZktYZjh6QUZDTExjZDdaY0FLam41U081SmZyc2R2RGcrK1U0OVFubUJNN0UyRHU2Mmc4SGkiLCJtYWMiOiJhNWExNTJjNDcyZmE1Mzg0ZGQxOTI3MmI1ODc1YWE5ZmIyNzk4OTVmZjU4OTRjZjZjZTNmNmRjN2MxODc1MGY3IiwidGFnIjoiIn0%3D; test_session=eyJpdiI6IklQMXBoYmprZFdZODcxQ2p3QTcvOUE9PSIsInZhbHVlIjoiVE9rUUwzaHJJUjg0bVJuL3FQVHZLK0sweVhPc01HSENtZmsxVjN6TTNqS1cvZ3FoeUZYakdqVXpaZ09UWmY1a2M5Q21JNlhVMFNxVEJaVXFqSzFhU2Q0Q1Y4WUthTmFsNzAwd1VSUVVHYjhDS3RWV3BncTdVOVVIWXN5dGUrdTc0cDFXQjh5eTlhdmxiaTZnUjN6Y2RnNnB5OTdaZnhpbHFUN3BjSHIxSDV1ektaRnZ4L3F5WDIwTWF2dmR4b1hrbnpMYk9rTWxET2ZrM29qZkpEUmpNb2hUclhBMUZFaVdaQUgwY3dwVTJJcU85V2UvcXhsRjJyVlhGb01lWXNsdUJLanUrRVJ4cmhyKzdjYUUvcG9XU3NtUVVBdUMyeDRkbXlUZFNMNDZuMVpkRjZIcVcrNklDeVBYN2FxMlh2bmxJTFdZeERpTi9Famhwd0lKdk4vclhobmE0ZWMwUzVvaWwvY3FSL3JsQnpCY1IzRVZXMStNUXZwL3hnUnR2UjN4WVNEZVRIT2o5UWRjdDVaaTQrcGVraC9xYklIUEhhejQyQ1hJWXpSSGk3OUg5Y0lSUWtJRTBZRk9WRjFON2ZhMmdEZ3lxdjk0UlRmSVFsWit4SU5QSjU5TlBmTGYrVzlaakV6RzFyL1FLQ0tjclp1VnlJcnhSYkV0U3hzelVDMWwiLCJtYWMiOiIwZTNjMTlhYTJjNzg1YzNiYTk0ZWEzOTFhMWM4ZDI3YjZlNTdjZjdlMDJhYjY1MjliOGI5NWRiMjkyODUyOTA0IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-918549504\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">iD3eOztlFQTk9Wu93brk2PH5kNLllwdWSLzbHLjf</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">atMfj5qpj5gKx4OQYPP5PbQzRbuF7iB8X4zv4aOY</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1154337309 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 07 Jun 2025 22:36:30 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6ImZEQy9qN2dnblZYQzFnM2lZWG1zVmc9PSIsInZhbHVlIjoiTzJXTlB3bDY2MktUMmVqVU9HeXJSZlFuVzNPc2FXcVFXM014a0xPSXliWjR0cEVQT0gxcWs2M2prM1BxNlB6WEd4MU5Ebmp3Z3FaeGZzY3dDY01qMW54Q3N1QlNzdWw2UDhyREpBU2o3WU5sMnN5MTRPM1hHZXNZTDVKRy9Qblh4ckIybldnQVBJQUQ5cThrMThITzRtWmVBckpIcmlESFNQRU9OVXhXbHZlZE9ROXUxT1B3ZEsrdHF4MHgybHJvMFY1Sm1FZHVENUZ1OHkvSWd2QlovWHhON3BFbktlcnFTUFhsYzFVTGdLNVViTlNYTmVCTG41ZGR4S0lyM0c4Y2gwTEVycGlMYW02cEswWWRvcU9xTGxvWGFsbVQyYTkzNDc5WUtrRnNZRGJtS2xDWk8zSDR0c3lxVzJxeUFEcFJ4NUhtK3k4eXJXYUtBclFhdHFLeC9Eb0VkRE9iNU9vK1BsQW80LzB1a3RxT1AyTWJiNXlUV3hvZ01rOFY1Nlg1U202NlcxS3kvcmYwYWhOeFc4SlhXSEt5SDlQVkpBV1hzbHREaXRSa2Z4SlV5YUQ0UFR5dE5sQjllWlE0ZWhpQnJTR0p6RWJWK3FZMzNuRCtqOFZkdEN0YXdJaXovTE91SGhyWUpRTGtmZEN3REcwNUxsQzdVaHpxTlhVVzltZmwiLCJtYWMiOiI1NDg5NDQ4YTgxODQ4MDlmMGRiYWU0MGIwMzFiMjBjYjc3YmNiNzM0NzFhMTFlMjIzNmU3YTcyZjA5ZTAwODk3IiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 00:36:30 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6Ikxld0VqTkYrYmhrak5PSEJVUDk1d0E9PSIsInZhbHVlIjoiRngwbGpnelFjNE5WbUNEYkdXQ3crQldYZi9LbURkTUNzRmVhQ2hXTUdQWXdvS3U3cWhMNVZQZzR2UXNSN2ovVGhXREtyMms1RTU2YjBUWHFlME9ZdzJsY1F3b09wSTBQYXVlUjVybTl6OGJldXdhWFlSMmJOTElQRFFCSUxWNGtUbG5PemhUODlNOUdKTWdsQkZVSXVMbXczTXdUTllmQ0JjMWlVMEJ6QlAyMVdPN0MrUENPV25zTFZYWGNqaFBiaENZU2ZTM3lHb1g1VjZUNHlaWWNHeTROWWRQaHkwemxrZzl5bEU4L0xCWDdOL2RDRGNYR2N4S1ZMYkJXelk3SndiLzdNL244b2FDTTN1NnYzQy80NzhkY3BUbFRKL09wSHMxeGpIL0RrRzBoWkdidU9XQVVEVGlkWkdpVm5CVjVEdnZhV2xRY2NzcU1zOG5XaTMwKzhjUFhycTNaRExUYnoza3p4dk9RZ0VkSWNnYUdxR0dvaXFibXVETXRrWEpUSGd1ZjFoNW9RcExaRjRmeElBSEZacDlod05ZNGJOYUhZVjZGVkJGNTkzUHZ3OXdESlkzWE9xVzFlbWhUUW5WYllaTUEwZVJkbFRDRFdTdGJnU3VwVGFPejZOVUtvNVBLNnpGSGZWdHNpRFVTbDdheGZLL3NTQ0hYL24zSEpWYnoiLCJtYWMiOiJmZmJmMjJlYTQyMjVjMDNkZWYxMzhkMDM5ZmIxMjNlMjgyYzNiMzk4YjU3M2E1ODg5MGI5MWQ2N2Q0Yzg5ZTliIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 00:36:30 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6ImZEQy9qN2dnblZYQzFnM2lZWG1zVmc9PSIsInZhbHVlIjoiTzJXTlB3bDY2MktUMmVqVU9HeXJSZlFuVzNPc2FXcVFXM014a0xPSXliWjR0cEVQT0gxcWs2M2prM1BxNlB6WEd4MU5Ebmp3Z3FaeGZzY3dDY01qMW54Q3N1QlNzdWw2UDhyREpBU2o3WU5sMnN5MTRPM1hHZXNZTDVKRy9Qblh4ckIybldnQVBJQUQ5cThrMThITzRtWmVBckpIcmlESFNQRU9OVXhXbHZlZE9ROXUxT1B3ZEsrdHF4MHgybHJvMFY1Sm1FZHVENUZ1OHkvSWd2QlovWHhON3BFbktlcnFTUFhsYzFVTGdLNVViTlNYTmVCTG41ZGR4S0lyM0c4Y2gwTEVycGlMYW02cEswWWRvcU9xTGxvWGFsbVQyYTkzNDc5WUtrRnNZRGJtS2xDWk8zSDR0c3lxVzJxeUFEcFJ4NUhtK3k4eXJXYUtBclFhdHFLeC9Eb0VkRE9iNU9vK1BsQW80LzB1a3RxT1AyTWJiNXlUV3hvZ01rOFY1Nlg1U202NlcxS3kvcmYwYWhOeFc4SlhXSEt5SDlQVkpBV1hzbHREaXRSa2Z4SlV5YUQ0UFR5dE5sQjllWlE0ZWhpQnJTR0p6RWJWK3FZMzNuRCtqOFZkdEN0YXdJaXovTE91SGhyWUpRTGtmZEN3REcwNUxsQzdVaHpxTlhVVzltZmwiLCJtYWMiOiI1NDg5NDQ4YTgxODQ4MDlmMGRiYWU0MGIwMzFiMjBjYjc3YmNiNzM0NzFhMTFlMjIzNmU3YTcyZjA5ZTAwODk3IiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 00:36:30 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6Ikxld0VqTkYrYmhrak5PSEJVUDk1d0E9PSIsInZhbHVlIjoiRngwbGpnelFjNE5WbUNEYkdXQ3crQldYZi9LbURkTUNzRmVhQ2hXTUdQWXdvS3U3cWhMNVZQZzR2UXNSN2ovVGhXREtyMms1RTU2YjBUWHFlME9ZdzJsY1F3b09wSTBQYXVlUjVybTl6OGJldXdhWFlSMmJOTElQRFFCSUxWNGtUbG5PemhUODlNOUdKTWdsQkZVSXVMbXczTXdUTllmQ0JjMWlVMEJ6QlAyMVdPN0MrUENPV25zTFZYWGNqaFBiaENZU2ZTM3lHb1g1VjZUNHlaWWNHeTROWWRQaHkwemxrZzl5bEU4L0xCWDdOL2RDRGNYR2N4S1ZMYkJXelk3SndiLzdNL244b2FDTTN1NnYzQy80NzhkY3BUbFRKL09wSHMxeGpIL0RrRzBoWkdidU9XQVVEVGlkWkdpVm5CVjVEdnZhV2xRY2NzcU1zOG5XaTMwKzhjUFhycTNaRExUYnoza3p4dk9RZ0VkSWNnYUdxR0dvaXFibXVETXRrWEpUSGd1ZjFoNW9RcExaRjRmeElBSEZacDlod05ZNGJOYUhZVjZGVkJGNTkzUHZ3OXdESlkzWE9xVzFlbWhUUW5WYllaTUEwZVJkbFRDRFdTdGJnU3VwVGFPejZOVUtvNVBLNnpGSGZWdHNpRFVTbDdheGZLL3NTQ0hYL24zSEpWYnoiLCJtYWMiOiJmZmJmMjJlYTQyMjVjMDNkZWYxMzhkMDM5ZmIxMjNlMjgyYzNiMzk4YjU3M2E1ODg5MGI5MWQ2N2Q0Yzg5ZTliIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 00:36:30 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1154337309\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-2105767986 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">iD3eOztlFQTk9Wu93brk2PH5kNLllwdWSLzbHLjf</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>16</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2105767986\", {\"maxDepth\":0})</script>\n"}}