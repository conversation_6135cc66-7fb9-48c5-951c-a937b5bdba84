{"__meta": {"id": "X55f1f9044aa4633b97f864e444a7cd62", "datetime": "2025-06-07 22:39:08", "utime": **********.598993, "method": "POST", "uri": "/event/get_event_data", "ip": "127.0.0.1"}, "php": {"version": "8.3.16", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749335947.828036, "end": **********.599026, "duration": 0.7709898948669434, "duration_str": "771ms", "measures": [{"label": "Booting", "start": 1749335947.828036, "relative_start": 0, "end": **********.489219, "relative_end": **********.489219, "duration": 0.6611828804016113, "duration_str": "661ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.489246, "relative_start": 0.6612098217010498, "end": **********.599031, "relative_end": 5.0067901611328125e-06, "duration": 0.10978507995605469, "duration_str": "110ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45391272, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET event/get_event_data", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\EventController@get_event_data", "namespace": null, "prefix": "", "where": [], "as": "event.get_event_data", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FEventController.php&line=317\" onclick=\"\">app/Http/Controllers/EventController.php:317-345</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.00613, "accumulated_duration_str": "6.13ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 16 limit 1", "type": "query", "params": [], "bindings": ["16"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.554606, "duration": 0.0041600000000000005, "duration_str": "4.16ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 67.863}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.5769591, "duration": 0.0008399999999999999, "duration_str": "840μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 67.863, "width_percent": 13.703}, {"sql": "select * from `events` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/EventController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\EventController.php", "line": 327}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.583912, "duration": 0.00113, "duration_str": "1.13ms", "memory": 0, "memory_str": null, "filename": "EventController.php:327", "source": "app/Http/Controllers/EventController.php:327", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FEventController.php&line=327", "ajax": false, "filename": "EventController.php", "line": "327"}, "connection": "ty", "start_percent": 81.566, "width_percent": 18.434}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "iD3eOztlFQTk9Wu93brk2PH5kNLllwdWSLzbHLjf", "_previous": "array:1 [\n  \"url\" => \"http://localhost:8000/account-dashboard\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "16"}, "request": {"path_info": "/event/get_event_data", "status_code": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">iD3eOztlFQTk9Wu93brk2PH5kNLllwdWSLzbHLjf</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-332792101 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">http://localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"39 characters\">http://localhost:8000/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1938 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=17ceeen%7C2%7Cfwk%7C0%7C1960; XSRF-TOKEN=eyJpdiI6Ikg5ZCtmNW1uZHFMS1VtU2VEWkRLM2c9PSIsInZhbHVlIjoiQlhTVkFPT29Ncm5McnI3UVkwUDNUNWJZaVBrd3RDS0tObkJucGNoT0twdU9vRnpGNUVZK042WS9tWHkwczVnWWwrNitLZ2VnTVU4bXhtZm5Oc1dvYnIrd2dEOVB0aVRlejkvM1daMmxzQWprbTUrdHVYZjgyNW02dzZEVG5Cd2k2a2NpYkRjbnJrQ3o1cHViV0Rhd2J0eDAvSlJ2UklBRXdOUy9CUGxNa2tMRk9Yc0RIZGU1MDZmYWpkOE1yL3JmUFNlSGZldWl6SUJNZlUrZG9rK0dqb3VyU3ZEWVk4N2gzdFcrM1lsM21nTGJNcHF4T2g0T1JybTN1R0JNQURCbDdYR3c3bUNQVVpoRnhpZ3JYYjBwd2haWU9YMm5ickFGTFpWMStHZGt3T1NLbE5WSHlObzB3RFVhSTl6bGF6S2NTU1JFUENwTkVUUml3aUhUTUFYWmRKTkViMEd5S2F4RjJIRXpjc1hqaWFtZVJDcTJ2aC9rNFg1VTVRazZKaDJ1N1ZmZzNpSjhhMmlTc09hUjdqcm15NmkvNWcvQVdQdllZaGhsUTNZTXM5blR0Z0tzYTJCQzRxSXljTy9WZnpNc3lkbWdITWZtWkZvNmNJM1g4Y08wVU9ocXc0TzduQWg5MWF2ME5KRGNaSzVWaTRlR1RIK2RKMk00YWVaWDk3NnUiLCJtYWMiOiIxODBkYjI4NzFmYTYxNTlkMTE3NDFmODMyMDY0NGE1MTU2MTlhOTQzNzE1MmNiZjllOWRhMGQ2MWM0NmExYjdkIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6IjlheHlLNm5BNFU1RTJxd0JYWmJhemc9PSIsInZhbHVlIjoiQ2l2VWlQeFFGYUc2ZElOcUhrQ3g4NU9HbDBKSndZTzRsTWNvM3hEbTR6SmZtTmlTWm9mYTJ0SXVjejlZS01FNTltd25VMG1ESXRaTWx5QkN5N1BOajhneVYwY2M2eFl5MUM4OVBzTm5vai9KaU1vUDRnVVVxY2hJMGFURlRPN0F4RDQ1bjI2RnUyR1k0dFdEV0JaNzEwR1N5SzZ3UThTUmNHR1A5YmxEaDB3NW9idkRnbm92T0o4UTNCV05lNlhPSzgwVlk0YnlkcDRSa1B5OVdJTlVyVXhxaVp1SERiWitJYnJ3SnBTSWFKWTdPcC9UbFFvVnZGb1hvblNvMkFrZkQ4REFXc0dsbXJvaHYyNmRmWktZd1NPL2w3MUhScXo5ZFpuMkxyQWVnYzJZOWlIS2xQTFRPR2RpRTBTRkFqSk51TDdWRnpKdnhHUGpTUE1RNjJFM3RYcFVrMWFvTkFmMVduYkd4YURadGxnbk5OdkNreTFvMmxmOHlXRWVDMmVsSjltNXUrZ1JsMktLQkgzT2dmZmlVbGk5dnRkRXEzL0VzbW4rL3YzTWNHOGZFVVhreXNjQ1RtMXphVks3NUZXY1k1bzNsandpVkw0cVRpbHVjOHN2WTVpZnpiVXdDZzY3NktmWUtiMDFrMEE5ZzhESFpIdVhGYXpVNzlET0krZnIiLCJtYWMiOiI2MDZiOWJiYzczZjljN2UwNDcwZDg0OTA0NmQyOGJjYzRhZjk0MTRmYjU5ZjExYWQ4YzQ5NzZkOGNkYTVhYjMxIiwidGFnIjoiIn0%3D; _clsk=1yftgw7%7C1749335947329%7C7%7C1%7Ck.clarity.ms%2Fcollect</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-332792101\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-727183619 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">iD3eOztlFQTk9Wu93brk2PH5kNLllwdWSLzbHLjf</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">atMfj5qpj5gKx4OQYPP5PbQzRbuF7iB8X4zv4aOY</span>\"\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-727183619\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-56388930 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 07 Jun 2025 22:39:08 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Imc5RmwvL0VuSldRaVJWUDhpVCs2U2c9PSIsInZhbHVlIjoieU5WYzFCWGl4bUpVY1E5UGJWeXRXRUgzZW5ieE1SWlk3YVlXVlM3cGNxVWMwNEZVZHc4MlEvaU0rVmhESDdUQkJyakJWRXgxOHp2RHI4NWgxUzZDSnNJVjd5ZDdJT2NSR2xyZG5vZElkMVVMK2lCcXRhMmhaZmVkL0VLU1NJVk9TZmJuVE1qdzlDYld6U0FpWithaW5TNUpnTDdnUSt5VWJqamwyY01KMjhRdkppb0lwaDQ4SEY3K1kxTWJXMnE5MnFJeWJXL3NzSzJmVkZhcTBHNDc4a1A3ak9vWTBDTE1oZ2ZhTVhIZ0FxcVVKc2ZldWI3R0I0THl2a252YWVtMStnck1PZ3Q3ZTY4bk5BUTJsbXcrOUR5cWdwak83ZVpJbk16elM5TGgxRGVMVkl3M3dzSmpCQzJ5WlowUkhQcEYwS2ZlNmhGV1NEVzJvRnl0bkpaSWFTTU5mKzdkQlNqbHY3dWVtUTBEck85bGc1VUErNTFkdnQwL0ZJdTRJSmQ3ZU9UbzB4czBGcGtiSHRKTWJkSCtQWUNuT0xrU0xpUXhwbEYrcFI1QWNrY3J2OXpRelZ4bXUzdE9rT2tkQXV3Z1BEYUpiN3AzTjNTdHlqa21idTlJdkY1V09NRU5SdHNpNXM0OEtVaDh0TEU2Z3hrNWU5cjJmeUZKWTl5RXRlNFoiLCJtYWMiOiI2ZmMwN2I5ZGU3ZWM5YjM0ZTNlZDc4MjdhZWU0N2MzZjU3YzQxMmFkNWQ5MTQ4MTJhMmJkNjQ0YTExMDE0Y2ViIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 00:39:08 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6IlhPNGhqSmRLZklVR2N2a0NkS3lXaGc9PSIsInZhbHVlIjoiaE9vejgxajVnOHM5MVJVb0xsSUdqbkRDNlFIdG1Dc3haTU5nOHovN24vekR5WDRwNzRKb21PNzJ2ZGQva2doTnE4RzM5dEpZVlVUZTd3OGNVQkhydGdtcnB5RXZHRVZScWlQTnNoTG5Lb1N2WFQxWURCUmpyVDRWbHB6Q1dSTWJGZm9aM3M4R2ZRWTV6NWplT2o1OGduMUpHWktqOVhla0xKZ0ZnQ2wwQkw2WDJwSUFqSkNac2hVVjQ3QXluQ3FUSGpQa0x4R0pjZ2VweWJrUWt3aGhMZVB5dXg0MEM4YWRlZzVoU0FjaktYOXVEcUJCYkdDblIwVm5zejFFMGl5V1ZWRkVRVzRvREdra1gwQnJEZ0syTG82cEdFQWJxODVNZW1UVjd0V1BnNlY2TzdxZm4xTXZEdzlLSUVDb1FaQmk3ZS8rWExCMWJSYkt4ait4bndmaGIra0NIOXpIdEtzQ3pSYmtlU2dZbmhHVEhrRGZMRVVRMW5CV2Z3d2RpTGU3eVJiNkRZbC9FM1JWV01abHNjcytWdzlNQXYvTGUxUmZyNEk0U21EZkJ2QWJmU0Z0VVlDaUFBOHpnUVltWkdwZEkzYnVUSEJGTzJlNVRNeUtvYmlzMUhCSDhKT1hoRitHQjRvUHVUVnZoVm5NYW9ZcHcvd0p6KytrSnRjUFFDSVQiLCJtYWMiOiJhZmNlMGE5N2I2MmI3ZWY3N2M4NTJjNGQwN2RiNGEzNjNkNzUwM2VmMTRkZmRjOTY5YTkxZGQ5YmIwOTVjOGE4IiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 00:39:08 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Imc5RmwvL0VuSldRaVJWUDhpVCs2U2c9PSIsInZhbHVlIjoieU5WYzFCWGl4bUpVY1E5UGJWeXRXRUgzZW5ieE1SWlk3YVlXVlM3cGNxVWMwNEZVZHc4MlEvaU0rVmhESDdUQkJyakJWRXgxOHp2RHI4NWgxUzZDSnNJVjd5ZDdJT2NSR2xyZG5vZElkMVVMK2lCcXRhMmhaZmVkL0VLU1NJVk9TZmJuVE1qdzlDYld6U0FpWithaW5TNUpnTDdnUSt5VWJqamwyY01KMjhRdkppb0lwaDQ4SEY3K1kxTWJXMnE5MnFJeWJXL3NzSzJmVkZhcTBHNDc4a1A3ak9vWTBDTE1oZ2ZhTVhIZ0FxcVVKc2ZldWI3R0I0THl2a252YWVtMStnck1PZ3Q3ZTY4bk5BUTJsbXcrOUR5cWdwak83ZVpJbk16elM5TGgxRGVMVkl3M3dzSmpCQzJ5WlowUkhQcEYwS2ZlNmhGV1NEVzJvRnl0bkpaSWFTTU5mKzdkQlNqbHY3dWVtUTBEck85bGc1VUErNTFkdnQwL0ZJdTRJSmQ3ZU9UbzB4czBGcGtiSHRKTWJkSCtQWUNuT0xrU0xpUXhwbEYrcFI1QWNrY3J2OXpRelZ4bXUzdE9rT2tkQXV3Z1BEYUpiN3AzTjNTdHlqa21idTlJdkY1V09NRU5SdHNpNXM0OEtVaDh0TEU2Z3hrNWU5cjJmeUZKWTl5RXRlNFoiLCJtYWMiOiI2ZmMwN2I5ZGU3ZWM5YjM0ZTNlZDc4MjdhZWU0N2MzZjU3YzQxMmFkNWQ5MTQ4MTJhMmJkNjQ0YTExMDE0Y2ViIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 00:39:08 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6IlhPNGhqSmRLZklVR2N2a0NkS3lXaGc9PSIsInZhbHVlIjoiaE9vejgxajVnOHM5MVJVb0xsSUdqbkRDNlFIdG1Dc3haTU5nOHovN24vekR5WDRwNzRKb21PNzJ2ZGQva2doTnE4RzM5dEpZVlVUZTd3OGNVQkhydGdtcnB5RXZHRVZScWlQTnNoTG5Lb1N2WFQxWURCUmpyVDRWbHB6Q1dSTWJGZm9aM3M4R2ZRWTV6NWplT2o1OGduMUpHWktqOVhla0xKZ0ZnQ2wwQkw2WDJwSUFqSkNac2hVVjQ3QXluQ3FUSGpQa0x4R0pjZ2VweWJrUWt3aGhMZVB5dXg0MEM4YWRlZzVoU0FjaktYOXVEcUJCYkdDblIwVm5zejFFMGl5V1ZWRkVRVzRvREdra1gwQnJEZ0syTG82cEdFQWJxODVNZW1UVjd0V1BnNlY2TzdxZm4xTXZEdzlLSUVDb1FaQmk3ZS8rWExCMWJSYkt4ait4bndmaGIra0NIOXpIdEtzQ3pSYmtlU2dZbmhHVEhrRGZMRVVRMW5CV2Z3d2RpTGU3eVJiNkRZbC9FM1JWV01abHNjcytWdzlNQXYvTGUxUmZyNEk0U21EZkJ2QWJmU0Z0VVlDaUFBOHpnUVltWkdwZEkzYnVUSEJGTzJlNVRNeUtvYmlzMUhCSDhKT1hoRitHQjRvUHVUVnZoVm5NYW9ZcHcvd0p6KytrSnRjUFFDSVQiLCJtYWMiOiJhZmNlMGE5N2I2MmI3ZWY3N2M4NTJjNGQwN2RiNGEzNjNkNzUwM2VmMTRkZmRjOTY5YTkxZGQ5YmIwOTVjOGE4IiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 00:39:08 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-56388930\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">iD3eOztlFQTk9Wu93brk2PH5kNLllwdWSLzbHLjf</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"39 characters\">http://localhost:8000/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>16</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n"}}