{"__meta": {"id": "Xa289725b9e1229d6073cab373592cef6", "datetime": "2025-06-07 22:38:02", "utime": **********.207251, "method": "POST", "uri": "/event/get_event_data", "ip": "127.0.0.1"}, "php": {"version": "8.3.16", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749335881.12623, "end": **********.207283, "duration": 1.0810530185699463, "duration_str": "1.08s", "measures": [{"label": "Booting", "start": 1749335881.12623, "relative_start": 0, "end": **********.074018, "relative_end": **********.074018, "duration": 0.9477880001068115, "duration_str": "948ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.074037, "relative_start": 0.9478070735931396, "end": **********.207286, "relative_end": 2.86102294921875e-06, "duration": 0.13324880599975586, "duration_str": "133ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45390968, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET event/get_event_data", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\EventController@get_event_data", "namespace": null, "prefix": "", "where": [], "as": "event.get_event_data", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FEventController.php&line=317\" onclick=\"\">app/Http/Controllers/EventController.php:317-345</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.00678, "accumulated_duration_str": "6.78ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 16 limit 1", "type": "query", "params": [], "bindings": ["16"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.1595309, "duration": 0.0049299999999999995, "duration_str": "4.93ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 72.714}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.184143, "duration": 0.00086, "duration_str": "860μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 72.714, "width_percent": 12.684}, {"sql": "select * from `events` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/EventController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\EventController.php", "line": 327}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.190772, "duration": 0.00099, "duration_str": "990μs", "memory": 0, "memory_str": null, "filename": "EventController.php:327", "source": "app/Http/Controllers/EventController.php:327", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FEventController.php&line=327", "ajax": false, "filename": "EventController.php", "line": "327"}, "connection": "ty", "start_percent": 85.398, "width_percent": 14.602}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "iD3eOztlFQTk9Wu93brk2PH5kNLllwdWSLzbHLjf", "_previous": "array:1 [\n  \"url\" => \"http://localhost:8000/account-dashboard\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "16"}, "request": {"path_info": "/event/get_event_data", "status_code": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">iD3eOztlFQTk9Wu93brk2PH5kNLllwdWSLzbHLjf</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1289148540 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">http://localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"39 characters\">http://localhost:8000/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1938 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=17ceeen%7C2%7Cfwk%7C0%7C1960; XSRF-TOKEN=eyJpdiI6ImFRd09WL2pFYW92RDZaSGsxNWI4WlE9PSIsInZhbHVlIjoiTmZNSHRkR1BIbFVPMi96dGVubmo4ZzE4WVFMSkJwWjhBMEtBOFBHMVNVZlVBSG1MT2x4N1loRlYycE5SN0dFd3MvWFIyblFXOHB6cFhoelYxT0t6eVRKanpIdkFLYi9kNmRiZjdRNVZzd0I0cGx6RjBmKzhpMDZHREF1aGNBWWkzRFc4WUFocFF2cVlsb0VZbFFXZkpDT3Fhb3FNTE14dDBZZ3l3ZnlsRWI4REtSREg5YlZrTEl4bW01YnpyYVA0eWdWUmlpL29NNXp0Q2wwUDMyc3NmUHYyRDlrNWpnb29DM1VGeUZRRzdvUllOVVhza1lyQUpreGhTUm1Hb3NDYnA4d3p0cjhMc0tKTmE0bE0wOVkyd096MnJoMk4vUE0zRXZHWVhjYWhoamRwSTZHQUdMaS9ab0RPbW0yMzNPVGtYS3M0OVpFTFdwYVl6U2UvWDJsdDRHTE5DdCtIMGE4Qm1HUENVU1ZWbFpFYWM2SHcwY01jbE50NVAwdUFiU3hpcitmL0JYOVBEMkZkNWJzbmxOcVMyYzcrM0FzWlhlLzNuQ2g1OVJXOEVOYWVsZ0VtZUJHRFNXSDd2bnhWdDFkN041TThQaDM4U3VqU1R4eWJNay94WitSamphRjJsOVNjOG5ac0ttUEFhNkp2Z0lYNUIxQWtFNTgwbzh3WWdvRi8iLCJtYWMiOiJkNzI1ZWNmZTkzN2M1YWFhMWJiNDY4OGRkZGFhOWYwZWNiNmMwN2FhZTc5ZmMxYmEwMjgzNTFhZThkZGI4MzQ2IiwidGFnIjoiIn0%3D; test_session=eyJpdiI6ImdLc0RydXRmaVpreGF4WGhpYlpZU1E9PSIsInZhbHVlIjoiaGZOSTNpM2RhTGJGTTdvMWpld2ZiQzBBbVhYZ2RSSkNHK1duRDBObElzSFdXaWdyVHVKaEkvUXp4b0VjcDg2R09EMjJldTFmbGtFdWsxWHg4ZWMwUGg5dk5YNkdOb0lBMUt4ZFYrai9Xa1BTVHlWeVFmYW9VV2tTQWVncjNoZ2lvQ2JZcys1VW1QQVNrdUlCTUdXMVVOSFJVeFFOM2hDc09XcGdxWkJtWENVQy9LSG13Nkk1Z3FuT3NudExvb1dDRmppSWFYMjNPSU5XZ1M0S2tPOTdSeGpQYTJLM25aNnNncUpMRzRrK3pzUmFxb3F3eTRjbmtJWko0TWlPaldzZm5QVWlrek5ld2xpWmhvRENPS1NlUlJyUzJ5ODRMZUJrY3hSZ2NXU0ZnK0w0RExDQk5nQ1JQbi94bjVkU2N5b2U1VG1CUlM4RGwwQUlqc0VNVXdha0twakpKbGFTSDZJcjBmZlQ4S2ZsRTFjUjVjNUxpUWxlRTNiK3J0UHljV3BTY1dQZmowUEJuYTA2dEhONFUvalZlZVY2elNweDZScUN5TTZwYURtUjMxdTFtM2dwWE00dnVxaEUvR2tKZGplenBhU29MckNid0srSU9hVG1XbC9YRlA0Z0xpS0ozRFdzUjlGV25VRjZrYUtmTENXVFR5U2pEUXdjeGtNd0l6N28iLCJtYWMiOiJkZGU3MTU0NzI1NGM3NjY3NjBjN2NmOGRhYjY3M2U1MWI1MmI2OWNkYjRkN2VjNGZjZDk5NmUxMGZjMzg5ZWRjIiwidGFnIjoiIn0%3D; _clsk=1yftgw7%7C1749335881083%7C6%7C1%7Ck.clarity.ms%2Fcollect</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1289148540\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-520395727 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">iD3eOztlFQTk9Wu93brk2PH5kNLllwdWSLzbHLjf</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">atMfj5qpj5gKx4OQYPP5PbQzRbuF7iB8X4zv4aOY</span>\"\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-520395727\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 07 Jun 2025 22:38:02 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6ImZNZGpPYklucWFtODZmZFhCcTVINnc9PSIsInZhbHVlIjoiUHZOVnpvdlhpVjJtWnBvdWU1WGVUMW1UdHJneXBLYXhlYlZVYnhpckF2UEtnZ1Q4TWVqc1lyc0oyZk9OTnk0YjUzclp5Nit6Tythdmk3SkozS3hXc2pkTEFEWGR6UVBXYm1WZ2o1dTkrendSdGJFSHEwQUNaWHBqc1I2TkdnRm9UYjI1U09KSkswZWtMTzRrUzd2Y01kd3FRVC9nb3puNThZbzk2NTJsM0kvUy9DOVJIR2tPN0llNENocWJzaEFkOERJN1VvMmtmVjZTV0IzVVRycGRvRm5Ddk1VSFVNVkh2SnF5V09kOU5LNzFMZHJySlFEUE1kcGpRRldieWpNMS8yY0dzSWhXMkxqTVVnUDJjK1gxYStrM21LQWxwRm9WYm92Tldyc25lbC91czNWZ0hLUldQeDJGSFBiMFJXTGFQdUtnS1ptL3h0Nld1Rk5FVDlyeWpkb0xzUkZMTk5aeGd1VjF4TnA3YjhsZXg2VWJUNElxT1YxaDFWNmQ1ZmFwTHlnZi8xSWZ5d3Nhc283bTdvcmt3ejM4T1czUUJtcEJWdzF0MkI4dXAwV0taUGFaOWx3d3VRcE44U1FyQXZHWnEwaVA2bzd0ekRqU0JWRkIzS3NITXVpU1Z5MFd2alRQTXV6akgweG5OMVFuNFJUTERaR3pRQnArZmRHTkFNbkEiLCJtYWMiOiI4OWVkYzk1NDJkMTFjNmUxNTU3YjM1M2I3MjhiNjQ5ODc2ZmZlOTIwYjY0ODBhMTE1MWNiYjY4MzkyODAzZmVlIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 00:38:02 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6ImhBK0Q0MkdoWWVqV2QxUVF0VnZaWEE9PSIsInZhbHVlIjoiakwramVDbHhQSlhCNEppU3dveFpLTUNHQkhIY0M1L2xJU05BclpRSHlxSWtscFFMYTJSUUlQZkJLbTM4cjB4WlNSM3o4YUU2aHBFdVJZRU5lTWtTams5UWNQSlBHUUkzTFppbnhxK2FHRTJjNzYweW9HeFMwQnREVWdOS2d0bXlWS1BmbEVsUkVoRitqTmJvRjlzTTJRUENXNGV4QnJWc3NmT2FaZlpqd2RydUx6dnZUSTdEeFl0ekV1Wjc3YTJLTG8rRzNxWUZYeUoyWEkwSlFkR1lqWno3R2tEdGltTFkxc2wzbmhzdVVZeUdKOHl3VEI3a0N5MjMwNlFoaDY1Y2FyOUlkc2twZmlka1o3NlRVQTBMUXFjZ05IS3RBTW9DSm5YL1V1R29jWTFIYjVHQi82VW5kVHJ3Tzdsanc1blc0SnBWZytNZWpvQjd0NWNSanZaSlFCVkR2NFJoalF1NnhuMXhwMUgyUlN0WTFhRUN0ZkFaRFVLbE1UamFNSmZ1NGRyQk9lSFVvTWh1eWpRQ2UzNHBVZHRuVnBwMCtiVTJ5NGVZQjJibWdtTW1tUHJpVEJZcGRyK1cwWENLL1MxWGEzeEVaTHMvVUZrL2cvcHlUQ0pvMmFqKzRjQzZsd1luWWJucFk5YTNkZlBBT24vUkVyaG5lcFZMRUVyMjRYSjQiLCJtYWMiOiI2MTVlOWMwMDNjMjQ3NDJhYWIwY2JkYzJmNDM2N2UyY2VkYjhlOWY4NGViNDdiMjk1MDMxYTE1ZWFhOGI0ZjFjIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 00:38:02 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6ImZNZGpPYklucWFtODZmZFhCcTVINnc9PSIsInZhbHVlIjoiUHZOVnpvdlhpVjJtWnBvdWU1WGVUMW1UdHJneXBLYXhlYlZVYnhpckF2UEtnZ1Q4TWVqc1lyc0oyZk9OTnk0YjUzclp5Nit6Tythdmk3SkozS3hXc2pkTEFEWGR6UVBXYm1WZ2o1dTkrendSdGJFSHEwQUNaWHBqc1I2TkdnRm9UYjI1U09KSkswZWtMTzRrUzd2Y01kd3FRVC9nb3puNThZbzk2NTJsM0kvUy9DOVJIR2tPN0llNENocWJzaEFkOERJN1VvMmtmVjZTV0IzVVRycGRvRm5Ddk1VSFVNVkh2SnF5V09kOU5LNzFMZHJySlFEUE1kcGpRRldieWpNMS8yY0dzSWhXMkxqTVVnUDJjK1gxYStrM21LQWxwRm9WYm92Tldyc25lbC91czNWZ0hLUldQeDJGSFBiMFJXTGFQdUtnS1ptL3h0Nld1Rk5FVDlyeWpkb0xzUkZMTk5aeGd1VjF4TnA3YjhsZXg2VWJUNElxT1YxaDFWNmQ1ZmFwTHlnZi8xSWZ5d3Nhc283bTdvcmt3ejM4T1czUUJtcEJWdzF0MkI4dXAwV0taUGFaOWx3d3VRcE44U1FyQXZHWnEwaVA2bzd0ekRqU0JWRkIzS3NITXVpU1Z5MFd2alRQTXV6akgweG5OMVFuNFJUTERaR3pRQnArZmRHTkFNbkEiLCJtYWMiOiI4OWVkYzk1NDJkMTFjNmUxNTU3YjM1M2I3MjhiNjQ5ODc2ZmZlOTIwYjY0ODBhMTE1MWNiYjY4MzkyODAzZmVlIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 00:38:02 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6ImhBK0Q0MkdoWWVqV2QxUVF0VnZaWEE9PSIsInZhbHVlIjoiakwramVDbHhQSlhCNEppU3dveFpLTUNHQkhIY0M1L2xJU05BclpRSHlxSWtscFFMYTJSUUlQZkJLbTM4cjB4WlNSM3o4YUU2aHBFdVJZRU5lTWtTams5UWNQSlBHUUkzTFppbnhxK2FHRTJjNzYweW9HeFMwQnREVWdOS2d0bXlWS1BmbEVsUkVoRitqTmJvRjlzTTJRUENXNGV4QnJWc3NmT2FaZlpqd2RydUx6dnZUSTdEeFl0ekV1Wjc3YTJLTG8rRzNxWUZYeUoyWEkwSlFkR1lqWno3R2tEdGltTFkxc2wzbmhzdVVZeUdKOHl3VEI3a0N5MjMwNlFoaDY1Y2FyOUlkc2twZmlka1o3NlRVQTBMUXFjZ05IS3RBTW9DSm5YL1V1R29jWTFIYjVHQi82VW5kVHJ3Tzdsanc1blc0SnBWZytNZWpvQjd0NWNSanZaSlFCVkR2NFJoalF1NnhuMXhwMUgyUlN0WTFhRUN0ZkFaRFVLbE1UamFNSmZ1NGRyQk9lSFVvTWh1eWpRQ2UzNHBVZHRuVnBwMCtiVTJ5NGVZQjJibWdtTW1tUHJpVEJZcGRyK1cwWENLL1MxWGEzeEVaTHMvVUZrL2cvcHlUQ0pvMmFqKzRjQzZsd1luWWJucFk5YTNkZlBBT24vUkVyaG5lcFZMRUVyMjRYSjQiLCJtYWMiOiI2MTVlOWMwMDNjMjQ3NDJhYWIwY2JkYzJmNDM2N2UyY2VkYjhlOWY4NGViNDdiMjk1MDMxYTE1ZWFhOGI0ZjFjIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 00:38:02 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">iD3eOztlFQTk9Wu93brk2PH5kNLllwdWSLzbHLjf</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"39 characters\">http://localhost:8000/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>16</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n"}}