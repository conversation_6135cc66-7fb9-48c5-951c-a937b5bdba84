{"__meta": {"id": "X9417a17214687a07ad03ff3e395f346f", "datetime": "2025-06-07 22:18:18", "utime": **********.794328, "method": "POST", "uri": "/chats/getContacts", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749334697.263834, "end": **********.794365, "duration": 1.5305309295654297, "duration_str": "1.53s", "measures": [{"label": "Booting", "start": 1749334697.263834, "relative_start": 0, "end": **********.565014, "relative_end": **********.565014, "duration": 1.3011798858642578, "duration_str": "1.3s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.565037, "relative_start": 1.3012030124664307, "end": **********.79437, "relative_end": 5.0067901611328125e-06, "duration": 0.22933292388916016, "duration_str": "229ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45083440, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/getContacts", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getContacts", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "contacts.get", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=333\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:333-390</a>"}, "queries": {"nb_statements": 4, "nb_failed_statements": 0, "accumulated_duration": 0.02044, "accumulated_duration_str": "20.44ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.660367, "duration": 0.015460000000000002, "duration_str": "15.46ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 75.636}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.7115471, "duration": 0.0016699999999999998, "duration_str": "1.67ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 75.636, "width_percent": 8.17}, {"sql": "select * from `migrations`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/rachidlaasri/laravel-installer/src/Helpers/MigrationsHelper.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\rachidlaasri\\laravel-installer\\src\\Helpers\\MigrationsHelper.php", "line": 29}, {"index": 14, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\XSS.php", "line": 40}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 16, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.754156, "duration": 0.00174, "duration_str": "1.74ms", "memory": 0, "memory_str": null, "filename": "MigrationsHelper.php:29", "source": "vendor/rachidlaasri/laravel-installer/src/Helpers/MigrationsHelper.php:29", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Frachidlaasri%2Flaravel-installer%2Fsrc%2FHelpers%2FMigrationsHelper.php&line=29", "ajax": false, "filename": "MigrationsHelper.php", "line": "29"}, "connection": "ty", "start_percent": 83.806, "width_percent": 8.513}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (1) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.772395, "duration": 0.00157, "duration_str": "1.57ms", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 92.319, "width_percent": 7.681}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "cN6pwMHluqMWM9hLaRtOtRasnQF8pfN3ghxjsOfa", "_previous": "array:1 [\n  \"url\" => \"http://localhost/users\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "1", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/getContacts", "status_code": "<pre class=sf-dump id=sf-dump-1681117340 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1681117340\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-796204081 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-796204081\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-54406601 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">cN6pwMHluqMWM9hLaRtOtRasnQF8pfN3ghxjsOfa</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-54406601\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1602994802 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/users</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1993 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=a091af98-270d-406e-ab7e-33a6e610d6e2414686; _clck=nups8l%7C2%7Cfwk%7C0%7C1960; _clsk=ij6lzq%7C1749334686693%7C3%7C1%7Ck.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IkwweWhCZW9SeitaQ2N4V1owZGsvbnc9PSIsInZhbHVlIjoiZldpRGFwQ3RDZGtEV2F5ZXozVDRFRGg4aGFRSlI5OFBvam10Qk45bzFPT3hTZTQ5VlN1SjdOdWwvZ1lMSlpzQUg4WU1tZ3dFam91VGpkTENTQmxqUGJlQ0I0emdtaFJpMmFPajBmTG9FQzdkS2dBRTQ5UmNhUWZoT0dmVXlodWN4TGh4UUZaSC9GM00rUmtxc1Y3MndydUpyQnFnd2ttcXlMbElWQWtXNHhlcHVyYlkwVXBEU0kyeXVPRmNvUXdwRkltdTh2anZ3RTk5TUFLYXIxVUpYblFyOU9vRmk1Tm9WdDdUZGMxS2tWUkJodzNwQmJXcDdScG9PYjd5TUJ3WTh5S0FDK3ltUE41aDRFWjBYZDdENUkzTFpYVStPQ1dUOEtTeTNhbngxMEJab0pZQ2JDRjlTd3FCVE5CV0RIa3RMbUJyV0MzaWxmMVllaDNlZWdUTS9IMDAyekx2dFNRUlAvN1BTbXpzU3k5azYvNWYreGhyakZJR0krd3RBTDZ2Y1ZMcW9abjkxbGhRdTljTVVnK0xWR1dYUEhRNnVXSzZnUjN3N1pMYjRwRG4zR2gwMEltRERjaEczSmZNdlZMdVZ6Z1VkWHVicmVzYXc2bHhhM3RMeXNQb3NpNjlTSjJVUzVCNm5UUlpBYXg1Qlg2WEI4WnFvNlpqR3lTVkJmN20iLCJtYWMiOiI2ODY3OGNjZGI2YTUzNzIyMDMzMDlmYjgyZTc4N2FkZGY5NzA5YzVlMmUxZjA5ZGJjOGE5Yjk4ZGIzMmZiYjQ3IiwidGFnIjoiIn0%3D; test_session=eyJpdiI6IlUwU2hYc1VGaVp6eHZRY1pkdE9hL0E9PSIsInZhbHVlIjoiNUViSkNhbUt2YWhXdjJZekxiUUdqV3RLdzBzR05NbnMzdkhpZ2RuTmUrR05LbjUxbENPUHpkZ3h6R1hzM3kxM3FaZXh0dTVpcG1LbVpGT3F5K3N5YkFUdTRiS3c0V21CdkszQ29sMHhIRHhWdVkzMHZ0aDZSeU1IY0duVkJOZm9GWWFVYzE2Q1Y2TUxQQ0RleTBYTEZPaHh5eEpjek9hSm91bGdkblcyNWlOZG9Ub1B3YVcwbE50OEtYMTdQcXFQRVp4eUpWNFlOejY5eDlIajNKMlBMTmxCei9OY0JCVFg5MHVnRW9BWmJiUlpZbVJwMFVraEdWMlgrdFA4Lzd1L202eGMxRUZFQk9hTllkeGtXQ3FxTnhJQjJJc2pmWU9oUlJVZHp0L1A2YmZEeUR6LzRTQ1F1bmZhNGpGUzFxWGtTUkFac1FBVHVLWFhDbVdPZFBKVGNWL3ZsN1ZSaGhRTDRKbWhYOGFob1IzMXBveVh3MVNlL0hyQkJrcnhwT2UzVzVUcUhXV1VWWm9FSW1VUzB2YVNvbTJGekdYQzVKVzk3YnBHYzkvdmltdnoxUHF6cFNHVGJSaHZGc2sweW8yWjF4c0ZCVXdHNHY0RldrUnNzMUF3dnhxZ1FpTHpScTh0eUZMTDcwVjRyNjIvQStXMEpCRVNDNmZlN3J0dnZ4WGQiLCJtYWMiOiI0YWJkNzUzNGIwMjg1NWM0M2UwNjBlNGRkNWYxZTdkOGU5OTcyOTNhOTNmN2QzYWM4OWQ5MDliNDIwNzVjNDkzIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1602994802\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1051477554 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">cN6pwMHluqMWM9hLaRtOtRasnQF8pfN3ghxjsOfa</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">PmFRgUqysTWH0qB2ROlhITKoLIETZGW2KykVXtOx</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1051477554\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1675868147 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 07 Jun 2025 22:18:18 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IkdRVjZSOENjY3dJUnJyNEZDMDE1amc9PSIsInZhbHVlIjoidmdVaXdYR3pwMUhnRElOcEVTa0JTdHZtUHU3SVhpNnBNeTRBWTFnV1BsYTEwTlVRVEJqRWxaVlM2UjZPZzFSaWdFNmlDdnhhb1dKRDZFM0d0aHc1Y0xBemtEbGVpU1pkNTBKQkJGYUYzRklpUEFVWkZSY1htNENVRVJQbE5xQThpUmpUTVVLanNMUXZwNU5vcGJ5bVN4VmdUaFJVSDBldWhQbVZIOThpVWpwdEE1eFkyeFp4Uk1TL2tpOHVWK0tiZlNqS1NaU2J2RElzZTFSQzBHclpNZFJ2aGZpMTN6Y0NUQ3pDWTBCdEhSSzc2RjI0NEU3MDJPL3ptSVVSZytFMFpGNklSWWMwQmk3SUZBZWNqODQ1V3ZUcSsxck1XWmlDWTR4Nkl5VGxHamx1a2dpbjRkMHVaV0pVTU51ZW9YRktiTG8wcFUzY3N4UVlnU2pPNGpFWUl0eHNvaWpneTV5aVhjOXZBRWxRWDhqcWlIVzRnV0pNSDdyWVB1Z0ZXa3M3QWhVT0w1bEZPQXh6dmpUYnZmbWx6N1l3ME5lNzZROUd0c3h5Y2o0cGtEVXZJMjhLZXlqUElXOTU5b1E2NUxSRGNJeU0zUksrVUFjdTNqTVFJRDVvUm00R1FwZW9DQ045TjU1b0IzVVQ5TkFBTzJkcC96VEpLUHI5MzAwc2dwVWYiLCJtYWMiOiIxZGYyNmRhM2UxMzgwNGMxNjA5YThmMDUwODNlNGQ1MmVjYjM0MmUwODk2MWM5MTJlOTQzZjAzMzgyNjI4NTJkIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 00:18:18 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6IkZNSmhZUnpxUmg2V3ZsdU5RV0o0NWc9PSIsInZhbHVlIjoiZFNIZjJReGZmU0VaeG5KV04yK1hJSmVpZ3d1NFVud0IxM2pUUDVhcjM1UnBtbjR2dit1MHlyN0FvYWlkNDRsZk91NFVzaDdRQVN2SThTV0VpSStmbTQ1eFZIaU1nZC9RRHpCcTNqcGlMN25Od3hOUHduUTFnNmJiMHhQbnhPS1o2ZE5iakpNZ0VQZnB2TUNrWGw0aXg3d1hUR05MeGR1WEtnOW52SFdFQzlVUWxVSm95RmJ5Q2NYVFg2VmptcU1vcHdZWFpmNlBUb3Y4RlZnVDQ4VUVLUzQ2d3BmaUVaak15SlZ6RTJUNDU4UjFyY282SlM3alUrTXU1OTh4Qmh6MUhsWlgzaFZibERqeFU1UUp1ZSs3TjNod0pScVExaWxqeDc2dWo5NDVNam9uajExMXdTN1YxM21ZdDlTQmIxcGRFaWxIUy8vTmVkSlJpZzdIaXQ4OWFxVjJFTEs1aUIxSGFYakhVSEd0Tmw4WUZFWDR1a0d3Q1VUdzZ2cmh6ZGRmN2xRSkpJeXZzazdybUdlckt0V1pqS1V6cTVDcXYyc0hBVVI2cDFJbGVDUEJsemV5LzVocUlKcXR5ZVJBaFhqVUlxbGJVL0ZQOGVoYUxVNjF6S3hGdW04ck9FVUc2M3BqL3FMWmptY3pwQVkyVXFYMGJORTZxWnFpSmQxNEc1MEEiLCJtYWMiOiI3MDVmMWViZDQwNDAzZTA3MzU2ZjlhMmYwNThlMTg4NTM4YzczNTdjMjU3YTU4NGUzYWZiNmEzMzU2NDYwOTNjIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 00:18:18 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IkdRVjZSOENjY3dJUnJyNEZDMDE1amc9PSIsInZhbHVlIjoidmdVaXdYR3pwMUhnRElOcEVTa0JTdHZtUHU3SVhpNnBNeTRBWTFnV1BsYTEwTlVRVEJqRWxaVlM2UjZPZzFSaWdFNmlDdnhhb1dKRDZFM0d0aHc1Y0xBemtEbGVpU1pkNTBKQkJGYUYzRklpUEFVWkZSY1htNENVRVJQbE5xQThpUmpUTVVLanNMUXZwNU5vcGJ5bVN4VmdUaFJVSDBldWhQbVZIOThpVWpwdEE1eFkyeFp4Uk1TL2tpOHVWK0tiZlNqS1NaU2J2RElzZTFSQzBHclpNZFJ2aGZpMTN6Y0NUQ3pDWTBCdEhSSzc2RjI0NEU3MDJPL3ptSVVSZytFMFpGNklSWWMwQmk3SUZBZWNqODQ1V3ZUcSsxck1XWmlDWTR4Nkl5VGxHamx1a2dpbjRkMHVaV0pVTU51ZW9YRktiTG8wcFUzY3N4UVlnU2pPNGpFWUl0eHNvaWpneTV5aVhjOXZBRWxRWDhqcWlIVzRnV0pNSDdyWVB1Z0ZXa3M3QWhVT0w1bEZPQXh6dmpUYnZmbWx6N1l3ME5lNzZROUd0c3h5Y2o0cGtEVXZJMjhLZXlqUElXOTU5b1E2NUxSRGNJeU0zUksrVUFjdTNqTVFJRDVvUm00R1FwZW9DQ045TjU1b0IzVVQ5TkFBTzJkcC96VEpLUHI5MzAwc2dwVWYiLCJtYWMiOiIxZGYyNmRhM2UxMzgwNGMxNjA5YThmMDUwODNlNGQ1MmVjYjM0MmUwODk2MWM5MTJlOTQzZjAzMzgyNjI4NTJkIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 00:18:18 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6IkZNSmhZUnpxUmg2V3ZsdU5RV0o0NWc9PSIsInZhbHVlIjoiZFNIZjJReGZmU0VaeG5KV04yK1hJSmVpZ3d1NFVud0IxM2pUUDVhcjM1UnBtbjR2dit1MHlyN0FvYWlkNDRsZk91NFVzaDdRQVN2SThTV0VpSStmbTQ1eFZIaU1nZC9RRHpCcTNqcGlMN25Od3hOUHduUTFnNmJiMHhQbnhPS1o2ZE5iakpNZ0VQZnB2TUNrWGw0aXg3d1hUR05MeGR1WEtnOW52SFdFQzlVUWxVSm95RmJ5Q2NYVFg2VmptcU1vcHdZWFpmNlBUb3Y4RlZnVDQ4VUVLUzQ2d3BmaUVaak15SlZ6RTJUNDU4UjFyY282SlM3alUrTXU1OTh4Qmh6MUhsWlgzaFZibERqeFU1UUp1ZSs3TjNod0pScVExaWxqeDc2dWo5NDVNam9uajExMXdTN1YxM21ZdDlTQmIxcGRFaWxIUy8vTmVkSlJpZzdIaXQ4OWFxVjJFTEs1aUIxSGFYakhVSEd0Tmw4WUZFWDR1a0d3Q1VUdzZ2cmh6ZGRmN2xRSkpJeXZzazdybUdlckt0V1pqS1V6cTVDcXYyc0hBVVI2cDFJbGVDUEJsemV5LzVocUlKcXR5ZVJBaFhqVUlxbGJVL0ZQOGVoYUxVNjF6S3hGdW04ck9FVUc2M3BqL3FMWmptY3pwQVkyVXFYMGJORTZxWnFpSmQxNEc1MEEiLCJtYWMiOiI3MDVmMWViZDQwNDAzZTA3MzU2ZjlhMmYwNThlMTg4NTM4YzczNTdjMjU3YTU4NGUzYWZiNmEzMzU2NDYwOTNjIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 00:18:18 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1675868147\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-69857755 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">cN6pwMHluqMWM9hLaRtOtRasnQF8pfN3ghxjsOfa</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/users</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-69857755\", {\"maxDepth\":0})</script>\n"}}